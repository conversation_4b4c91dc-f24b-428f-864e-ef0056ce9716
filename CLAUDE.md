# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Nebula is an enterprise document management and workflow system built with Go-Zero microservices framework. It handles internal/external documents, business dictionaries, digital signatures, file libraries, and approval workflows across multiple databases (NebulaDB, DocvaultDB, PhoenixDB).

## Development Commands

### Code Generation
```bash
# Generate API handlers, types, and routes from .api files
make apigo

# Format API specification files 
make apifor

# Generate gRPC client/server code from protobuf files
make grpcgo
```

### Build and Deploy
```bash
# Build for Linux AMD64
make build

# Build for Linux ARM64  
make build-arm

# Clean build artifacts
make clean

# Deploy to development environment (***************)
make deploy150
```

### Testing
```bash
# Run all tests
go test ./...

# Run tests for specific package
go test ./internal/logic/document_library/

# Run tests with verbose output
go test -v ./internal/logic/document_library/

# Run specific test function
go test -run TestGetDeprecateApplications ./internal/logic/document_library/

# Generate mocks for testing
go generate ./...
```

## Architecture Patterns

### Go-Zero Framework Structure
- **API Definition**: All REST endpoints defined in `/convention/*.api` files
- **Auto-Generation**: Handlers, types, and routes generated via `goctl api go`
- **Service Context**: Centralized dependency injection in `internal/svc/`
- **Middleware**: `TraceMiddleware` and `AuthMiddleware` applied to protected endpoints

### Domain-Driven Design (DDD)
```
internal/domain/
├── aggregate/     # Business logic aggregates (Signature, UserDemo, etc.)
├── entity/        # Domain entities  
├── factory/       # Domain service factories with dependency injection
└── value/         # Value objects
```

### Hexagonal Architecture (Ports & Adapters)
```
internal/
├── domain/              # Core business logic
├── logic/               # Application services
├── handler/             # HTTP request handlers
└── infrastructure/      # External adapters
    ├── adapter/
    │   ├── clientx/     # External service clients (Phoenix, Macrohard)
    │   ├── mapper/      # Database access layer (GORM)
    │   ├── kqs/         # Kafka producers/consumers
    │   └── addons/      # Utility adapters
    └── referenceimpl/   # Domain reference implementations
```

### Multi-Database Pattern
- **NebulaDB**: Core business data (deprecation records, business dictionary)
- **DocvaultDB**: Document storage and metadata
- **PhoenixDB**: User management and organization data

Each database has its own connection pool configuration and GORM client in the service context.

### Event-Driven Architecture with Kafka
- **Producers**: Data export, business dictionary changes
- **Consumers**: Workflow approvals, document operations
- **Topics**: Environment-prefixed (e.g., `sit_workflow_passed_SIGNATURE_APPROVAL`)

## Testing Conventions

### Framework and Patterns
- **Primary Framework**: GoConvey for BDD-style testing
- **Mock Generation**: `golang/mock` with `//go:generate` directives
- **Test Organization**: Separate files for different test types:
  - `*_test.go`: Standard unit tests
  - `*_complete_test.go`: Integration tests with database connections
  - `*_mock_test.go`: Mock-based unit tests
  - `*_table_test.go`: Table-driven tests

### Test Structure Example
```go
func TestFunction(t *testing.T) {
    convey.Convey("Test description", t, func() {
        // Setup
        // Test execution
        convey.So(actual, convey.ShouldEqual, expected)
    })
}
```

### RPC Testing Requirements
When testing gRPC services, always inject the session interceptor:
```go
zrpc.WithUnaryClientInterceptor(rpcinterceptor.SessionUnaryClientInterceptor)
```

## Database Optimization Patterns

### Batch Query Pattern
Avoid N+1 query problems by implementing batch operations:
```go
// Instead of querying in loops
for _, id := range ids {
    result := client.GetByID(ctx, id)
}

// Use batch queries
results := client.BatchGetByIDs(ctx, ids)
```

### Charset Handling for MySQL Joins
When joining tables with different charset collations, use explicit conversion:
```sql
CONVERT(table1.field USING utf8mb4) COLLATE utf8mb4_unicode_ci = 
CONVERT(table2.field USING utf8mb4) COLLATE utf8mb4_unicode_ci
```

## Configuration Management

### Environment Files
- **Development**: `etc/nebula.yaml`
- **Production**: External config center (Etcd) with local fallback
- **Multi-Environment**: Configuration includes environment prefixes for Kafka topics

### Key Configuration Sections
- **MicroServices**: External service URLs (Phoenix, Macrohard)
- **Databases**: Connection pools for NebulaDB, DocvaultDB, PhoenixDB
- **Kafka**: Brokers, producers, consumers with topic configuration
- **Business**: Domain-specific settings (e.g., signature authorization letter file ID)

## External Service Integration

### Phoenix Workflow System
- **Purpose**: User management and approval workflows
- **Integration**: HTTP client with workflow callbacks
- **URL**: Configured in MicroServices.phoenix.Url

### Docvault Document Storage
- **Purpose**: Document storage and retrieval
- **Integration**: gRPC client with protocol buffers
- **Endpoints**: Configured in DocvaultRPC.Endpoints

### Macrohard Document Processing
- **Purpose**: Document format conversion and processing
- **Integration**: HTTP client
- **URL**: Configured in MicroServices.macrohard.Url

## Code Quality Standards

### Database Layer (Mappers)
- Use table prefixes in SQL queries to avoid column ambiguity
- Implement batch query methods to prevent N+1 problems
- Handle charset conversion for cross-table joins
- Use GORM with proper connection lifecycle management

### Service Layer (Logic)
- Delegate complex queries to specialized query services
- Implement proper error handling and logging
- Use dependency injection via service context
- Follow single responsibility principle

### Testing Requirements
- Write comprehensive test coverage for all business logic
- Use table-driven tests for multiple scenarios
- Implement proper test data setup and cleanup
- Mock external dependencies for unit tests