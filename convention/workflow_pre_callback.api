syntax = "v1"


type WorkflowInfoReq {
    TenantID string `json:"tenantId"`
    OrganizationID string `json:"organizationId"`
    WorkflowID string `json:"workflowId"`
    SponsorID string `json:"sponsorId"`
    FormContent string `json:"formContent"`
    CreatedAt int64 `json:"createdAt"`
    BusinessID string `json:"businessId"`
    BusinessCode string `json:"businessCode"`
}

type WorkflowInfoResp {}

type DistributeApplicationInfoReq {
    ID                 string           `json:"id,optional"`        // 发放记录id
    SaveMethod         int              `json:"saveMethod"`         // 保存方式
    Applicant          string           `json:"applicant"`          // 申请人
    ApplyDate          int64            `json:"applyDate"`          // 申请日期
    DistributeType     int              `json:"distributeType"`     // 发放类型
    FileType           int              `json:"fileType"`           // 文件类型
    TypeDictNodeId     string           `json:"typeDictNodeId"`     // 类型字典节点id
    Reason             string           `json:"reason"`                 // 原因
    OtherReason        string           `json:"otherReason,optional"`       // 其他原因
    WishDistributeDate int64            `json:"wishDistributeDate,optional"` // 期望发放日期
    DistributeList     []DistributeItem `json:"distributeList"`     // 发放清单
}

type DistributeItem {
    FileID      string       `json:"fileId"`      // 文档id
    FileName    string       `json:"fileName"`    // 文档名称
    Number      string       `json:"number"`      // 文档编号
    Version     string       `json:"version"`     // 文档版本
    Permissions []Permission `json:"permissions"` // 权限
}

type Permission {
    FileForm       int         `json:"fileForm"`       // 文档形式
    FilePermission int         `json:"filePermission"` // 文档权限
    Recipient      string      `json:"recipient,optional"`     // 接收方
    ReceivedBy     []Recipient `json:"receivedBy"`     // 接收人
}

type Recipient {
    UserId   string `json:"userId"`
    UserName string `json:"userName"`
}

type TemporaryStorageDistributeInfoResp {}