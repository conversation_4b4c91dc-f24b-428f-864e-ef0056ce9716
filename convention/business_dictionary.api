syntax = "v1"



type GetBusinessDictionarysReq {
	PageInfo
	CommonSearchInfo
}

type GetBusinessDictionarysResp {
	PageInfo
	Data []BusinessDictionaryInfo `json:"data"`
}

type BusinessDictionaryInfo{
	ID string `json:"id"`
	ModuleName string `json:"moduleName"`
	FieldName string `json:"fieldName"`
	Status bool `json:"status"`
	UpdatedAt int64 `json:"updatedAt"`
	UpdatedBy string `json:"updatedBy"`
}




type GetBusinessDictionaryNodesReq {
	DictionaryID string `form:"dictionaryId"`
	ParentID string `form:"parentId,optional"`
}

type GetBusinessDictionaryNodesResp {
	Data []BusinessDictionaryNodeInfo `json:"data"`
}

type BusinessDictionaryNodeInfo {
	ID string `json:"id"`
	ParentID string `json:"parentId,optional"`
	Sort int `json:"sort"`
	Code string `json:"code"`
	Name string `json:"name"`
	Remark string `json:"remark"`
	Status bool `json:"status"`
	UpdatedAt int64 `json:"updatedAt"`
	UpdatedBy string `json:"updatedBy"`
}


type CreateBusinessDictionaryNodeReq {
	DictionaryID string `json:"dictionaryId"`
	ParentID string `json:"parentId,optional"`
	Name string `json:"name"`
	Remark string `json:"remark"`
	Code string `json:"code,optional"`
}

type CreateBusinessDictionaryNodeResp {

}

type UpdateBusinessDictionaryNodeReq {
	ID string `json:"id"`
	Name string `json:"name"`
	Remark string `json:"remark"`
	Status bool `json:"status"`
	Code string `json:"code"`
}

type UpdateBusinessDictionaryNodeResp {}

// 移动顺序
type MoveBusinessDictionaryNodeReq {
	ID string `json:"id"`
	Sort int `json:"sort"`
}

type MoveBusinessDictionaryNodeResp {

}


type DeleteBusinessDictionaryNodeReq {
	NodeID string `json:"nodeId"`
}

type DeleteBusinessDictionaryNodeResp {

}

type GetBusinessDictionaryRelationCountReq {
	NodeID string `form:"nodeId"`
}

type GetBusinessDictionaryRelationCountResp {
	Count int64 `json:"count"`
}

type GetBusinessDictionaryNodeTreeReq {
	ID string `form:"id"`
}

type GetBusinessDictionaryNodeTreeResp {
	ID string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
	Children []*GetBusinessDictionaryNodeTreeResp `json:"children"`
}