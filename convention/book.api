syntax = "v1"

type (
    BookInfo {
        ID string `json:"id,optional"`
        Status int32 `json:"status,optional"`
        IsDelete int32 `json:"isDelete,optional"`
        Number string `json:"number,optional"`
        Name string `json:"name,optional"`
        Author string `json:"author,optional"`
        BookType string `json:"bookType,optional"`
        Publisher string `json:"publisher,optional"`
        RegisterCount int `json:"registerCount,optional"`
        ReceiveCount int `json:"receiveCount,optional"`
        BorrowCount int `json:"borrowCount,optional"`
        // 是否借出（false未借出 | true借出中）
        OnBorrow bool `json:"onBorrow,optional"`
        SurplusCount int `json:"surplusCount,optional"`
        Remark string `json:"remark,optional"` // 0.5版本新增：备注
        CreatedTime int64 `json:"createdTime,optional"`
        UpdatedTime int64 `json:"updatedTime,optional"`
        CreatedBy string `json:"createdBy,optional"`
        UpdatedBy string `json:"updatedBy,optional"`
        DictionaryNodeID string `json:"dictionaryNodeId,optional"`
        FileID string `json:"fileId,optional"`
        OrganizationID string `json:"organizationId,optional"`
    }

    // 新建书籍信息
    CreateBookReq {
        Name string `json:"name"`
        Author string `json:"author"`
        Publisher string `json:"publisher"`
        RegisterCount int32 `json:"registerCount"`
        DictionaryNodeID string `json:"dictionaryNodeId"`
        // 书籍文件id
        FileID string `json:"fileId,optional"`
        Remark string `json:"remark,optional"` // 0.5版本新增：备注
    }

    CreateBookResp {

    }

    GetBookListReq {
        PageInfo
        Number string `json:"number,optional"`
        Name string `json:"name,optional"`
        Author string `json:"author,optional"`
        Publisher string `json:"publisher,optional"`
        BookType string `json:"bookType,optional"`
        OnBorrow string `json:"onBorrow,optional"`
        DictionaryNodeIds []string `json:"dictionaryNodeIds,optional"`
    }

    GetBookListResp {
        Total int64 `json:"total"`
        Data []BookListInfo `json:"data"`
    }
    BookListInfo {
        ID string `json:"id"`
        Status int32 `json:"status"`
        Number string `json:"number"`
        Name string `json:"name"`
        Author string `json:"author"`
        BookType string `json:"bookType"`
        Publisher string `json:"publisher"`
        RegisterCount int `json:"registerCount"`
        ReceiveCount int `json:"receiveCount"`
        BorrowCount int `json:"borrowCount"`
        // 是否借出（false未借出 | true借出中）
        OnBorrow bool `json:"onBorrow"`
        SurplusCount int `json:"surplusCount"`
        Remark string `json:"remark"` // 0.5版本新增：备注
        DictionaryNodeID string `json:"dictionaryNodeId"`
        BookFileInfo BookFileInfo `json:"bookFileInfo"`
        BookBorrowUsers []BookUser `json:"bookBorrowUsers"`
        BookReceiveUsers []BookUser `json:"bookReceiveUsers"`
    }

    BookUser {
        UserID string `json:"userId"`
        Nickname string `json:"nickname"`
        Date int64 `json:"date"`
    }

    BookFileInfo {
        FileId string `json:"fileId"`
        FileName string `json:"fileName"`
    }

    UpdateBookResp {

    }

    DeleteBookReq {
        ID string `json:"id"`
    }

    DeleteBookResp {

    }

    ImportBookReq {
        DictionaryID string `json:"dictionaryId"`
        ExcelID string `json:"excelId"`
        FileInfo []FileInfo `json:"fileInfo,optional"`
    }
    FileInfo {
        FileID string `json:"fileId,optional"`
        FileName string `json:"fileName,optional"`
    }

    ImportBookResp {

    }

    GiveBackBookReq {
        BookID string `json:"bookId"`
        GiveBackType int32 `json:"giveBackType"` // 归还类型 1借用归还 | 2领用归还
    }

    GiveBackBookResp {

    }

    CheckDuplicateBorrowOrReceiveReq {
        BookID string `form:"bookId""`
        PlagiarismCheckType string `form:"plagiarismCheckType"` // 查重类型 1借用查重 | 2领用查重
    }
)
