syntax = "v1"


info (
	title: "signature"
	desc:  "签名管理API"
)



type CreateSignatureTaskReq {}

type CreateSignatureTaskResp {
	TaskId string `json:"taskId"`
}

type GetSignatureTaskStatusReq {
	TaskId string `form:"taskId"`
}

type GetSignatureTaskStatusResp {
	SignatureBase64 string `json:"signatureBase64,optional"`
}

type UploadSignatureReq {
	TaskId          string `json:"taskId"`
	SignatureBase64 string `json:"signatureBase64"`
}

type UploadSignatureResp {
}

type ConfirmSignatureReq {
	TaskId string `json:"taskId"`
}

type ConfirmSignatureResp {
}


type GetCurrentSignatureResp {
    ID string `json:"id"`
    SignatureBase64 string `json:"signatureBase64"`
    AuthLetterFileID string `json:"authLetterFileId"`
    AuthLetterFileName string `json:"authLetterFileName"`
    EffectiveDate int64 `json:"effectiveDate"`
    ApproverName string `json:"approverName"`
}


type GetSignatureHistoryReq {
    PageInfo
}

type GetSignatureHistoryResp {
    Data []SignatureHistoryItem `json:"data"`
    PageInfo
}

type SignatureHistoryItem {
    ID string `json:"id"`
    SignatureBase64 string `json:"signatureBase64"`
    AuthLetterFileID string `json:"authLetterFileId"`
    AuthLetterFileName string `json:"authLetterFileName"`
    EffectiveDate int64 `json:"effectiveDate"`
    ExpirationDate int64 `json:"expirationDate"`
}


