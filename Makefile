apigo:
	 goctl api go -api ./convention/main.api -dir ./ --remote https://gitee.com/damengde/goctl_api_tempalte_new.git --branch v1.0.0 --style go_zero
apifor:
	 goctl api format --dir ./convention

build: clean
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o nebula

build-arm: clean
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -ldflags="-s -w" -o nebula

clean:
	rm -rf nebula


# 部署到150
deploy150: build
	./goblin --name nebula --host *************** --password Zj123456,,

grpcgo:
	protoc --go_out ./internal/infrastructure/adapter/grpc/pb --go-grpc_out ./internal/infrastructure/adapter/grpc/pb ./internal/infrastructure/adapter/grpc/protos/docvault.proto