ARG GOOSX

ARG GOARCHX

FROM registry.zhijiasoft.com/envs/golang:1.24-alpine AS builder

WORKDIR /app

ADD . .

RUN go env -w GO111MODULE=on

RUN go env -w GOPROXY=https://goproxy.cn,direct

RUN go mod tidy

RUN CGO_ENABLED=0 GOOS=$GOOSX GOARCH=$GOARCHX go build -a -installsuffix cgo -o /app/nebula .

FROM registry.zhijiasoft.com/envs/alpine:latest AS production

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update && apk upgrade

RUN apk --no-cache add ca-certificates

WORKDIR /root/

COPY --from=builder /app/nebula .

CMD ["./nebula"]
