package svc

import (
	"nebula/internal/config"
	"nebula/internal/domain/aggregate"
	"nebula/internal/domain/factory"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/clientx"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/infrastructure/referenceimpl"

	"gitee.com/damengde/gzconfigcenter"
	"go.uber.org/fx"
)

// 依赖注入
func provide(c gzconfigcenter.ConfigManager[config.Config], svcCtx *ServiceContext) {

	fx.New(
		// 基础组件
		fx.Options(
			fx.Supply(c.GetConfig()),
			fx.Provide(fx.Annotate(addons.NewRedisAddonsImpl, fx.As(new(addons.RedisAddons)))),
			fx.Provide(fx.Annotate(addons.NewIdGeneratorAddonsImpl, fx.As(new(addons.IDGeneratorAddons)))),
			fx.Provide(fx.Annotate(addons.NewQuickNameTranslatorImpl, fx.As(new(addons.QuickNameTranslator)))),
			fx.Provide(mapper.NewNebulaDB),
			fx.Provide(mapper.NewDocvaultDB),
			fx.Provide(mapper.NewPhoenixDB),
			fx.Provide(fx.Annotate(clientx.NewPhoenixClientImpl, fx.As(new(clientx.PhoenixClient)))),
			fx.Provide(fx.Annotate(clientx.NewMacrohardClientImpl, fx.As(new(clientx.MacrohardClient)))),
		),

		// 领域能力
		fx.Options(
			// 能力实现与其接口绑定
			fx.Provide(fx.Annotate(referenceimpl.NewBasicAbilityReferenceImpl, fx.As(new(aggregate.BasicAbilityReference)))),
			fx.Provide(fx.Annotate(referenceimpl.NewUserReferenceImplDemo, fx.As(new(aggregate.UserReferenceDemo)))),
			fx.Provide(fx.Annotate(referenceimpl.NewSignatureReferenceImpl, fx.As(new(aggregate.SignatureReference)))),
		),

		// 领域工厂
		fx.Options(
			fx.Provide(fx.Annotate(factory.NewImpl, fx.As(new(factory.Factory)))),
		),

		// Kafka 消费者
		// fx.Options(
		// 	fx.Invoke(queue.Init),
		// ),

		// 全文注入
		fx.Options(
			fx.Populate(&svcCtx.Factory),
			fx.Populate(&svcCtx.IdGenerator),
			fx.Populate(&svcCtx.RedisAddons),
			fx.Populate(&svcCtx.QuickNameTranslator),
			fx.Populate(&svcCtx.NebulaDB),
			fx.Populate(&svcCtx.DocvaultDB),
			fx.Populate(&svcCtx.PhoenixDB),
			fx.Populate(&svcCtx.PhoenixClient),
		),
	)

}
