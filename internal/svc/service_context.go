package svc

import (
	"context"
	"nebula/internal/config"
	"nebula/internal/domain/factory"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/clientx"
	"nebula/internal/infrastructure/adapter/kqs"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/infrastructure/adapter/rpcinterceptor"
	"nebula/internal/middleware"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config                           config.Config
	TraceMiddleware                  rest.Middleware
	AuthMiddleware                   rest.Middleware
	ConfigManager                    gzconfigcenter.ConfigManager[config.Config]
	RedisAddons                      addons.RedisAddons
	IdGenerator                      addons.IDGeneratorAddons
	Factory                          factory.Factory
	NebulaDB                         *mapper.NebulaDB
	DocvaultDB                       *mapper.DocvaultDB
	PhoenixDB                        *mapper.PhoenixDB
	PhoenixClient                    clientx.PhoenixClient
	MacrohardClient                  clientx.MacrohardClient
	QuickNameTranslator              addons.QuickNameTranslator
	DocvaultRpcConn                  *grpc.ClientConn
	KafkaDataExportProducer          kqs.DataExportProducer
	BusinessDictionaryChangeProducer *kqs.BusinessDictionaryChangeProducer
}

func NewServiceContext(c gzconfigcenter.ConfigManager[config.Config]) *ServiceContext {
	svcCtx := &ServiceContext{
		ConfigManager:   c,
		TraceMiddleware: middleware.NewTraceMiddleware().Handle,
		AuthMiddleware:  middleware.NewAuthMiddleware().Handle,
		DocvaultRpcConn: zrpc.MustNewClient(c.GetConfig().DocvaultRPC, zrpc.WithUnaryClientInterceptor(rpcinterceptor.SessionUnaryClientInterceptor)).Conn(),
	}
	initProducers(c, svcCtx)
	// 依赖注入
	provide(c, svcCtx)
	return svcCtx
}

func initProducers(c gzconfigcenter.ConfigManager[config.Config], svcCtx *ServiceContext) {
	producers := c.GetConfig().Kafka.Producers
	if len(producers) == 0 {
		return
	}
	for key, producer := range producers {
		switch key {
		case "data_export":
			svcCtx.KafkaDataExportProducer = kqs.NewDataExportProducer(c.GetConfig().Kafka.Brokers, producer.Topic)
		case "docvault_business_dictionary_change":
			svcCtx.BusinessDictionaryChangeProducer = kqs.NewBusinessDictionaryChangeProducer(c.GetConfig().Kafka.Brokers, producer.Topic)
		default:
			logc.Errorf(context.Background(), "未配置生产者：%s", key)
		}
	}
}
