package document_library

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/infrastructure/adapter/rpcinterceptor"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
	"gorm.io/datatypes"
)

// TestDataForGetDeprecateApplications 获取作废申请测试数据结构
type TestDataForGetDeprecateApplications struct {
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	ExternalDocuments               []mapper.ExternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
	DeprecationRecords              []mapper.DeprecationRecord
	DeprecationDocumentRelations    []mapper.DeprecationDocumentRelation
}

// setupTestEnvironmentForGetDeprecateApplications 设置获取作废申请测试环境
func setupTestEnvironmentForGetDeprecateApplications() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC, zrpc.WithUnaryClientInterceptor(rpcinterceptor.SessionUnaryClientInterceptor)).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForGetDeprecateApplications(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForGetDeprecateApplications 创建获取作废申请测试数据
func createTestDataForGetDeprecateApplications(svcCtx *svc.ServiceContext) (*TestDataForGetDeprecateApplications, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForGetDeprecateApplications(svcCtx)

	// 1. 创建测试用户（包含相似昵称的用户）
	users := []mapper.User{
		{
			ID:        "test_deprecate_app_user_001",
			Username:  "test_deprecate_app_user_001",
			Nickname:  "作废申请测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_deprecate_app_user_002",
			Username:  "test_deprecate_app_user_002",
			Nickname:  "作废申请测试用户2",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_deprecate_app_user_003",
			Username:  "test_deprecate_app_user_003",
			Nickname:  "作废申请测试人员1", // 相似但不同的昵称
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_deprecate_app_user_004",
			Username:  "test_deprecate_app_user_004",
			Nickname:  "测试用户申请人", // 包含"申请"关键字的昵称
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_deprecate_app_internal_doc_001",
			No:             "DEPRECATE-APP-INT-001",
			Name:           "作废申请测试内部文档1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_deprecate_app_file_001",
			DocCategoryID:  "test_deprecate_app_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_deprecate_app_user_001",
			CreatedBy:      "test_deprecate_app_user_001",
			UpdatedBy:      "test_deprecate_app_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour),
		},
		{
			ID:             "test_deprecate_app_internal_doc_002",
			No:             "DEPRECATE-APP-INT-002",
			Name:           "作废申请测试内部文档2",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_deprecate_app_file_002",
			DocCategoryID:  "test_deprecate_app_category_002",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_deprecate_app_user_002",
			CreatedBy:      "test_deprecate_app_user_002",
			UpdatedBy:      "test_deprecate_app_user_002",
			EffectiveDate:  now.Add(60 * 24 * time.Hour),
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:                   "test_deprecate_app_external_doc_001",
			Number:               "DEPRECATE-APP-EXT-001",
			Name:                 "作废申请测试外部文档1",
			OrganizationID:       "test_org_001",
			TenantID:             "test_tenant_001",
			Status:               3, // 有效状态
			CreatedAt:            now,
			UpdatedAt:            now,
			CreatedBy:            "test_deprecate_app_user_001",
			UpdatedBy:            "test_deprecate_app_user_001",
			TypeDictionaryNodeId: "test_deprecate_app_type_001",
			PublishDepartment:    "测试发文部门1",
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 4. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_deprecate_app_category_001",
			Names:  "作废申请内部文档类别1",
		},
		{
			NodeID: "test_deprecate_app_category_002",
			Names:  "作废申请内部文档类别2",
		},
		{
			NodeID: "test_deprecate_app_type_001",
			Names:  "作废申请外部文档类型1",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	// 5. 创建作废记录（包含更多用户的记录）
	deprecationRecords := []mapper.DeprecationRecord{
		{
			ID:             "test_deprecate_app_record_001",
			DeprecateAt:    now.Add(30 * 24 * time.Hour), // 30天后作废
			ApprovalStatus: 1,                            // 待提交
			Reason:         1,                            // 版本过期
			OtherReason:    "",
			WorkflowID:     "",
			ApprovalInfo:   datatypes.JSON("{}"),
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			CreatedAt:      now,
			UpdatedAt:      now,
			CreatedBy:      "test_deprecate_app_user_001",
			UpdatedBy:      "test_deprecate_app_user_001",
		},
		{
			ID:             "test_deprecate_app_record_002",
			DeprecateAt:    now.Add(60 * 24 * time.Hour), // 60天后作废
			ApprovalStatus: 2,                            // 待审批
			Reason:         2,                            // 功能变更
			OtherReason:    "",
			WorkflowID:     "test_workflow_002",
			ApprovalInfo:   datatypes.JSON("{}"),
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			CreatedAt:      now,
			UpdatedAt:      now,
			CreatedBy:      "test_deprecate_app_user_002",
			UpdatedBy:      "test_deprecate_app_user_002",
		},
		{
			ID:             "test_deprecate_app_record_003",
			DeprecateAt:    now.Add(90 * 24 * time.Hour), // 90天后作废
			ApprovalStatus: 3,                            // 已审批
			Reason:         3,                            // 政策变更
			OtherReason:    "",
			WorkflowID:     "test_workflow_003",
			ApprovalInfo:   datatypes.JSON("{}"),
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			CreatedAt:      now,
			UpdatedAt:      now,
			CreatedBy:      "test_deprecate_app_user_001",
			UpdatedBy:      "test_deprecate_app_user_001",
		},
		{
			ID:             "test_deprecate_app_record_004",
			DeprecateAt:    now.Add(120 * 24 * time.Hour), // 120天后作废
			ApprovalStatus: 1,                             // 待提交
			Reason:         1,                             // 版本过期
			OtherReason:    "",
			WorkflowID:     "",
			ApprovalInfo:   datatypes.JSON("{}"),
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			CreatedAt:      now,
			UpdatedAt:      now,
			CreatedBy:      "test_deprecate_app_user_004", // 使用第四个用户
			UpdatedBy:      "test_deprecate_app_user_004",
		},
	}

	// 插入作废记录数据
	for _, record := range deprecationRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废记录失败: %w", err)
		}
	}

	// 6. 创建作废文档关系
	deprecationDocRelations := []mapper.DeprecationDocumentRelation{
		{
			ID:                  "test_deprecate_app_relation_001",
			DeprecationRecordID: "test_deprecate_app_record_001",
			DocumentID:          "test_deprecate_app_internal_doc_001",
		},
		{
			ID:                  "test_deprecate_app_relation_002",
			DeprecationRecordID: "test_deprecate_app_record_002",
			DocumentID:          "test_deprecate_app_internal_doc_002",
		},
		{
			ID:                  "test_deprecate_app_relation_003",
			DeprecationRecordID: "test_deprecate_app_record_003",
			DocumentID:          "test_deprecate_app_external_doc_001",
		},
	}

	// 插入作废文档关系数据
	for _, relation := range deprecationDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废文档关系失败: %w", err)
		}
	}

	return &TestDataForGetDeprecateApplications{
		Users:                           users,
		InternalDocuments:               internalDocs,
		ExternalDocuments:               externalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
		DeprecationRecords:              deprecationRecords,
		DeprecationDocumentRelations:    deprecationDocRelations,
	}, nil
}

// cleanupTestDataForGetDeprecateApplications 清理获取作废申请测试数据
func cleanupTestDataForGetDeprecateApplications(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除作废文档关系记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_document_relations WHERE id LIKE '%test_deprecate_app_%'")

	// 删除作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_records WHERE id LIKE '%test_deprecate_app_%'")

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_app_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_app_external_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_deprecate_app_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_app_user_%").Delete(&mapper.User{})
}

// TestGetDeprecateApplicationsBasicQuery 测试基础查询功能
func TestGetDeprecateApplicationsBasicQuery(t *testing.T) {
	convey.Convey("测试获取作废申请基础查询功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有用户信息的上下文
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试无过滤条件查询
		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
		convey.So(resp.Total, convey.ShouldBeGreaterThanOrEqualTo, 0)
		convey.So(len(resp.Data), convey.ShouldBeGreaterThanOrEqualTo, 0)
	})
}

// TestGetDeprecateApplicationsFilterByDocumentNo 测试按文档编号过滤
func TestGetDeprecateApplicationsFilterByDocumentNo(t *testing.T) {
	convey.Convey("测试按文档编号过滤作废申请", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			DocumentNo: "DEPRECATE-APP-INT-001",
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// TestGetDeprecateApplicationsFilterByDocumentName 测试按文档名称过滤
func TestGetDeprecateApplicationsFilterByDocumentName(t *testing.T) {
	convey.Convey("测试按文档名称过滤作废申请", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			DocumentName: "作废申请测试内部文档1",
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// TestGetDeprecateApplicationsFilterByModuleType 测试按文档模块类型过滤
func TestGetDeprecateApplicationsFilterByModuleType(t *testing.T) {
	convey.Convey("测试按文档模块类型过滤作废申请", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试内部文档
		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			DocumentModuleType: 2, // 内部文档
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)

		// 测试外部文档
		req.DocumentModuleType = 3 // 外部文档
		resp, err = logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// TestGetDeprecateApplicationsFilterByStatus 测试按状态过滤
func TestGetDeprecateApplicationsFilterByStatus(t *testing.T) {
	convey.Convey("测试按状态过滤作废申请", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试待提交状态
		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			Status: 1, // 待提交
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)

		// 测试待审批状态
		req.Status = 2
		resp, err = logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// TestGetDeprecateApplicationsFilterByApplicant 测试按申请人过滤
func TestGetDeprecateApplicationsFilterByApplicant(t *testing.T) {
	convey.Convey("测试按申请人过滤作废申请", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试单个用户匹配
		convey.Convey("测试单个用户匹配", func() {
			req := &types.GetDeprecateApplicationsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
				Applicant: "作废申请测试用户1", // 应该只匹配一个用户
			}

			resp, err := logic.GetDeprecateApplications(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该返回该用户创建的记录（user_001 创建了2个记录）
		})

		// 测试多个用户匹配
		convey.Convey("测试多个用户匹配", func() {
			req := &types.GetDeprecateApplicationsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
				Applicant: "作废申请测试用户", // 应该匹配 user_001 和 user_002
			}

			resp, err := logic.GetDeprecateApplications(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该返回 user_001 和 user_002 创建的所有记录（总共3个记录）
		})

		// 测试关键字匹配
		convey.Convey("测试关键字匹配", func() {
			req := &types.GetDeprecateApplicationsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
				Applicant: "申请", // 应该匹配包含"申请"的所有用户
			}

			resp, err := logic.GetDeprecateApplications(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该匹配 user_001, user_002, user_003, user_004 的记录
		})

		// 测试不存在的用户
		convey.Convey("测试不存在的用户", func() {
			req := &types.GetDeprecateApplicationsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
				Applicant: "不存在的用户昵称",
			}

			resp, err := logic.GetDeprecateApplications(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 0)
			convey.So(len(resp.Data), convey.ShouldEqual, 0)
		})
	})
}

// TestGetDeprecateApplicationsPagination 测试分页功能
func TestGetDeprecateApplicationsPagination(t *testing.T) {
	convey.Convey("测试作废申请分页功能", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试第一页
		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 2,
				NoPage:   false,
			},
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)

		// 测试不分页
		req.PageInfo.NoPage = true
		resp, err = logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// TestGetDeprecateApplicationsEmptyResult 测试空结果
func TestGetDeprecateApplicationsEmptyResult(t *testing.T) {
	convey.Convey("测试作废申请空结果", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 不创建测试数据，直接查询
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			DocumentNo: "不存在的文档编号",
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
		convey.So(resp.Total, convey.ShouldEqual, 0)
		convey.So(len(resp.Data), convey.ShouldEqual, 0)
	})
}

// TestGetDeprecateApplicationsCombinedFilters 测试组合过滤条件
func TestGetDeprecateApplicationsCombinedFilters(t *testing.T) {
	convey.Convey("测试作废申请组合过滤条件", t, func() {
		svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetDeprecateApplications(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_app_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

		// 测试多个条件组合
		req := &types.GetDeprecateApplicationsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
				NoPage:   false,
			},
			DocumentModuleType: 2, // 内部文档
			Status:             1, // 待提交状态
			Applicant:          "作废申请测试用户1",
		}

		resp, err := logic.GetDeprecateApplications(req)
		convey.So(err, convey.ShouldBeNil)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

// 新增：边界条件和异常处理测试
func TestGetDeprecateApplicationsAdvancedScenarios(t *testing.T) {
	convey.Convey("测试作废申请高级场景", t, func() {
		convey.Convey("测试边界条件和异常处理", func() {
			svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			userInfo := &utils.UserLoginInfo{
				UserId:         "test_deprecate_app_user_001",
				TenantId:       "test_tenant_001",
				OrganizationId: "test_org_001",
				DeviceKind:     1,
				IsVirtualUser:  false,
			}
			ctx := userInfo.SetContext(context.Background())

			logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

			convey.Convey("测试极端分页参数", func() {
				// 测试页码为0
				req := &types.GetDeprecateApplicationsReq{
					PageInfo: types.PageInfo{
						Page:     0,
						PageSize: 10,
						NoPage:   false,
					},
				}
				resp, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试负数页码（使用0代替负数，因为Page是uint64类型）
				req.PageInfo.Page = 0
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试超大页码
				req.PageInfo.Page = 999999
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试页大小为0
				req.PageInfo.Page = 1
				req.PageInfo.PageSize = 0
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试超大页大小
				req.PageInfo.PageSize = 10000
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)
			})

			convey.Convey("测试特殊字符和SQL注入防护", func() {
				// 测试文档编号中的特殊字符
				req := &types.GetDeprecateApplicationsReq{
					PageInfo: types.PageInfo{
						Page:     1,
						PageSize: 10,
						NoPage:   false,
					},
					DocumentNo: "'; DROP TABLE deprecation_records; --",
				}
				resp, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试文档名称中的特殊字符
				req.DocumentNo = ""
				req.DocumentName = "<script>alert('xss')</script>"
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试申请人中的特殊字符
				req.DocumentName = ""
				req.Applicant = "'; UNION SELECT * FROM users; --"
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)
			})

			convey.Convey("测试超长字符串参数", func() {
				longString := ""
				for i := 0; i < 1000; i++ {
					longString += "a"
				}

				req := &types.GetDeprecateApplicationsReq{
					PageInfo: types.PageInfo{
						Page:     1,
						PageSize: 10,
						NoPage:   false,
					},
					DocumentNo:   longString,
					DocumentName: longString,
					Applicant:    longString,
				}
				resp, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)
			})

			convey.Convey("测试无效状态值", func() {
				req := &types.GetDeprecateApplicationsReq{
					PageInfo: types.PageInfo{
						Page:     1,
						PageSize: 10,
						NoPage:   false,
					},
					Status: 999, // 无效状态值
				}
				resp, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)

				// 测试负数状态值
				req.Status = -999
				resp, err = logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)
			})

			convey.Convey("测试无效文档模块类型", func() {
				req := &types.GetDeprecateApplicationsReq{
					PageInfo: types.PageInfo{
						Page:     1,
						PageSize: 10,
						NoPage:   false,
					},
					DocumentModuleType: 999, // 无效模块类型
				}
				resp, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
				convey.So(resp, convey.ShouldNotBeNil)
			})
		})

		// 新增：并发查询安全性测试
		convey.Convey("测试并发查询安全性", func() {
			svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createTestDataForGetDeprecateApplications(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			userInfo := &utils.UserLoginInfo{
				UserId:         "test_deprecate_app_user_001",
				TenantId:       "test_tenant_001",
				OrganizationId: "test_org_001",
				DeviceKind:     1,
				IsVirtualUser:  false,
			}
			ctx := userInfo.SetContext(context.Background())

			logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

			// 并发执行多次查询
			type result struct {
				resp *types.GetDeprecateApplicationsResp
				err  error
			}
			results := make(chan result, 20)

			for i := 0; i < 20; i++ {
				go func(index int) {
					req := &types.GetDeprecateApplicationsReq{
						PageInfo: types.PageInfo{
							Page:     1,
							PageSize: 10,
							NoPage:   false,
						},
						DocumentModuleType: int32(2 + (index % 2)), // 交替查询内部和外部文档
					}
					resp, err := logic.GetDeprecateApplications(req)
					results <- result{resp: resp, err: err}
				}(i)
			}

			// 验证所有并发查询结果
			for i := 0; i < 20; i++ {
				res := <-results
				convey.So(res.err, convey.ShouldBeNil)
				convey.So(res.resp, convey.ShouldNotBeNil)
				convey.So(res.resp.Total, convey.ShouldBeGreaterThanOrEqualTo, 0)
			}
		})

		// 新增：性能基准测试
		convey.Convey("测试性能基准", func() {
			svcCtx, cleanup, err := setupTestEnvironmentForGetDeprecateApplications()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createTestDataForGetDeprecateApplications(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			userInfo := &utils.UserLoginInfo{
				UserId:         "test_deprecate_app_user_001",
				TenantId:       "test_tenant_001",
				OrganizationId: "test_org_001",
				DeviceKind:     1,
				IsVirtualUser:  false,
			}
			ctx := userInfo.SetContext(context.Background())

			logic := NewGetDeprecateApplicationsLogic(ctx, svcCtx)

			// 测量单次查询性能
			start := time.Now()
			req := &types.GetDeprecateApplicationsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
			}
			resp, err := logic.GetDeprecateApplications(req)
			singleQueryDuration := time.Since(start)

			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(singleQueryDuration, convey.ShouldBeLessThan, 5*time.Second) // 性能基准：5秒内完成

			// 批量查询性能测试
			start = time.Now()
			for i := 0; i < 10; i++ {
				_, err := logic.GetDeprecateApplications(req)
				convey.So(err, convey.ShouldBeNil)
			}
			batchQueryDuration := time.Since(start)

			convey.So(batchQueryDuration, convey.ShouldBeLessThan, 30*time.Second) // 批量查询基准：30秒内完成

			fmt.Printf("📊 作废申请列表查询性能测试结果:\n")
			fmt.Printf("   - 单次查询: %v\n", singleQueryDuration)
			fmt.Printf("   - 批量查询(10次): %v\n", batchQueryDuration)
			fmt.Printf("   - 平均查询时间: %v\n", batchQueryDuration/10)
		})
	})
}
