package document_library

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackIncorporateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackIncorporateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackIncorporateLogic {
	return &WorkflowPreCallbackIncorporateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WorkflowPreCallbackIncorporateLogic) WorkflowPreCallbackIncorporate(req *types.WorkflowInfoReq) (resp *types.WorkflowInfoResp, err error) {
	logc.Infof(l.ctx, "纳入回调接口入参: %v", req)
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)
	orgInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, req.OrganizationID)
	if err != nil {
		logc.Errorf(l.ctx, "获取组织信息失败: %v", err)
		return
	}
	logc.Infof(l.ctx, "获取组织架构信息: %v", orgInfo)
	var data Data
	if err = json.Unmarshal([]byte(req.FormContent), &data); err != nil {
		logc.Errorf(l.ctx, "处理表单信息失败: %v", err)
		return
	}
	logc.Infof(l.ctx, "接受formContent信息: %v", data)

	var importGroupDocsToCompanyInfo []*docvault.ImportGroupDocsToCompanyInfo
	for _, v := range data.Data.Data {
		importGroupDocsToCompanyInfo = append(importGroupDocsToCompanyInfo, &docvault.ImportGroupDocsToCompanyInfo{
			Id:              v.ID,
			OriginalNumber:  v.OriginalNumber,
			OriginalVersion: v.OriginalVersion,
		})
	}
	logc.Infof(l.ctx, "开始调用rpc的纳入接口。。。")
	_, err = docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).ImportGroupDocsToCompany(l.ctx, &docvault.ImportGroupDocsToCompanyReq{
		OrgCode:           orgInfo.Code,
		Data:              importGroupDocsToCompanyInfo,
		ApprovalInfo:      &docvault.ApprovalInfo{},
		IncorporateStatus: 1,
		WorkFlowId:        req.WorkflowID,
	})
	if err != nil {
		logc.Errorf(l.ctx, "处理审批失败: %v", err)
		return nil, err
	}
	logc.Infof(l.ctx, "调用rpc的纳入接口完成。。。")

	return &types.WorkflowInfoResp{}, nil
}

type Data struct {
	Data ImportGroupExternalDocsToCompany `json:"data"`
}

type ImportGroupExternalDocsToCompany struct {
	Data []ImportGroupExternalDocsToCompanyInfo `json:"data"`
}

type ImportGroupExternalDocsToCompanyInfo struct {
	ID              string `json:"id"`
	OriginalNumber  string `json:"originalNumber"`
	OriginalVersion string `json:"originalVersion"`
}
