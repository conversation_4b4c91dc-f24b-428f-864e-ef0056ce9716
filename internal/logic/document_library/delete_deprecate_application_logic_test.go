package document_library

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestDeleteDeprecateApplication 测试删除作废申请功能
func TestDeleteDeprecateApplication(t *testing.T) {
	convey.Convey("测试删除作废申请功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForDeleteDeprecateApplication()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForDeleteDeprecateApplication(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有用户信息的上下文
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_delete_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		convey.Convey("测试删除存在的作废申请", func() {
			// 先创建一个作废申请
			saveLogic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)
			createReq := &types.SaveDeprecateApplicationReq{
				PlannedDeprecateDate: time.Now().AddDate(0, 1, 0).UnixMilli(),
				DocumentModuleType:   2,
				DocumentCategoryID:   "test_delete_category_001",
				DeprecateReason:      1,
				OtherReason:          "",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_delete_internal_doc_001",
					},
				},
				ApprovalStatus: 1,
			}

			createResp, err := saveLogic.SaveDeprecateApplication(createReq)
			convey.So(err, convey.ShouldBeNil)
			convey.So(createResp.ID, convey.ShouldNotBeEmpty)

			// 执行删除操作
			deleteLogic := NewDeleteDeprecateApplicationLogic(ctx, svcCtx)
			deleteReq := &types.DeleteDeprecateApplicationReq{
				ID: createResp.ID,
			}

			deleteResp, err := deleteLogic.DeleteDeprecateApplication(deleteReq)
			convey.So(err, convey.ShouldBeNil)
			convey.So(deleteResp, convey.ShouldNotBeNil)

			// 验证记录已被删除
			verifyDeprecationRecordDeleted(svcCtx, createResp.ID)
		})

		convey.Convey("测试参数验证", func() {
			deleteLogic := NewDeleteDeprecateApplicationLogic(ctx, svcCtx)

			convey.Convey("测试ID为空", func() {
				req := &types.DeleteDeprecateApplicationReq{
					ID: "", // 空ID
				}

				resp, err := deleteLogic.DeleteDeprecateApplication(req)
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(resp, convey.ShouldBeNil)
				convey.So(err.Error(), convey.ShouldContainSubstring, "作废申请ID不能为空")
			})
		})

		convey.Convey("测试删除不存在的记录", func() {
			deleteLogic := NewDeleteDeprecateApplicationLogic(ctx, svcCtx)

			req := &types.DeleteDeprecateApplicationReq{
				ID: "non_existent_id_12345",
			}

			// 删除不存在的记录应该会返回错误（由gRPC服务返回）
			resp, err := deleteLogic.DeleteDeprecateApplication(req)
			// 根据gRPC服务的实现，这里可能返回错误，也可能成功
			// 我们只验证不会panic
			_ = resp
			_ = err
		})
	})
}

// setupTestEnvironmentForDeleteDeprecateApplication 设置删除作废申请测试环境
func setupTestEnvironmentForDeleteDeprecateApplication() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForDeleteDeprecateApplication(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForDeleteDeprecateApplication 创建删除作废申请测试数据
func createTestDataForDeleteDeprecateApplication(svcCtx *svc.ServiceContext) (*TestDataForDeleteDeprecateApplication, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForDeleteDeprecateApplication(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_delete_user_001",
			Username:  "test_delete_user_001",
			Nickname:  "删除作废申请测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_delete_internal_doc_001",
			No:             "DELETE-INT-001",
			Name:           "删除作废申请测试内部文档1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_delete_file_001",
			DocCategoryID:  "test_delete_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_delete_user_001",
			CreatedBy:      "test_delete_user_001",
			UpdatedBy:      "test_delete_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour),
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_delete_category_001",
			Names:  "删除作废申请内部文档类别",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	return &TestDataForDeleteDeprecateApplication{
		Users:                           users,
		InternalDocuments:               internalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
	}, nil
}

// cleanupTestDataForDeleteDeprecateApplication 清理删除作废申请测试数据
func cleanupTestDataForDeleteDeprecateApplication(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除作废文档关系记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_document_relations WHERE document_id LIKE '%test_delete_%'")

	// 删除作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_records WHERE created_by = 'test_delete_user_001'")

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_delete_category_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_user_%").Delete(&mapper.User{})
}

// verifyDeprecationRecordDeleted 验证作废记录已被删除
func verifyDeprecationRecordDeleted(svcCtx *svc.ServiceContext, deprecationID string) {
	ctx := context.Background()
	deprecationClient := mapper.NewDeprecationRecordClient(svcCtx.DocvaultDB)

	// 尝试获取记录，应该返回错误或nil（表示记录已被删除）
	record, err := deprecationClient.GetByID(ctx, deprecationID)

	// 记录应该被删除，所以要么返回错误，要么返回nil
	if err != nil {
		// 预期的情况：记录不存在会返回错误
		convey.So(err, convey.ShouldNotBeNil)
	} else {
		// 如果没有错误，记录应该为nil
		convey.So(record, convey.ShouldBeNil)
	}
}

// TestDataForDeleteDeprecateApplication 删除作废申请测试数据结构
type TestDataForDeleteDeprecateApplication struct {
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
}
