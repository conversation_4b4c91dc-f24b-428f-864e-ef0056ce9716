package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeDetailLogic {
	return &GetDistributeDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDistributeDetail 根据发放记录id查询发放详情
func (l *GetDistributeDetailLogic) GetDistributeDetail(req *types.GetDistributeDetailReq) (resp *types.GetDistributeDetailResp, err error) {
	// 调用 RPC 接口获取发放详情
	detail, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDistributeDetail(l.ctx, &docvault.GetDistributeDetailReq{
		Id: req.ID,
	})
	if err != nil {
		l.Logger.Errorf("获取发放详情失败: %v", err)
		return nil, err
	}

	// 数据转换
	resp = l.dataTransition(detail)
	return resp, nil
}

// dataTransition 数据转换方法，将 RPC 响应转换为 API 响应格式
func (l *GetDistributeDetailLogic) dataTransition(detail *docvault.GetDistributeDetailResp) *types.GetDistributeDetailResp {
	if detail.Detail == nil {
		return &types.GetDistributeDetailResp{}
	}

	// 转换发放清单
	var distributeList []types.DistributeInventoryDetail
	for _, item := range detail.Detail.DistributeList {
		// 转换权限详情
		var permissions []types.PermissionDetail
		for _, perm := range item.Permissions {
			// 转换接收人详情
			var receivedBy []types.DistributeUserDetail
			for _, user := range perm.ReceivedBy {
				receivedBy = append(receivedBy, types.DistributeUserDetail{
					UserID:        user.UserId,
					UserNickname:  user.UserNickname,
					RecycleStatus: user.RecycleStatus,
					RecycleTime:   user.RecycleTime,
				})
			}

			permissions = append(permissions, types.PermissionDetail{
				FileForm:       perm.FileForm,
				FilePermission: perm.FilePermission,
				Recipient:      perm.Recipient,
				ReceivedBy:     receivedBy,
			})
		}

		distributeList = append(distributeList, types.DistributeInventoryDetail{
			ID:          item.Id,
			FileID:      item.FileId,
			FileName:    item.FileName,
			Number:      item.Number,
			Version:     item.Version,
			Permissions: permissions,
		})
	}

	return &types.GetDistributeDetailResp{
		ID:                 detail.Detail.Id,
		WorkflowID:         detail.Detail.WorkflowId,
		Applicant:          detail.Detail.Applicant,
		ApplicantName:      detail.Detail.ApplicantName,
		ApplyDate:          detail.Detail.ApplyDate,
		DistributeType:     detail.Detail.DistributeType,
		FileType:           detail.Detail.FileType,
		FileCategory:       detail.Detail.FileCategory,
		TypeDictNodeID:     detail.Detail.TypeDictNodeId,
		Reason:             detail.Detail.Reason,
		OtherReason:        detail.Detail.OtherReason,
		WishDistributeDate: detail.Detail.WishDistributeDate,
		Status:             detail.Detail.Status,
		DistributeList:     distributeList,
		CreatedAt:          detail.Detail.CreatedAt,
		UpdatedAt:          detail.Detail.UpdatedAt,
	}
}
