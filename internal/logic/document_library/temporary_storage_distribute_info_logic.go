package document_library

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemporaryStorageDistributeInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTemporaryStorageDistributeInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemporaryStorageDistributeInfoLogic {
	return &TemporaryStorageDistributeInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TemporaryStorageDistributeInfoLogic) TemporaryStorageDistributeInfo(req *types.DistributeApplicationInfoReq) (resp *types.TemporaryStorageDistributeInfoResp, err error) {
	// 获取文件分类
	fileCategory, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, req.TypeDictNodeId)
	if err != nil {
		l.Logger.Errorf("获取文件分类失败: %v", err)
		return nil, err
	}

	// 构造发放记录
	documentDistributeReq := l.apiReqToRpcReq(req, &fileCategory)

	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).PreSaveDistributeRecord(l.ctx, documentDistributeReq)
	if err != nil {
		logc.Errorf(l.ctx, "保存文件发放记录失败: %v", err)
		return nil, err
	}

	return nil, nil
}

func (l *TemporaryStorageDistributeInfoLogic) apiReqToRpcReq(distributeApplication *types.DistributeApplicationInfoReq, fileCategory *mapper.BusinessDictionaryNodeRelation) *docvault.DocumentDistributeReq {
	documentDistributeReq := &docvault.DocumentDistributeReq{
		Id:                 distributeApplication.ID,
		Applicant:          distributeApplication.Applicant,
		ApplyDate:          distributeApplication.ApplyDate,
		DistributeType:     int32(distributeApplication.DistributeType),
		FileType:           int32(distributeApplication.FileType),
		TypeDictNodeId:     distributeApplication.TypeDictNodeId,
		Reason:             distributeApplication.Reason,
		OtherReason:        distributeApplication.OtherReason,
		WishDistributeDate: distributeApplication.WishDistributeDate,
		FileCategory:       fileCategory.Names,
		DistributeList:     nil,
		SaveMethod:         int32(distributeApplication.SaveMethod),
	}

	for k1, distribute := range distributeApplication.DistributeList {
		documentDistributeReq.DistributeList = append(documentDistributeReq.DistributeList, &docvault.DistributeList{
			FileId:      distribute.FileID,
			FileName:    distribute.FileName,
			Number:      distribute.Number,
			Version:     distribute.Version,
			Permissions: nil,
		})
		for k2, permission := range distribute.Permissions {
			documentDistributeReq.DistributeList[k1].Permissions = append(documentDistributeReq.DistributeList[k1].Permissions, &docvault.Permission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				Recipient:      permission.Recipient,
				ReceivedBy:     nil,
			})
			for _, received := range permission.ReceivedBy {
				documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy = append(documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy, &docvault.Recipient{
					UserId:   received.UserId,
					UserName: received.UserName,
				})
			}
		}
	}
	return documentDistributeReq
}
