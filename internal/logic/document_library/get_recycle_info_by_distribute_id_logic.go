package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRecycleInfoByDistributeIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRecycleInfoByDistributeIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRecycleInfoByDistributeIdLogic {
	return &GetRecycleInfoByDistributeIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetRecycleInfoByDistributeId 根据发放清单ID查询回收信息
func (l *GetRecycleInfoByDistributeIdLogic) GetRecycleInfoByDistributeId(req *types.GetRecycleInfoByDistributeIdReq) (resp *types.GetRecycleInfoByDistributeIdResp, err error) {
	// 调用 RPC 接口获取回收信息
	recycleInfo, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetRecycleInfoByDistributeRecordFileId(l.ctx, &docvault.GetRecycleInfoReq{
		DistributeRecordFileId: req.DistributeListID,
	})
	if err != nil {
		l.Logger.Errorf("根据发放清单ID查询回收信息失败: %v", err)
		return nil, err
	}

	// 数据转换
	resp = l.dataTransition(recycleInfo)
	return resp, nil
}

// dataTransition 数据转换方法，将 RPC 响应转换为 API 响应格式
func (l *GetRecycleInfoByDistributeIdLogic) dataTransition(recycleInfo *docvault.GetRecycleInfoResp) *types.GetRecycleInfoByDistributeIdResp {
	if recycleInfo.RecycleInfo == nil {
		return &types.GetRecycleInfoByDistributeIdResp{}
	}

	// 转换回收记录列表
	var recycleRecords []types.RecycleRecord
	for _, record := range recycleInfo.RecycleInfo.RecycleRecords {
		// 转换交还人信息列表
		var handoverPersons []types.HandoverPerson
		for _, person := range record.HandoverPersons {
			handoverPersons = append(handoverPersons, types.HandoverPerson{
				HandoverID:     person.HandoverId,
				HandoverName:   person.HandoverName,
				FileForm:       person.FileForm,
				FilePermission: person.FilePermission,
			})
		}

		recycleRecords = append(recycleRecords, types.RecycleRecord{
			RecycleInitiator: record.RecycleInitiator,
			RecycleReason:    record.RecycleReason,
			HandoverPersons:  handoverPersons,
			Auditors:         record.Auditors,
			Approvers:        record.Approvers,
			RecycleDate:      record.RecycleDate,
		})
	}

	return &types.GetRecycleInfoByDistributeIdResp{
		FileName:       recycleInfo.RecycleInfo.FileName,
		FileNumber:     recycleInfo.RecycleInfo.FileNumber,
		RecycleRecords: recycleRecords,
	}
}
