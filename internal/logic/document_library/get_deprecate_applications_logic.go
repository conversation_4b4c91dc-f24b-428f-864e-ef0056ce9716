package document_library

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeprecateApplicationsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeprecateApplicationsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeprecateApplicationsLogic {
	return &GetDeprecateApplicationsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeprecateApplicationsLogic) GetDeprecateApplications(req *types.GetDeprecateApplicationsReq) (resp *types.GetDeprecateApplicationsResp, err error) {
	// 使用新的查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetDeprecateApplications(l.ctx, req)
}

