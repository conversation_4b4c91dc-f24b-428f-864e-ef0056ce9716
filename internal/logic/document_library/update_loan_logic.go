package document_library

import (
	"context"
	"time"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateLoanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateLoanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateLoanLogic {
	return &UpdateLoanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// UpdateLoan 更新借阅记录
// 功能：根据借阅记录ID更新指定的借阅记录信息
// 参数：req - 更新借阅记录请求参数，包含借阅记录ID和更新字段
// 返回值：resp - 更新响应，err - 错误信息
// 异常：当获取用户信息失败或RPC调用失败时返回错误
func (l *UpdateLoanLogic) UpdateLoan(req *types.UpdateLoanReq) (resp *types.UpdateLoanResp, err error) {
	// 步骤1：转换文档列表格式为gRPC所需格式
	// 步骤2：构建gRPC请求参数，包含用户ID和更新字段
	// 步骤3：调用docvault服务的ModifyBorrowRecord方法
	// 步骤4：处理响应结果并记录日志
	// 步骤5：返回更新响应

	// 转换文档列表格式
	var documents []*docvault.BorrowDocumentItem
	for _, doc := range req.Documents {
		documents = append(documents, &docvault.BorrowDocumentItem{
			DocumentId:    doc.DocumentID,
			VersionNo:     doc.DocumentVersionNo,
			ModuleType:    doc.DocumentModuleType,
			BorrowStatus:  doc.BorrowStatus,
			RecoverUserId: "", // 更新时不设置回收用户
			RecoverTime:   0,  // 更新时不设置回收时间
		})
	}

	// 构建gRPC请求参数
	grpcReq := &docvault.BorrowRecordModifyReq{
		BorrowRecordId:    req.ID,
		UserId:            utils.GetContextUserID(l.ctx),
		Documents:         documents,
		BorrowTime:        req.BorrowTime,
		DueTime:           req.DueTime,
		BorrowReasonType:  req.Reason,
		BorrowOtherReason: req.OtherReason,
		ApprovalStatus:    req.ApprovalStatus,
		ApprovalApplyTime: time.Now().UnixMilli(),
	}

	// 调用docvault服务更新借阅记录
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).ModifyBorrowRecord(l.ctx, grpcReq)
	if err != nil {
		logc.Errorw(l.ctx, "更新借阅记录失败",
			logx.Field("borrowRecordId", req.ID),
			logx.Field("userId", utils.GetContextUserID(l.ctx)),
			logx.Field("error", err))
		return nil, err
	}

	// 返回响应
	return &types.UpdateLoanResp{}, nil
}
