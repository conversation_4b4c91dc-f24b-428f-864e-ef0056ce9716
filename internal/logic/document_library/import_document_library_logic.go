package document_library

import (
	"context"
	"errors"

	"nebula/internal/logic/document_library/doclib_importer"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ImportDocumentLibraryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewImportDocumentLibraryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportDocumentLibraryLogic {
	return &ImportDocumentLibraryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ImportDocumentLibraryLogic) ImportDocumentLibrary(req *types.ImportDocumentLibraryReq) (resp *types.ImportDocumentLibraryResp, err error) {
	var it doclib_importer.Importer
	switch req.ModuleType {
	case 1: // 书籍库
		it = doclib_importer.NewImporter(doclib_importer.NewBookImporter(l.svcCtx))
	case 2: // 内部库
		it = doclib_importer.NewImporter(doclib_importer.NewInternalDocumentImporter(l.svcCtx))
	case 3: // 外部库
		it = doclib_importer.NewImporter(doclib_importer.NewExternalDocumentImporter(l.svcCtx))
	default:
		return nil, errors.New("module type not supported")
	}

	if err := it.Import(l.ctx, doclib_importer.ImportRequest{
		MainFileID:         req.MainFileID,
		ListFileIDs:        req.ListFileIDs,
		TypeDictionaryID:   req.TypeDictionaryID,
		DomainDictionaryID: req.DomainDictionaryID,
		AuthDictionaryID:   req.AuthDictionaryID,
	}); err != nil {
		return nil, err
	}

	return
}
