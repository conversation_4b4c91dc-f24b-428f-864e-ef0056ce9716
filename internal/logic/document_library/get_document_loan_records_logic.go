package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/query/borrowrecord"
	"nebula/internal/query/borrowrecord/assemblers"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocumentLoanRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDocumentLoanRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocumentLoanRecordsLogic {
	return &GetDocumentLoanRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDocumentLoanRecords 根据文档ID获取借阅记录
// 功能: 根据指定的文档ID，反向查询该文档的所有借阅记录
// 参数:
//   - req: 查询请求参数，包含文档ID
//
// 返回值:
//   - resp: 借阅记录响应，包含借阅记录列表和总数
//   - err: 错误信息
//
// 异常: 当文档ID为空或查询失败时返回错误
func (l *GetDocumentLoanRecordsLogic) GetDocumentLoanRecords(req *types.GetDocumentLoanRecordsReq) (resp *types.GetDocumentLoanRecordsResp, err error) {
	// 实现步骤：
	// 1. 参数验证
	// 2. 创建BorrowRecordClient实例
	// 3. 构建查询请求，使用DocumentIDs过滤
	// 4. 执行查询获取借阅记录
	// 5. 使用查询服务获取关联数据并组装结果
	// 6. 返回响应

	// 1. 参数验证
	if req.DocumentID == "" {
		return nil, fmt.Errorf("文档ID不能为空")
	}

	// 2. 创建BorrowRecordClient实例
	borrowerClient := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB)

	// 3. 构建查询请求，使用DocumentIDs过滤
	pageReq := mapper.PageBorrowRecordReq{
		DocumentIDs:    []string{req.DocumentID},
		Page:           int(req.Page),
		PageSize:       int(req.PageSize),
		NoPage:         req.NoPage,
		ApprovalStatus: func() *int { v := 3; return &v }(),
	}

	// 4. 执行查询获取借阅记录
	borrowerRecords, total, err := borrowerClient.GetBorrowRecordStatistics(l.ctx, pageReq)
	if err != nil {
		return nil, fmt.Errorf("查询借阅记录失败: %w", err)
	}

	if len(borrowerRecords) == 0 {
		return &types.GetDocumentLoanRecordsResp{
			Data:  []types.LoanRecord{},
			Total: total,
		}, nil
	}

	// 5. 使用查询服务获取关联数据并组装结果
	queryService := borrowrecord.NewBorrowRecordQueryService(l.svcCtx)
	queryResult, err := queryService.QueryConcurrently(l.ctx, borrowerRecords)
	if err != nil {
		return nil, fmt.Errorf("并发查询关联数据失败: %w", err)
	}

	// 6. 组装最终响应数据
	loanRecordAssembler := assemblers.NewLoanRecordAssembler()
	assemblerQueryResult := &assemblers.QueryResult{
		UserNicknames:  queryResult.UserNicknames,
		DocumentCounts: queryResult.DocumentCounts,
	}

	// 组装响应
	assembledResp := loanRecordAssembler.AssembleWithPagination(borrowerRecords, assemblerQueryResult, int(total))

	return &types.GetDocumentLoanRecordsResp{
		Data:  assembledResp.Data,
		Total: assembledResp.Total,
	}, nil
}
