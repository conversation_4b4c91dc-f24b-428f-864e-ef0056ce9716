package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAllowBorrowDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewGetAllowBorrowDocumentsLogic 创建查询可借阅文档列表逻辑处理器
// 功能：初始化查询可借阅文档列表的业务逻辑处理器
// 参数：ctx - 上下文，svcCtx - 服务上下文
// 返回值：查询可借阅文档列表逻辑处理器实例
func NewGetAllowBorrowDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAllowBorrowDocumentsLogic {
	return &GetAllowBorrowDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetAllowBorrowDocuments 查询可借阅文档列表
// 功能：根据多种筛选条件查询用户可以借阅的文档列表，支持按有效性状态、文档类型、分类、名称、编号等条件筛选
// 参数：req - 查询请求参数，包含筛选条件
// 返回值：可借阅文档列表响应，错误信息
func (l *GetAllowBorrowDocumentsLogic) GetAllowBorrowDocuments(req *types.GetAllowBorrowDocumentsReq) (resp *types.GetAllowBorrowDocumentsResp, err error) {
	// 获取当前用户的组织ID
	organizationID := utils.GetContextOrganizationID(l.ctx)
	// 构建查询参数
	queryReq := mapper.AllDocumentsQueryReq{
		OrganizationID:     organizationID,
		DocumentCategoryID: req.DocumentCategoryId,
		DocumentName:       req.DocumentName,
		DocumentNo:         req.DocumentNo,
		NoPage:             true, // 不分页，获取所有符合条件的文档
	}

	// 设置文档状态筛选条件
	if req.DocumentValidity != 0 {
		queryReq.DocumentStatus = []int8{int8(req.DocumentValidity)} // 查询所有状态
	}

	if req.DocumentModuleType != 0 {
		queryReq.DocumentModule = []int{req.DocumentModuleType} // 查询内部库和外部库
	}

	// 创建文档统一视图客户端
	allDocsClient := mapper.NewAllDocumentsViewClient(l.svcCtx.DocvaultDB)

	// 查询文档列表
	documents, _, err := allDocsClient.GetAllowBorrowDocuments(l.ctx, queryReq)
	if err != nil {
		l.Logger.Errorf("查询可借阅文档失败：%v", err)
		return nil, err
	}

	// 转换响应数据格式
	borrowableDocuments := make([]types.BorrowableDocument, 0, len(documents))
	for _, doc := range documents {
		borrowableDocuments = append(borrowableDocuments, types.BorrowableDocument{
			DocumentId:        doc.DocumentID,
			DocumentVersionNo: doc.DocumentVersionNo,
			DocumentNo:        doc.DocumentNo,
			DocumentName:      doc.DocumentName,
		})
	}

	return &types.GetAllowBorrowDocumentsResp{
		List: borrowableDocuments,
	}, nil
}
