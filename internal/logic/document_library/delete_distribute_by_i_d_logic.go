package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDistributeByIDLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDistributeByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDistributeByIDLogic {
	return &DeleteDistributeByIDLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDistributeByIDLogic) DeleteDistributeByID(req *types.DeleteDistributeReq) (resp *types.DeleteDistributeResp, err error) {
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).DeleteDistributeRecord(l.ctx, &docvault.DeleteDistributeReq{
		Id: req.ID,
	})
	if err != nil {
		l.Logger.Errorf("删除失败: %v", err)
		return nil, err
	}
	return &types.DeleteDistributeResp{}, nil
}
