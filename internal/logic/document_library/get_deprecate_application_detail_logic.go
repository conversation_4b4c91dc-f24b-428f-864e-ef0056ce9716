package document_library

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeprecateApplicationDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeprecateApplicationDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeprecateApplicationDetailLogic {
	return &GetDeprecateApplicationDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeprecateApplicationDetailLogic) GetDeprecateApplicationDetail(req *types.GetDeprecateApplicationDetailReq) (resp *types.GetDeprecateApplicationDetailResp, err error) {
	// 使用新的查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetDeprecateApplicationDetail(l.ctx, req)
}

