package doclib_exporter

import (
	"context"
	"nebula/internal/svc"
	"nebula/internal/utils"
	"os"
	"path"

	"errors"
	"time"

	"nebula/internal/infrastructure/adapter/kqs"

	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

// 导出器接口
type Exporter interface {
	Export(ctx context.Context, req interface{}) error
}

// 导出器能力接口
// ExporterAbility 定义了导出流程中各环节的能力接口，适用于不同业务场景的导出需求。
type ExporterAbility interface {
	// 获取导出模块信息
	// ctx: 上下文对象
	// req: 导出请求参数
	// 返回：文件名称，模块名称，错误信息
	GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error)

	// 推送进行中状态
	// ctx: 上下文对象
	// modelInfo: 导出任务信息
	// 返回：错误信息
	PushStatusProgress(ctx context.Context, modelInfo kqs.DataExportModelInfo) error

	// 获取数据
	// ctx: 上下文对象
	// req: 导出请求参数
	// 返回：二维字符串数据，错误信息
	GetData(ctx context.Context, req interface{}) ([][]string, error)

	// 获取模板
	// ctx: 上下文对象
	// 返回：模板路径，错误信息
	GetTpl(ctx context.Context) (tplPath string, err error)

	// 写入表格
	// ctx: 上下文对象
	// tplPath: 模板路径
	// params: 表格参数
	// startRow: 起始行
	// 返回：新文件路径，错误信息
	WriteSheet(ctx context.Context, tplPath string, params [][]string, startRow int) (newPath string, err error)

	// 上传文件
	// ctx: 上下文对象
	// path: 本地文件路径
	// filename: 指定上传后的文件名
	// 返回：文件ID，错误信息
	UploadFile(ctx context.Context, path, filename string) (fileID string, err error)

	// 推送完成状态到 kafka
	// ctx: 上下文对象
	// modelInfo: 导出任务信息
	// 返回：错误信息
	PushStatusComplete(ctx context.Context, modelInfo *kqs.DataExportModelInfo, err *error) error
}

// NewExporter 创建一个新的通用导出器实例。
// exporterAbility: 具体业务的导出能力实现
// svc: 服务上下文
// 返回：通用导出器实例
func NewExporter(exporterAbility ExporterAbility, svc *svc.ServiceContext) *ExporterImpl {
	return &ExporterImpl{
		ExporterAbility: exporterAbility,
		SvcCtx:          svc,
	}
}

// Export 执行完整的导出流程。
// ctx: 上下文对象
// req: 导出请求参数
// 返回：文件ID，错误信息
func (t *ExporterImpl) Export(ctx context.Context, req interface{}) (err error) {
	// 1. 获取导出模块信息
	fileName, moduleName, err := t.GetExportModelInfo(ctx)
	if err != nil {
		logc.Errorf(ctx, "获取导出模块信息失败: %v", err)
		return err
	}
	modelInfo := kqs.DataExportModelInfo{
		TaskID:     t.SvcCtx.IdGenerator.GenerateIDString(),
		FileName:   fileName,
		ModuleName: moduleName,
		UserID:     utils.GetContextUserID(ctx),
	}
	// 日志加入task_id
	ctx = logx.ContextWithFields(ctx, logx.Field("task_id", modelInfo.TaskID))

	// 2. 推送进行中状态
	err = t.PushStatusProgress(ctx, modelInfo)
	if err != nil {
		logc.Errorf(ctx, "推送进行中状态失败: %v", err)
		return err
	}

	// 开始异步导出
	go func(ctx context.Context, modelInfo kqs.DataExportModelInfo) {
		var err error
		defer t.PushStatusComplete(ctx, &modelInfo, &err)

		// 3. 查询数据
		data, err := t.GetData(ctx, req)
		if err != nil {
			logc.Errorf(ctx, "查询数据失败: %v", err)
			return
		}

		// 4. 获取模板路径
		tplPath, err := t.GetTpl(ctx)
		if err != nil {
			logc.Errorf(ctx, "获取模板路径失败: %v", err)
			return
		}

		// 5. 写入表格
		exportPath, err := t.WriteSheet(ctx, tplPath, data, 2)
		if err != nil {
			logc.Errorf(ctx, "写入表格失败: %v", err)
			return
		}
		defer os.Remove(exportPath)

		// 6. 上传文件（统一处理）
		fileID, err := t.UploadFile(ctx, exportPath, fileName)
		if err != nil {
			logc.Errorf(ctx, "上传文件失败: %v", err)
			return
		}
		modelInfo.FileID = fileID

	}(context.WithoutCancel(ctx), modelInfo)

	return nil
}

// 通用导出器实现
// ExporterImpl 实现了导出器通用流程，依赖能力接口完成具体业务逻辑。
type ExporterImpl struct {
	SvcCtx *svc.ServiceContext
	ExporterAbility
}

// 导出器能力实现
type ExporterAbilityImpl struct {
	svcCtx *svc.ServiceContext
}

// NewExporterAbility 返回 ExporterAbility
func NewExporterAbility(svcCtx *svc.ServiceContext) ExporterAbility {
	return &ExporterAbilityImpl{
		svcCtx: svcCtx,
	}
}

func (t *ExporterAbilityImpl) GetExportModelInfo(ctx context.Context) (string, string, error) {
	return "", "", nil
}

func (t *ExporterAbilityImpl) PushStatusProgress(ctx context.Context, modelInfo kqs.DataExportModelInfo) error {
	modelInfo.Status = kqs.DataExportStatusProgress
	err := t.svcCtx.KafkaDataExportProducer.SendMessage(ctx, modelInfo)
	if err != nil {
		logc.Errorf(ctx, "推送进行中状态失败: %v", err)
		return err
	}
	return nil
}

func (t *ExporterAbilityImpl) GetData(ctx context.Context, req interface{}) ([][]string, error) {
	return nil, nil
}

func (t *ExporterAbilityImpl) GetTpl(ctx context.Context) (string, error) {
	return "", nil
}

func (t *ExporterAbilityImpl) WriteSheet(ctx context.Context, tplPath string, params [][]string, startRow int) (string, error) {
	// 使用 excelize 处理 Excel 文件
	// 如果 tplPath 为空则新建表格，否则加载模板
	var (
		f   *excelize.File
		err error
	)
	f, err = excelize.OpenFile(tplPath)
	if err != nil {
		return "", err
	}

	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		sheetName = "Sheet1"
	}

	// 从 startRow 行开始写入 params 数据
	for i, row := range params {
		for j, cell := range row {
			cellAxis, _ := excelize.CoordinatesToCellName(j+1, startRow+i)
			f.SetCellValue(sheetName, cellAxis, cell)
		}
	}

	// 保存为新文件
	// 放在系统临时目录里
	outputPath := path.Join(os.TempDir(), "exported_"+time.Now().Format("20060102150405")+".xlsx")
	if err := f.SaveAs(outputPath); err != nil {
		return "", err
	}
	return outputPath, nil
}

func (t *ExporterAbilityImpl) PushStatusComplete(ctx context.Context, modelInfo *kqs.DataExportModelInfo, err *error) error {
	if *err != nil {
		logc.Errorf(ctx, "导出失败: %v", *err)
		modelInfo.Status = kqs.DataExportStatusFailed
	} else {
		modelInfo.Status = kqs.DataExportStatusComplete
	}
	err2 := t.svcCtx.KafkaDataExportProducer.SendMessage(ctx, *modelInfo)
	if err2 != nil {
		logc.Errorf(ctx, "推送完成状态失败: %v", err2)
		return err2
	}
	return nil
}

func (t *ExporterAbilityImpl) UploadFile(ctx context.Context, path, filename string) (string, error) {
	// 通过 svcCtx 获取 PhoenixClient
	phoenixClient := t.svcCtx.PhoenixClient
	if phoenixClient == nil {
		return "", errors.New("PhoenixClient 未初始化")
	}
	// 调用 PhoenixClient 的 UploadFile 方法上传文件
	fileId, err := phoenixClient.UploadFile(ctx, path, filename)
	if err != nil {
		return "", err
	}
	return fileId, nil
}
