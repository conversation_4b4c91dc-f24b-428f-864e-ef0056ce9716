package doclib_exporter

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

// DeprecationRecordExporter 作废记录导出器
// 实现 ExporterAbility 接口，提供作废记录导出功能
type DeprecationRecordExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

// NewDeprecationRecordExporter 创建作废记录导出器实例
// 功能：初始化作废记录导出器，设置服务上下文和基础导出能力
// 参数：svcCtx - 服务上下文
// 返回值：ExporterAbility - 导出器接口实例
func NewDeprecationRecordExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &DeprecationRecordExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

// GetExportModelInfo 获取导出模块信息
// 功能：生成作废记录导出的文件名和模块名称
// 参数：ctx - 上下文
// 返回值：fileName - 文件名，moduleName - 模块名称，err - 错误信息
func (e *DeprecationRecordExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	// 获取当前用户昵称
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))

	// 生成文件名：作废记录-用户昵称-时间戳.xlsx
	fileName = "作废记录-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx"
	moduleName = "文件管理-作废记录"

	return fileName, moduleName, nil
}

// GetData 获取作废记录数据
// 功能：调用作废记录查询接口获取数据，并转换为Excel导出格式
// 参数：ctx - 上下文，req - 查询请求参数
// 返回值：[][]string - Excel数据行，error - 错误信息
// 异常：JSON序列化失败、查询接口调用失败等
func (e *DeprecationRecordExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	// 实现步骤：
	// 1. 将请求参数转换为作废记录查询请求
	// 2. 调用作废记录查询逻辑获取数据
	// 3. 将查询结果转换为Excel格式的二维字符串数组
	// 4. 返回转换后的数据

	// 序列化请求参数
	jsonReq, err := json.Marshal(req)
	if err != nil {
		logc.Errorf(ctx, "序列化作废记录导出请求失败: %v", err)
		return nil, err
	}

	// 反序列化为作废记录查询请求
	var deprecateReq types.GetDeprecateApplicationsReq
	err = json.Unmarshal(jsonReq, &deprecateReq)
	if err != nil {
		logc.Errorf(ctx, "反序列化作废记录查询请求失败: %v", err)
		return nil, err
	}

	// 设置为不分页，获取所有数据
	deprecateReq.NoPage = true

	// 调用作废记录查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(e.svcCtx)
	resp, err := queryService.GetDeprecateApplications(ctx, &deprecateReq)
	if err != nil {
		logc.Errorf(ctx, "查询作废记录失败: %v", err)
		return nil, err
	}

	// 如果没有数据，返回空数组
	if len(resp.Data) == 0 {
		return [][]string{}, nil
	}

	// 转换为Excel格式数据
	data := make([][]string, len(resp.Data))
	for i, record := range resp.Data {
		data[i] = e.convertToExcelRow(i+1, record)
	}

	return data, nil
}

// convertToExcelRow 将作废记录转换为Excel行数据
// 功能：按照表格字段顺序转换单条作废记录
// 参数：index - 序号，record - 作废记录
// 返回值：Excel行数据
// 字段顺序：序号、文件编号、文件类别、作废文件数、拟定作废日期、申请人、申请日期、审核人、批准人、状态
func (e *DeprecationRecordExporter) convertToExcelRow(index int, record types.DeprecateApplication) []string {
	return []string{
		strconv.Itoa(index), // 序号
		e.getDocumentModuleName(record.DocumentModuleType), // 文件编号（这里显示文件模块类型）
		record.DocumentCategoryName,                        // 文件类别
		strconv.Itoa(int(record.DeprecateDocumentCount)),   // 作废文件数
		e.formatDate(record.PlannedDeprecateDate),          // 拟定作废日期
		record.Applicant,                       // 申请人
		e.formatDate(record.ApplyDate),         // 申请日期
		e.getReviewerName(record.ApprovalInfo), // 审核人
		e.getApproverName(record.ApprovalInfo), // 批准人
		e.getStatusName(record.Status),         // 状态
	}
}

// getDocumentModuleName 获取文档模块名称
// 功能：将文档模块类型转换为中文名称
// 参数：moduleType - 模块类型
// 返回值：模块名称
func (e *DeprecationRecordExporter) getDocumentModuleName(moduleType int32) string {
	switch moduleType {
	case 1:
		return "书籍库"
	case 2:
		return "内部文档"
	case 3:
		return "外部文档"
	default:
		return "未知"
	}
}

// getStatusName 获取状态名称
// 功能：将状态代码转换为中文名称
// 参数：status - 状态代码
// 返回值：状态名称
func (e *DeprecationRecordExporter) getStatusName(status int32) string {
	switch status {
	case 1:
		return "待提交"
	case 2:
		return "审批中"
	case 3:
		return "已审批"
	case 4:
		return "已驳回"
	default:
		return "未知"
	}
}

// formatDate 格式化日期
// 功能：将时间戳转换为日期字符串
// 参数：timestamp - 时间戳（毫秒）
// 返回值：格式化后的日期字符串
func (e *DeprecationRecordExporter) formatDate(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.UnixMilli(timestamp).Format("2006-01-02")
}

// getReviewerName 获取审核人姓名
// 功能：从审批信息中提取审核人姓名
// 参数：approvalInfo - 审批信息
// 返回值：审核人姓名
func (e *DeprecationRecordExporter) getReviewerName(approvalInfo types.ApprovalInfo) string {
	if len(approvalInfo.Auditors) > 0 {
		return approvalInfo.Auditors[0].UserNickname
	}
	return ""
}

// getApproverName 获取批准人姓名
// 功能：从审批信息中提取批准人姓名
// 参数：approvalInfo - 审批信息
// 返回值：批准人姓名
func (e *DeprecationRecordExporter) getApproverName(approvalInfo types.ApprovalInfo) string {
	if len(approvalInfo.Approvers) > 0 {
		return approvalInfo.Approvers[0].UserNickname
	}
	return ""
}

// GetTpl 获取导出模板路径
// 功能：返回作废记录导出的Excel模板文件路径
// 参数：ctx - 上下文
// 返回值：tplPath - 模板路径，err - 错误信息
func (e *DeprecationRecordExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/deprecated_export.xlsx", nil
}
