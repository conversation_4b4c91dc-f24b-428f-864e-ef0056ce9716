package doclib_exporter

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

// ExternalDeprecatedExporter 外部作废库导出器
// 实现 ExporterAbility 接口，提供外部作废文档导出功能
type ExternalDeprecatedExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

// NewExternalDeprecatedExporter 创建外部作废库导出器实例
// 功能：初始化外部作废库导出器，设置服务上下文和基础导出能力
// 参数：svcCtx - 服务上下文
// 返回值：ExporterAbility - 导出器接口实例
func NewExternalDeprecatedExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &ExternalDeprecatedExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

// GetExportModelInfo 获取导出模块信息
// 功能：生成外部作废库导出的文件名和模块名称
// 参数：ctx - 上下文
// 返回值：fileName - 文件名，moduleName - 模块名称，err - 错误信息
func (e *ExternalDeprecatedExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	// 获取当前用户昵称
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	
	// 生成文件名：外部作废库-用户昵称-时间戳.xlsx
	fileName = "外部作废库-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx"
	moduleName = "文件管理-外部作废库"
	
	return fileName, moduleName, nil
}

// GetData 获取外部作废文档数据
// 功能：调用外部作废文档查询接口获取数据，并转换为Excel导出格式
// 参数：ctx - 上下文，req - 查询请求参数
// 返回值：[][]string - Excel数据行，error - 错误信息
// 异常：JSON序列化失败、查询接口调用失败等
func (e *ExternalDeprecatedExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	// 实现步骤：
	// 1. 将请求参数转换为外部作废文档查询请求
	// 2. 调用外部作废文档查询逻辑获取数据
	// 3. 将查询结果转换为Excel格式的二维字符串数组
	// 4. 返回转换后的数据

	// 序列化请求参数
	jsonReq, err := json.Marshal(req)
	if err != nil {
		logc.Errorf(ctx, "序列化外部作废库导出请求失败: %v", err)
		return nil, err
	}

	// 反序列化为外部作废文档查询请求
	var externalReq types.GetExternalDeprecatedDocumentsReq
	err = json.Unmarshal(jsonReq, &externalReq)
	if err != nil {
		logc.Errorf(ctx, "反序列化外部作废文档查询请求失败: %v", err)
		return nil, err
	}

	// 设置为不分页，获取所有数据
	externalReq.NoPage = true

	// 调用外部作废文档查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(e.svcCtx)
	resp, err := queryService.GetExternalDeprecatedDocuments(ctx, &externalReq)
	if err != nil {
		logc.Errorf(ctx, "查询外部作废文档失败: %v", err)
		return nil, err
	}

	// 如果没有数据，返回空数组
	if len(resp.Data) == 0 {
		return [][]string{}, nil
	}

	// 转换为Excel格式数据
	data := make([][]string, len(resp.Data))
	for i, document := range resp.Data {
		data[i] = e.convertToExcelRow(i+1, document)
	}

	return data, nil
}

// convertToExcelRow 将外部作废文档转换为Excel行数据
// 功能：按照表格字段顺序转换单条外部作废文档
// 参数：index - 序号，document - 外部作废文档
// 返回值：Excel行数据
// 字段顺序：序号、文档编号、文档名称、文档类型、首次发布日期、首次实施日期、最后作废日期、作废版本数
func (e *ExternalDeprecatedExporter) convertToExcelRow(index int, document types.ExternalDeprecatedDocument) []string {
	return []string{
		strconv.Itoa(index),                                    // 序号
		document.Number,                                        // 文档编号
		document.Name,                                          // 文档名称
		document.DocType,                                       // 文档类型
		e.formatDate(document.FirstPublishDate),                // 首次发布日期
		e.formatDate(document.FirstEffectiveDate),              // 首次实施日期
		e.formatDate(document.LastDeprecatedDate),              // 最后作废日期
		strconv.Itoa(int(document.DeprecatedVersionCount)),     // 作废版本数
	}
}

// formatDate 格式化日期
// 功能：将时间戳转换为日期字符串
// 参数：timestamp - 时间戳（毫秒）
// 返回值：格式化后的日期字符串
func (e *ExternalDeprecatedExporter) formatDate(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.UnixMilli(timestamp).Format("2006-01-02")
}

// GetTpl 获取导出模板路径
// 功能：返回外部作废库导出的Excel模板文件路径
// 参数：ctx - 上下文
// 返回值：tplPath - 模板路径，err - 错误信息
func (e *ExternalDeprecatedExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/external_deprecated_export.xlsx", nil
}
