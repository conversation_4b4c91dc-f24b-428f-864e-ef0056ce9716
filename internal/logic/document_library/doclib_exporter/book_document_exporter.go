package doclib_exporter

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"strconv"
	"time"
)

type BookDocumentExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

func NewBookDocumentExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &BookDocumentExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

func (e *BookDocumentExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	return "书籍库-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx", "文件管理-书籍库", nil
}

func (e *BookDocumentExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	jsonReq, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	var r types.GetBookListReq
	err = json.Unmarshal(jsonReq, &r)
	if err != nil {
		return nil, err
	}
	orgId := utils.GetContextOrganizationID(ctx)
	bookInfoResp, err := docvault.NewBookClient(e.svcCtx.DocvaultRpcConn).GetBookList(ctx, &docvault.GetBookListReq{
		PageInfo: &docvault.PageInfo{
			NoPage: true,
		},
		Number:            r.Number,
		Name:              r.Name,
		Author:            r.Author,
		Publisher:         r.Publisher,
		DictionaryNodeIds: r.DictionaryNodeIds,
		OnBorrow:          r.OnBorrow,
		OrganizationId:    orgId,
	})
	if err != nil {
		return nil, err
	}
	if len(bookInfoResp.Data) == 0 {
		return [][]string{}, nil
	}
	data := make([][]string, len(bookInfoResp.Data))
	for i, v := range bookInfoResp.Data {
		data[i] = []string{
			v.Number,
			v.Name,
			v.Author,
			v.Publisher,
			v.BookType,
			intToString(v.RegisterCount),
			intToString(v.ReceiveCount),
			intToString(v.BorrowCount),
			boolToBorrowStatus(v.OnBorrow),
			intToString(v.SurplusCount),
			v.Remark,
		}
	}
	return data, nil
}

func (e *BookDocumentExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/book_document_export.xlsx", nil // 可根据需要新建书籍专用模板
}

func intToString(i int32) string {
	return strconv.Itoa(int(i))
}

func boolToBorrowStatus(b bool) string {
	if b {
		return "借出中"
	}
	return "未借出"
}
