package doclib_exporter

import (
	"context"
	"testing"

	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

// TestInternalDeprecatedExporter_GetExportModelInfo 测试获取导出模块信息
func TestInternalDeprecatedExporter_GetExportModelInfo(t *testing.T) {
	Convey("测试获取内部作废库导出模块信息", t, func() {
		// 跳过这个测试，因为它需要完整的服务上下文
		// 在实际使用中，这个方法会通过 NewInternalDeprecatedExporter 正确初始化
		t.Skip("需要完整的服务上下文，跳过此测试")
	})
}

// TestInternalDeprecatedExporter_FormatDate 测试日期格式化
func TestInternalDeprecatedExporter_FormatDate(t *testing.T) {
	Convey("测试日期格式化", t, func() {
		exporter := &InternalDeprecatedExporter{}

		Convey("正常时间戳", func() {
			// 2023-01-01 00:00:00 的时间戳（毫秒）
			timestamp := int64(1672531200000)
			result := exporter.formatDate(timestamp)
			So(result, ShouldEqual, "2023-01-01")
		})

		Convey("零时间戳", func() {
			result := exporter.formatDate(0)
			So(result, ShouldEqual, "")
		})
	})
}

// TestInternalDeprecatedExporter_ConvertToExcelRow 测试转换为Excel行数据
func TestInternalDeprecatedExporter_ConvertToExcelRow(t *testing.T) {
	Convey("测试转换为Excel行数据", t, func() {
		exporter := &InternalDeprecatedExporter{}

		document := types.InternalDeprecatedDocument{
			ID:                     "doc_001",
			DocumentNo:             "INT-001",
			DocumentName:           "内部作废文档测试",
			DocumentCategoryName:   "管理制度",
			FirstPublishDate:       1672531200000, // 2023-01-01
			FirstImplementDate:     1672617600000, // 2023-01-02
			LastDeprecatedDate:     1672704000000, // 2023-01-03
			DeprecatedVersionCount: 3,
		}

		result := exporter.convertToExcelRow(1, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "1")                    // 序号
		So(result[1], ShouldEqual, "INT-001")              // 文档编号
		So(result[2], ShouldEqual, "内部作废文档测试")        // 文档名称
		So(result[3], ShouldEqual, "管理制度")              // 文档类别
		So(result[4], ShouldEqual, "2023-01-01")           // 首次发布日期
		So(result[5], ShouldEqual, "2023-01-02")           // 首次实施日期
		So(result[6], ShouldEqual, "2023-01-03")           // 最后作废日期
		So(result[7], ShouldEqual, "3")                    // 作废版本数
	})
}

// TestInternalDeprecatedExporter_ConvertToExcelRowWithEmptyDates 测试空日期的转换
func TestInternalDeprecatedExporter_ConvertToExcelRowWithEmptyDates(t *testing.T) {
	Convey("测试空日期的Excel行数据转换", t, func() {
		exporter := &InternalDeprecatedExporter{}

		document := types.InternalDeprecatedDocument{
			ID:                     "doc_002",
			DocumentNo:             "INT-002",
			DocumentName:           "无日期文档",
			DocumentCategoryName:   "技术标准",
			FirstPublishDate:       0, // 空日期
			FirstImplementDate:     0, // 空日期
			LastDeprecatedDate:     0, // 空日期
			DeprecatedVersionCount: 1,
		}

		result := exporter.convertToExcelRow(2, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "2")                    // 序号
		So(result[1], ShouldEqual, "INT-002")              // 文档编号
		So(result[2], ShouldEqual, "无日期文档")             // 文档名称
		So(result[3], ShouldEqual, "技术标准")              // 文档类别
		So(result[4], ShouldEqual, "")                     // 首次发布日期（空）
		So(result[5], ShouldEqual, "")                     // 首次实施日期（空）
		So(result[6], ShouldEqual, "")                     // 最后作废日期（空）
		So(result[7], ShouldEqual, "1")                    // 作废版本数
	})
}

// TestInternalDeprecatedExporter_GetTpl 测试获取模板路径
func TestInternalDeprecatedExporter_GetTpl(t *testing.T) {
	Convey("测试获取模板路径", t, func() {
		exporter := &InternalDeprecatedExporter{}
		
		tplPath, err := exporter.GetTpl(context.Background())
		
		So(err, ShouldBeNil)
		So(tplPath, ShouldEqual, "data/template/internal_deprecated_export.xlsx")
	})
}

// TestInternalDeprecatedExporter_ConvertToExcelRowMultiple 测试多条数据转换
func TestInternalDeprecatedExporter_ConvertToExcelRowMultiple(t *testing.T) {
	Convey("测试多条数据的Excel行转换", t, func() {
		exporter := &InternalDeprecatedExporter{}

		documents := []types.InternalDeprecatedDocument{
			{
				ID:                     "doc_001",
				DocumentNo:             "INT-001",
				DocumentName:           "文档1",
				DocumentCategoryName:   "管理制度",
				FirstPublishDate:       1672531200000,
				FirstImplementDate:     1672617600000,
				LastDeprecatedDate:     1672704000000,
				DeprecatedVersionCount: 2,
			},
			{
				ID:                     "doc_002",
				DocumentNo:             "INT-002",
				DocumentName:           "文档2",
				DocumentCategoryName:   "技术标准",
				FirstPublishDate:       1672790400000,
				FirstImplementDate:     1672876800000,
				LastDeprecatedDate:     1672963200000,
				DeprecatedVersionCount: 1,
			},
		}

		// 转换第一条数据
		result1 := exporter.convertToExcelRow(1, documents[0])
		So(result1[0], ShouldEqual, "1")
		So(result1[1], ShouldEqual, "INT-001")
		So(result1[2], ShouldEqual, "文档1")
		So(result1[3], ShouldEqual, "管理制度")
		So(result1[7], ShouldEqual, "2")

		// 转换第二条数据
		result2 := exporter.convertToExcelRow(2, documents[1])
		So(result2[0], ShouldEqual, "2")
		So(result2[1], ShouldEqual, "INT-002")
		So(result2[2], ShouldEqual, "文档2")
		So(result2[3], ShouldEqual, "技术标准")
		So(result2[7], ShouldEqual, "1")
	})
}

// TestInternalDeprecatedExporter_ConvertToExcelRowWithLongText 测试长文本的转换
func TestInternalDeprecatedExporter_ConvertToExcelRowWithLongText(t *testing.T) {
	Convey("测试长文本的Excel行数据转换", t, func() {
		exporter := &InternalDeprecatedExporter{}

		document := types.InternalDeprecatedDocument{
			ID:                     "doc_003",
			DocumentNo:             "INT-003-VERY-LONG-NUMBER",
			DocumentName:           "这是一个非常长的内部文档名称，用于测试Excel导出时长文本的处理能力和字段截断",
			DocumentCategoryName:   "企业管理标准化技术规范文档",
			FirstPublishDate:       1672531200000,
			FirstImplementDate:     1672617600000,
			LastDeprecatedDate:     1672704000000,
			DeprecatedVersionCount: 10,
		}

		result := exporter.convertToExcelRow(3, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "3")
		So(result[1], ShouldEqual, "INT-003-VERY-LONG-NUMBER")
		So(result[2], ShouldEqual, "这是一个非常长的内部文档名称，用于测试Excel导出时长文本的处理能力和字段截断")
		So(result[3], ShouldEqual, "企业管理标准化技术规范文档")
		So(result[7], ShouldEqual, "10")
	})
}

// TestInternalDeprecatedExporter_ConvertToExcelRowWithEmptyCategory 测试空类别的转换
func TestInternalDeprecatedExporter_ConvertToExcelRowWithEmptyCategory(t *testing.T) {
	Convey("测试空类别的Excel行数据转换", t, func() {
		exporter := &InternalDeprecatedExporter{}

		document := types.InternalDeprecatedDocument{
			ID:                     "doc_004",
			DocumentNo:             "INT-004",
			DocumentName:           "无类别文档",
			DocumentCategoryName:   "", // 空类别
			FirstPublishDate:       1672531200000,
			FirstImplementDate:     1672617600000,
			LastDeprecatedDate:     1672704000000,
			DeprecatedVersionCount: 1,
		}

		result := exporter.convertToExcelRow(4, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "4")
		So(result[1], ShouldEqual, "INT-004")
		So(result[2], ShouldEqual, "无类别文档")
		So(result[3], ShouldEqual, "")                     // 空类别
		So(result[4], ShouldEqual, "2023-01-01")
		So(result[5], ShouldEqual, "2023-01-02")
		So(result[6], ShouldEqual, "2023-01-03")
		So(result[7], ShouldEqual, "1")
	})
}
