package doclib_exporter

import (
	"context"
	"testing"

	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

// TestExternalDeprecatedExporter_GetExportModelInfo 测试获取导出模块信息
func TestExternalDeprecatedExporter_GetExportModelInfo(t *testing.T) {
	Convey("测试获取外部作废库导出模块信息", t, func() {
		// 跳过这个测试，因为它需要完整的服务上下文
		// 在实际使用中，这个方法会通过 NewExternalDeprecatedExporter 正确初始化
		t.Skip("需要完整的服务上下文，跳过此测试")
	})
}

// TestExternalDeprecatedExporter_FormatDate 测试日期格式化
func TestExternalDeprecatedExporter_FormatDate(t *testing.T) {
	Convey("测试日期格式化", t, func() {
		exporter := &ExternalDeprecatedExporter{}

		Convey("正常时间戳", func() {
			// 2023-01-01 00:00:00 的时间戳（毫秒）
			timestamp := int64(1672531200000)
			result := exporter.formatDate(timestamp)
			So(result, ShouldEqual, "2023-01-01")
		})

		Convey("零时间戳", func() {
			result := exporter.formatDate(0)
			So(result, ShouldEqual, "")
		})
	})
}

// TestExternalDeprecatedExporter_ConvertToExcelRow 测试转换为Excel行数据
func TestExternalDeprecatedExporter_ConvertToExcelRow(t *testing.T) {
	Convey("测试转换为Excel行数据", t, func() {
		exporter := &ExternalDeprecatedExporter{}

		document := types.ExternalDeprecatedDocument{
			ID:                     "doc_001",
			Number:                 "EXT-001",
			Name:                   "外部作废文档测试",
			DocType:                "标准",
			FirstPublishDate:       1672531200000, // 2023-01-01
			FirstEffectiveDate:     1672617600000, // 2023-01-02
			LastDeprecatedDate:     1672704000000, // 2023-01-03
			DeprecatedVersionCount: 3,
		}

		result := exporter.convertToExcelRow(1, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "1")                    // 序号
		So(result[1], ShouldEqual, "EXT-001")              // 文档编号
		So(result[2], ShouldEqual, "外部作废文档测试")        // 文档名称
		So(result[3], ShouldEqual, "标准")                  // 文档类型
		So(result[4], ShouldEqual, "2023-01-01")           // 首次发布日期
		So(result[5], ShouldEqual, "2023-01-02")           // 首次实施日期
		So(result[6], ShouldEqual, "2023-01-03")           // 最后作废日期
		So(result[7], ShouldEqual, "3")                    // 作废版本数
	})
}

// TestExternalDeprecatedExporter_ConvertToExcelRowWithEmptyDates 测试空日期的转换
func TestExternalDeprecatedExporter_ConvertToExcelRowWithEmptyDates(t *testing.T) {
	Convey("测试空日期的Excel行数据转换", t, func() {
		exporter := &ExternalDeprecatedExporter{}

		document := types.ExternalDeprecatedDocument{
			ID:                     "doc_002",
			Number:                 "EXT-002",
			Name:                   "无日期文档",
			DocType:                "规范",
			FirstPublishDate:       0, // 空日期
			FirstEffectiveDate:     0, // 空日期
			LastDeprecatedDate:     0, // 空日期
			DeprecatedVersionCount: 1,
		}

		result := exporter.convertToExcelRow(2, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "2")                    // 序号
		So(result[1], ShouldEqual, "EXT-002")              // 文档编号
		So(result[2], ShouldEqual, "无日期文档")             // 文档名称
		So(result[3], ShouldEqual, "规范")                  // 文档类型
		So(result[4], ShouldEqual, "")                     // 首次发布日期（空）
		So(result[5], ShouldEqual, "")                     // 首次实施日期（空）
		So(result[6], ShouldEqual, "")                     // 最后作废日期（空）
		So(result[7], ShouldEqual, "1")                    // 作废版本数
	})
}

// TestExternalDeprecatedExporter_GetTpl 测试获取模板路径
func TestExternalDeprecatedExporter_GetTpl(t *testing.T) {
	Convey("测试获取模板路径", t, func() {
		exporter := &ExternalDeprecatedExporter{}
		
		tplPath, err := exporter.GetTpl(context.Background())
		
		So(err, ShouldBeNil)
		So(tplPath, ShouldEqual, "data/template/external_deprecated_export.xlsx")
	})
}

// TestExternalDeprecatedExporter_ConvertToExcelRowMultiple 测试多条数据转换
func TestExternalDeprecatedExporter_ConvertToExcelRowMultiple(t *testing.T) {
	Convey("测试多条数据的Excel行转换", t, func() {
		exporter := &ExternalDeprecatedExporter{}

		documents := []types.ExternalDeprecatedDocument{
			{
				ID:                     "doc_001",
				Number:                 "EXT-001",
				Name:                   "文档1",
				DocType:                "标准",
				FirstPublishDate:       1672531200000,
				FirstEffectiveDate:     1672617600000,
				LastDeprecatedDate:     1672704000000,
				DeprecatedVersionCount: 2,
			},
			{
				ID:                     "doc_002",
				Number:                 "EXT-002",
				Name:                   "文档2",
				DocType:                "规范",
				FirstPublishDate:       1672790400000,
				FirstEffectiveDate:     1672876800000,
				LastDeprecatedDate:     1672963200000,
				DeprecatedVersionCount: 1,
			},
		}

		// 转换第一条数据
		result1 := exporter.convertToExcelRow(1, documents[0])
		So(result1[0], ShouldEqual, "1")
		So(result1[1], ShouldEqual, "EXT-001")
		So(result1[2], ShouldEqual, "文档1")
		So(result1[7], ShouldEqual, "2")

		// 转换第二条数据
		result2 := exporter.convertToExcelRow(2, documents[1])
		So(result2[0], ShouldEqual, "2")
		So(result2[1], ShouldEqual, "EXT-002")
		So(result2[2], ShouldEqual, "文档2")
		So(result2[7], ShouldEqual, "1")
	})
}

// TestExternalDeprecatedExporter_ConvertToExcelRowWithLongText 测试长文本的转换
func TestExternalDeprecatedExporter_ConvertToExcelRowWithLongText(t *testing.T) {
	Convey("测试长文本的Excel行数据转换", t, func() {
		exporter := &ExternalDeprecatedExporter{}

		document := types.ExternalDeprecatedDocument{
			ID:                     "doc_003",
			Number:                 "EXT-003-VERY-LONG-NUMBER",
			Name:                   "这是一个非常长的文档名称，用于测试Excel导出时长文本的处理能力",
			DocType:                "技术规范标准文档",
			FirstPublishDate:       1672531200000,
			FirstEffectiveDate:     1672617600000,
			LastDeprecatedDate:     1672704000000,
			DeprecatedVersionCount: 10,
		}

		result := exporter.convertToExcelRow(3, document)

		So(len(result), ShouldEqual, 8)
		So(result[0], ShouldEqual, "3")
		So(result[1], ShouldEqual, "EXT-003-VERY-LONG-NUMBER")
		So(result[2], ShouldEqual, "这是一个非常长的文档名称，用于测试Excel导出时长文本的处理能力")
		So(result[3], ShouldEqual, "技术规范标准文档")
		So(result[7], ShouldEqual, "10")
	})
}
