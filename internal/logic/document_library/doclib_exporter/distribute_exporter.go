package doclib_exporter

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
)

// InternalDocumentExporter 采用模板方法模式实现内部文件列表导出能力
// 只重写需要自定义的钩子方法，其余复用通用导出流程

type DistributeExporter struct {
	req    interface{}
	svcCtx *svc.ServiceContext
	ExporterAbility
}

func NewDistributeExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &DistributeExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

// 钩子方法：获取导出模块信息
func (l *DistributeExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	nickname := addons.NewQuickNameTranslatorImpl(l.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	return "发放回收和处置记录-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx", "文件管理-发放回收和处置", nil
}

// 钩子方法：获取数据
func (l *DistributeExporter) GetData(ctx context.Context, req any) ([][]string, error) {

	// 格式化req
	jsonReq, err := json.Marshal(l.req)
	if err != nil {
		return nil, err
	}
	var r types.GetDistributeListReq
	err = json.Unmarshal(jsonReq, &r)
	if err != nil {
		return nil, err
	}
	// 使用 Applicant 模糊搜索申请人ids，如果登录用户是普通文件用户，则只差当前用户的申请记录
	applicant, err := l.getApplicant(ctx, r.Applicant)
	if err != nil {
		return nil, err
	}
	// 获取发放列表
	list, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDistributeInfoList(ctx, &docvault.GetDistributeListReq{
		PageInfo: &docvault.PageInfo{
			NoPage: true,
		},
		FileNumber:     r.FileNumber,
		FileName:       r.FileName,
		FileType:       int32(r.FileType),
		FileCategory:   r.FileCategory,
		DistributeType: int32(r.DistributeType),
		Status:         int32(r.Status),
		Applicant:      applicant,
	})
	if err != nil {
		logc.Errorf(ctx, "获取发放信息列表失败: %v", err)
		return nil, err
	}
	resp := l.dataTransition(list)

	data := make([][]string, len(resp.Data))
	for i, v := range resp.Data {
		data[i] = []string{
			l.getDistributeType(v.DistributeType),
			l.getFileType(v.FileType),
			v.FileCategory,
			time.UnixMilli(v.ApplyDate).Format(time.DateOnly),
			time.UnixMilli(v.ApplyDate).Format(time.DateOnly),
			l.getApprovalNames(v.ApprovalInfo.Auditors),
			l.getApprovalNames(v.ApprovalInfo.Approvers),
			fmt.Sprintf("%d", v.DistributeCount),
			fmt.Sprintf("%d", v.ReceivedCount),
			fmt.Sprintf("%d", v.DisposalCount),
			l.getStatus(v.Status),
		}
	}

	return data, nil
}
func (l *DistributeExporter) getDistributeType(disType int) string {
	switch disType {
	case 1:
		return "内部发放"
	case 2:
		return "外部发放"
	}
	return ""
}

// 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
func (l *DistributeExporter) getStatus(status int) string {
	switch status {
	case 1:
		return "待提交"
	case 2:
		return "待审批"
	case 3:
		return "已审批"
	case 4:
		return "已驳回"
	}
	return ""
}

func (l *DistributeExporter) getApprovalNames(as []types.Approval) string {
	if len(as) == 0 {
		return ""
	}
	var names []string
	for _, v := range as {
		names = append(names, v.UserNickname)
	}
	return strings.Join(names, ",")
}

func (l *DistributeExporter) getFileType(tp int) string {
	switch tp {
	case 1:
		return "内部文件"
	case 2:
		return "外部文件"
	}
	return ""
}

// 钩子方法：获取模板路径
func (l *DistributeExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/distribute_export.xlsx", nil
}

func (l *DistributeExporter) getApplicant(ctx context.Context, userName string) ([]string, error) {
	var ids []string
	if userName != "" {
		ids = append(ids, "1")
		users, err := l.svcCtx.PhoenixClient.GetUserInfoByNickname(ctx, userName)
		if err != nil {
			return nil, err
		}
		for _, user := range users {
			ids = append(ids, user.ID)
		}
	}
	userLoginInfo := utils.GetCurrentLoginUser(ctx)
	// 判断是否子公司普通用户
	code, err := l.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userLoginInfo.UserId, "ZGSWJYH")
	if err != nil {
		return nil, err
	}
	// 如果是，只查出自己的申请记录
	if code {
		ids = []string{userLoginInfo.UserId}
		return ids, nil
	}
	// 判断是否子公司普通用户
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userLoginInfo.UserId, "JTWJYH")
	if err != nil {
		return nil, err
	}
	// 如果是，只查出自己的申请记录
	if code {
		ids = []string{userLoginInfo.UserId}
		return ids, nil
	}
	// 判断是否集团文件管理员
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userLoginInfo.UserId, "JTWJGLY")
	if err != nil {
		return nil, err
	}
	// 如果是，根据搜索条件查
	if code {
		return ids, nil
	}
	// 判断是否子公司文件管理员
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userLoginInfo.UserId, "ZGSWJGLY")
	if err != nil {

		return nil, err
	}
	if code {
		return ids, nil
	}
	return nil, errors.New("当前用户不是文件用户")
}

func (l *DistributeExporter) dataTransition(list *docvault.GetDistributeListResp) *types.GetDistributeListResp {
	var getDistributeListInfo []types.GetDistributeListInfo
	for _, v := range list.Data {
		distributeCount := 0                       // 发放份数
		approvalInfo := types.ApprovalInfo{}       // 审批人
		recycleStatus := ""                        // 回收状态
		var received []types.DistributeUser        // 已签收人
		var notReceived []types.DistributeUser     // 未签收人
		var recycle []types.DistributeUser         // 已回收人
		var disposalBy []types.DistributeUser      // 已处置人
		var distributeUsers []types.DistributeUser // 用户汇总
		// 已审批，获取接收人信息
		if v.Status == 3 {
			distributeCount = int(v.DistributeCount)
			// 已签收
			for _, user := range v.Received {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   1,
				}
				received = append(received, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 未签收
			for _, user := range v.NotReceived {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   2,
				}
				notReceived = append(notReceived, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 已回收
			for _, user := range v.Recycle {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   3,
				}
				recycle = append(recycle, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 已处置
			for _, user := range v.DisposalBy {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   4,
				}
				disposalBy = append(disposalBy, temp)
				distributeUsers = append(distributeUsers, temp)
			}

			if len(recycle) == 0 {
				recycleStatus = "未回收"
			} else if len(recycle) > 0 && len(recycle) < int(v.DistributeCount) {
				recycleStatus = "部分回收"
			} else {
				recycleStatus = "已回收"
			}
		}
		// 已审批和已驳回，获取审批人
		if v.Status == 4 || v.Status == 3 {
			if v.ApprovalInfo != nil {
				for _, r := range v.ApprovalInfo.Approvers {
					approvalInfo.Approvers = append(approvalInfo.Approvers, types.Approval{
						UserID:       r.UserId,
						PassedDate:   r.PassedDate,
						UserNickname: r.Nickname,
					})
				}
				for _, r := range v.ApprovalInfo.Auditors {
					approvalInfo.Auditors = append(approvalInfo.Auditors, types.Approval{
						UserID:       r.UserId,
						PassedDate:   r.PassedDate,
						UserNickname: r.Nickname,
					})
				}
			}
		}
		getDistributeListInfo = append(getDistributeListInfo, types.GetDistributeListInfo{
			ID:                 v.Id,
			Applicant:          v.Applicant,
			ApplyDate:          v.ApplyDate,
			DistributeType:     int(v.DistributeType),
			FileType:           int(v.FileType),
			FileCategory:       v.FileCategory,
			Reason:             v.Reason,
			OtherReason:        v.OtherReason,
			WishDistributeDate: v.WishDistributeDate,
			Status:             int(v.Status),
			WorkflowID:         v.WorkflowId,
			ApprovalInfo:       approvalInfo,
			DistributeCount:    distributeCount,
			DisposalCount:      len(disposalBy),
			ReceivedCount:      len(received),
			DistributeUsers:    distributeUsers,
			RecycleStatus:      recycleStatus,
		})
	}
	return &types.GetDistributeListResp{
		Data: getDistributeListInfo,
	}
}
