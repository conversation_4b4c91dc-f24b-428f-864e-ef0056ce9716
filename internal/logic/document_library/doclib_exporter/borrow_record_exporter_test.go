package doclib_exporter

import (
	"context"
	"testing"
	"time"

	"nebula/internal/svc"

	. "github.com/smartystreets/goconvey/convey"
)

// TestBorrowRecordExporter 测试借阅记录导出器
func TestBorrowRecordExporter(t *testing.T) {
	Convey("测试借阅记录导出器", t, func() {
		// 创建模拟服务上下文
		svcCtx := &svc.ServiceContext{}
		exporter := NewBorrowRecordExporter(svcCtx)

		Convey("测试获取导出模块信息", func() {
			// 由于需要依赖完整的服务上下文，这里只测试基本功能
			SkipConvey("需要完整的服务上下文支持", func() {})
		})

		Convey("测试获取模板路径", func() {
			ctx := context.Background()
			tplPath, err := exporter.GetTpl(ctx)

			So(err, ShouldBeNil)
			So(tplPath, ShouldEqual, "data/template/borrow_record_export.xlsx")
		})

		Convey("测试格式化函数", func() {
			Convey("测试时间范围格式化", func() {
				startTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC).UnixMilli()
				endTime := time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC).UnixMilli()
				result := formatTimeRange(startTime, endTime)
				So(result, ShouldEqual, "2025-01-01 至 2025-01-15")

				// 测试零值
				result = formatTimeRange(0, 0)
				So(result, ShouldEqual, "")
			})

			Convey("测试时间戳格式化", func() {
				timestamp := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC).UnixMilli()
				result := formatTimestamp(timestamp)
				So(result, ShouldEqual, "2025-01-01")

				// 测试零值
				result = formatTimestamp(0)
				So(result, ShouldEqual, "")
			})

			Convey("测试审批状态格式化", func() {
				So(formatApprovalStatus(1), ShouldEqual, "待提交")
				So(formatApprovalStatus(2), ShouldEqual, "待审批")
				So(formatApprovalStatus(3), ShouldEqual, "已驳回")
				So(formatApprovalStatus(4), ShouldEqual, "已审批")
				So(formatApprovalStatus(999), ShouldEqual, "未知状态")
			})
		})
	})
}

// TestBorrowRecordExporterInterface 测试借阅记录导出器接口实现
func TestBorrowRecordExporterInterface(t *testing.T) {
	Convey("测试借阅记录导出器接口实现", t, func() {
		svcCtx := &svc.ServiceContext{}
		exporter := NewBorrowRecordExporter(svcCtx)

		Convey("应该实现 ExporterAbility 接口", func() {
			// 验证接口方法存在
			So(exporter, ShouldImplement, (*ExporterAbility)(nil))
		})
	})
}