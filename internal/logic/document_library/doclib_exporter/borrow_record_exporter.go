package doclib_exporter

import (
	"context"
	"encoding/json"
	"fmt"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/query/borrowrecord"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
)

// BorrowRecordExporter 借阅记录导出器
// 实现 ExporterAbility 接口，提供借阅记录导出功能
type BorrowRecordExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

// NewBorrowRecordExporter 创建借阅记录导出器实例
// 功能：初始化借阅记录导出器，设置服务上下文和基础导出能力
// 参数：svcCtx - 服务上下文
// 返回值：ExporterAbility - 导出器接口实例
func NewBorrowRecordExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &BorrowRecordExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

// GetExportModelInfo 获取导出模块信息
// 功能：生成导出文件名和模块名称
// 参数：ctx - 上下文
// 返回值：fileName - 文件名，moduleName - 模块名，err - 错误信息
func (e *BorrowRecordExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	// 实现步骤：
	// 1. 获取当前用户昵称
	// 2. 生成带时间戳的文件名
	// 3. 返回模块信息

	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	fileName = "借阅记录-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx"
	moduleName = "文件管理-借阅记录"
	return fileName, moduleName, nil
}

// GetData 获取借阅记录数据
// 功能：调用借阅记录查询接口获取数据，并转换为Excel导出格式
// 参数：ctx - 上下文，req - 查询请求参数
// 返回值：[][]string - Excel数据行，error - 错误信息
// 异常：JSON序列化失败、查询接口调用失败等
func (e *BorrowRecordExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	// 实现步骤：
	// 1. 将请求参数转换为借阅记录查询请求
	// 2. 调用借阅记录查询逻辑获取数据
	// 3. 将查询结果转换为Excel格式的二维字符串数组
	// 4. 返回转换后的数据

	// 序列化请求参数
	jsonReq, err := json.Marshal(req)
	if err != nil {
		logc.Errorf(ctx, "序列化借阅记录导出请求失败: %v", err)
		return nil, err
	}

	// 反序列化为借阅记录查询请求
	var loanReq types.GetLoanRecordsReq
	err = json.Unmarshal(jsonReq, &loanReq)
	if err != nil {
		logc.Errorf(ctx, "反序列化借阅记录查询请求失败: %v", err)
		return nil, err
	}

	// 设置不分页，获取所有数据
	loanReq.NoPage = true

	// 调用借阅记录查询服务
	queryService := borrowrecord.NewBorrowRecordQueryService(e.svcCtx)
	loanResp, err := queryService.GetLoanRecords(ctx, &loanReq)
	if err != nil {
		logc.Errorf(ctx, "查询借阅记录失败: %v", err)
		return nil, err
	}

	// 如果没有数据，返回空数组
	if len(loanResp.Data) == 0 {
		return [][]string{}, nil
	}

	// 转换数据格式 - 根据用户修改后的模板表头调整数据列
	data := make([][]string, len(loanResp.Data))
	for i, record := range loanResp.Data {
		data[i] = []string{
			strconv.Itoa(record.BorrowDocumentsCount),          // A列：借阅文档数
			strconv.Itoa(record.RecoverDocumentsCount),         // B列：交还文档数
			formatTimeRange(record.BorrowTime, record.DueTime), // C列：借阅期限
			record.UserNickname,                                // D列：申请人
			formatTimestamp(record.ApprovalApplyTime),          // E列：申请日期
			strings.Join(record.Auditors, ","),                 // F列：审核人
			strings.Join(record.Approvers, ","),                // G列：批准人
			record.RecoverNames,                                // H列：回收人
			formatApprovalStatus(record.ApprovalStatus),        // I列：状态
		}
	}

	return data, nil
}

// GetTpl 获取导出模板路径
// 功能：返回借阅记录导出的Excel模板文件路径
// 参数：ctx - 上下文
// 返回值：tplPath - 模板路径，err - 错误信息
func (e *BorrowRecordExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/borrow_record_export.xlsx", nil
}

// formatTimeRange 格式化时间范围
// 功能：将借阅开始时间和结束时间格式化为"开始日期 至 结束日期"的格式
// 参数：startTime - 开始时间戳（毫秒），endTime - 结束时间戳（毫秒）
// 返回值：格式化后的时间范围字符串
func formatTimeRange(startTime, endTime int64) string {
	if startTime == 0 || endTime == 0 {
		return ""
	}
	startDate := time.UnixMilli(startTime).Format("2006-01-02")
	endDate := time.UnixMilli(endTime).Format("2006-01-02")
	return fmt.Sprintf("%s 至 %s", startDate, endDate)
}

// formatTimestamp 格式化时间戳
// 功能：将毫秒级时间戳格式化为日期字符串
// 参数：timestamp - 毫秒级时间戳
// 返回值：格式化后的日期字符串
func formatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.UnixMilli(timestamp).Format("2006-01-02")
}

// formatApprovalStatus 格式化审批状态
// 功能：将审批状态码转换为中文描述
// 参数：status - 审批状态码
// 返回值：中文状态描述
func formatApprovalStatus(status int) string {
	switch status {
	case 1:
		return "待提交"
	case 2:
		return "待审批"
	case 3:
		return "已审批"
	case 4:
		return "已驳回"
	default:
		return "未知状态"
	}
}

// formatBorrowReason 格式化借阅原因
// 功能：将借阅原因码转换为中文描述，如果是其他原因则显示具体内容
// 参数：reason - 借阅原因码，otherReason - 其他原因描述
// 返回值：格式化后的借阅原因
func formatBorrowReason(reason int32, otherReason string) string {
	switch reason {
	case 1:
		return "工作需要"
	case 2:
		return "学习研究"
	case 3:
		return "项目参考"
	case 4:
		return "培训使用"
	case 5:
		if otherReason != "" {
			return fmt.Sprintf("其他：%s", otherReason)
		}
		return "其他"
	default:
		return "未知原因"
	}
}
