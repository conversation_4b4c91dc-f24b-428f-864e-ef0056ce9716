package document_library

import (
	"context"
	"fmt"
	"sort"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocumentDistributeRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDocumentDistributeRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocumentDistributeRecordsLogic {
	return &GetDocumentDistributeRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDocumentDistributeRecords 获取文档发放回收记录
// 功能: 根据文档ID分页查询发放回收记录，只显示通过审批的数据
// 参数:
//   - req: 请求参数，包含文档ID和分页信息
//
// 返回值:
//   - resp: 发放回收记录列表响应
//   - err: 错误信息
func (l *GetDocumentDistributeRecordsLogic) GetDocumentDistributeRecords(req *types.GetDocumentDistributeRecordsReq) (resp *types.GetDocumentDistributeRecordsResp, err error) {
	// 1. 查询发放记录文件
	distributeFiles, err := l.queryDistributeFiles(req.DocumentID)
	if err != nil {
		return nil, err
	}

	if len(distributeFiles) == 0 {
		return l.buildEmptyResponse(req), nil
	}

	// 2. 查询发放记录
	records, _, err := l.queryDistributeRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 3. 查询权限记录
	permissions, err := l.queryPermissions(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 4. 查询回收记录
	recycleRecordMap, recyclePermissionMap, err := l.queryRecycleRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 5. 构建权限变更记录映射
	permissionMap := l.buildPermissionChangeMap(permissions, distributeFiles)

	// 6. 构建响应数据
	distributeRecords := l.buildRecords(records, permissionMap, recycleRecordMap, recyclePermissionMap)

	// 6. 排序和分页
	l.sortRecordsByApplyTimeDesc(distributeRecords)
	pagedRecords, total := l.applyPagination(distributeRecords, req.Page, req.PageSize)

	return &types.GetDocumentDistributeRecordsResp{
		Data: pagedRecords,
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// PermissionChangeRecord 权限变更记录
type PermissionChangeRecord struct {
	InternalElectronicQuery    []types.InternalElectronicQueryRecord    // 内发：电子文件-查询权限变更记录列表
	InternalElectronicDownload []types.InternalElectronicDownloadRecord // 内发：电子文件-查询/下载权限变更记录列表
}

// buildPermissionChangeMap 构建权限变更记录映射
// 功能: 根据权限记录和文件记录构建权限变更记录映射
// 参数:
//   - permissions: 权限记录列表
//   - distributeFiles: 发放记录文件列表
//
// 返回值:
//   - map[string]PermissionChangeRecord: recordID -> 权限变更记录
func (l *GetDocumentDistributeRecordsLogic) buildPermissionChangeMap(permissions []mapper.DistributeRecordPermission, distributeFiles []mapper.DistributeRecordFile) map[string]PermissionChangeRecord {
	// 构建文件记录ID到发放记录ID的映射
	fileToRecordMap := l.buildFileToRecordMap(distributeFiles)

	// 按发放记录ID分组权限记录
	recordPermissionMap := l.groupPermissionsByRecordID(permissions, fileToRecordMap)

	// 构建权限变更记录
	result := make(map[string]PermissionChangeRecord)
	for recordID, perms := range recordPermissionMap {
		changeRecord := l.buildPermissionChangeRecord(perms)
		if l.isValidPermissionChangeRecord(changeRecord) {
			result[recordID] = changeRecord
		}
	}

	return result
}

// buildPermissionChangeRecord 构建单个权限变更记录
// 功能：将权限列表转换为权限变更记录，按权限类型分组
// 参数：perms - 权限记录列表
// 返回值：权限变更记录
func (l *GetDocumentDistributeRecordsLogic) buildPermissionChangeRecord(perms []mapper.DistributeRecordPermission) PermissionChangeRecord {
	changeRecord := PermissionChangeRecord{}

	// 按权限类型分组
	queryRecords := make([]types.InternalElectronicQueryRecord, 0)
	downloadRecords := make([]types.InternalElectronicDownloadRecord, 0)

	for _, perm := range perms {
		// 数据库查询时已经过滤了电子文件（FileForm=1）和权限类型（FilePermission=1,2）
		// 发放记录只显示签收和未签收状态（1-2）
		status := l.getDistributePermissionStatus(perm.SignForStatus)

		if perm.FilePermission == 1 { // 查询权限
			queryRecord := l.buildQueryRecord(perm, status)
			queryRecords = append(queryRecords, queryRecord)
		} else if perm.FilePermission == 2 { // 查询/下载权限
			downloadRecord := l.buildDownloadRecord(perm, status)
			downloadRecords = append(downloadRecords, downloadRecord)
		}
	}

	changeRecord.InternalElectronicQuery = queryRecords
	changeRecord.InternalElectronicDownload = downloadRecords

	return changeRecord
}

// buildQueryRecord 构建查询权限记录
// 功能：根据权限信息构建查询权限记录
// 参数：perm - 权限记录，status - 权限状态
// 返回值：查询权限记录
func (l *GetDocumentDistributeRecordsLogic) buildQueryRecord(perm mapper.DistributeRecordPermission, status int32) types.InternalElectronicQueryRecord {
	return types.InternalElectronicQueryRecord{
		UserID:       perm.UserID,
		UserNickname: perm.UserName,
		Status:       status,
	}
}

// buildDownloadRecord 构建下载权限记录
// 功能：根据权限信息构建下载权限记录
// 参数：perm - 权限记录，status - 权限状态
// 返回值：下载权限记录
func (l *GetDocumentDistributeRecordsLogic) buildDownloadRecord(perm mapper.DistributeRecordPermission, status int32) types.InternalElectronicDownloadRecord {
	return types.InternalElectronicDownloadRecord{
		UserID:       perm.UserID,
		UserNickname: perm.UserName,
		Status:       status,
	}
}

// isValidPermissionChangeRecord 检查权限变更记录是否有效
// 功能：检查权限变更记录是否包含有效的权限数据
// 参数：changeRecord - 权限变更记录
// 返回值：true表示有效，false表示无效
func (l *GetDocumentDistributeRecordsLogic) isValidPermissionChangeRecord(changeRecord PermissionChangeRecord) bool {
	return len(changeRecord.InternalElectronicQuery) > 0 || len(changeRecord.InternalElectronicDownload) > 0
}

// buildFileToRecordMap 构建文件记录ID到发放记录ID的映射
// 功能：为后续权限记录分组提供映射关系
// 参数：distributeFiles - 发放记录文件列表
// 返回值：文件记录ID到发放记录ID的映射
func (l *GetDocumentDistributeRecordsLogic) buildFileToRecordMap(distributeFiles []mapper.DistributeRecordFile) map[string]string {
	fileToRecordMap := make(map[string]string)
	for _, file := range distributeFiles {
		fileToRecordMap[file.ID] = file.RecordID
	}
	return fileToRecordMap
}

// groupPermissionsByRecordID 按发放记录ID分组权限记录
// 功能：将权限记录按发放记录ID进行分组，便于后续处理
// 参数：permissions - 权限记录列表，fileToRecordMap - 文件记录ID到发放记录ID的映射
// 返回值：发放记录ID到权限记录列表的映射
func (l *GetDocumentDistributeRecordsLogic) groupPermissionsByRecordID(permissions []mapper.DistributeRecordPermission, fileToRecordMap map[string]string) map[string][]mapper.DistributeRecordPermission {
	recordPermissionMap := make(map[string][]mapper.DistributeRecordPermission)
	for _, permission := range permissions {
		recordID := fileToRecordMap[permission.FileRecordID]
		if recordID != "" {
			recordPermissionMap[recordID] = append(recordPermissionMap[recordID], permission)
		}
	}
	return recordPermissionMap
}

// getDistributePermissionStatus 获取发放权限状态枚举
// 功能: 根据签收状态获取发放阶段的状态枚举值（只显示签收和未签收）
// 参数:
//   - signForStatus: 签收状态（1未签收 | 2已签收）
//
// 返回值:
//   - int32: 状态枚举（1-未签收 | 2-已签收）
func (l *GetDocumentDistributeRecordsLogic) getDistributePermissionStatus(signForStatus int32) int32 {
	if signForStatus == 2 { // 已签收
		return 2 // 已签收
	}
	return 1 // 未签收
}

// queryDistributeFiles 查询发放记录文件
func (l *GetDocumentDistributeRecordsLogic) queryDistributeFiles(documentID string) ([]mapper.DistributeRecordFile, error) {
	distributeRecordFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeFiles, err := distributeRecordFileClient.FindApprovedByFileID(l.ctx, documentID)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录失败")
	}
	return distributeFiles, nil
}

// buildEmptyResponse 构建空响应
func (l *GetDocumentDistributeRecordsLogic) buildEmptyResponse(req *types.GetDocumentDistributeRecordsReq) *types.GetDocumentDistributeRecordsResp {
	return &types.GetDocumentDistributeRecordsResp{
		Data: []types.DocumentDistributeRecord{},
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		},
	}
}

// queryDistributeRecords 查询发放记录
// 功能：批量查询已审批的发放记录，避免N+1查询和手动过滤
// 参数：distributeFiles - 发放记录文件列表
// 返回值：已审批的发放记录列表，文件记录映射，错误信息
func (l *GetDocumentDistributeRecordsLogic) queryDistributeRecords(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecord, map[string][]mapper.DistributeRecordFile, error) {
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	// 构建记录ID列表和文件记录映射
	recordIDs := make([]string, 0, len(distributeFiles))
	fileRecordMap := make(map[string][]mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		recordIDs = append(recordIDs, file.RecordID)
		if fileRecordMap[file.RecordID] == nil {
			fileRecordMap[file.RecordID] = make([]mapper.DistributeRecordFile, 0)
		}
		fileRecordMap[file.RecordID] = append(fileRecordMap[file.RecordID], file)
	}

	// 去重记录ID
	uniqueRecordIDs := utils.SliceDuplicate(recordIDs)

	// 批量查询已审批的发放记录（在数据库层面过滤 status = 3）
	records, err := distributeRecordClient.FindApprovedByIDs(l.ctx, uniqueRecordIDs)
	if err != nil {
		l.Logger.Errorf("批量查询已审批发放记录失败: %v", err)
		return nil, nil, fmt.Errorf("批量查询已审批发放记录失败")
	}

	return records, fileRecordMap, nil
}

// queryPermissions 查询权限记录
// 功能：查询内发电子文件的查询权限和查询/下载权限记录，在数据库层面进行过滤
// 参数：distributeFiles - 发放记录文件列表
// 返回值：内发电子文件权限记录列表，错误信息
func (l *GetDocumentDistributeRecordsLogic) queryPermissions(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecordPermission, error) {
	distributeRecordPermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	fileRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	// 使用过滤查询，只获取内发电子文件的查询权限和查询/下载权限
	permissions, err := distributeRecordPermissionClient.FindInternalElectronicPermissionsByFileRecordIDs(l.ctx, fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询内发电子文件权限记录失败: %v", err)
		return nil, fmt.Errorf("查询内发电子文件权限记录失败")
	}

	return permissions, nil
}

// queryRecycleRecords 查询回收记录
// 功能：通过发放记录文件ID查询对应的回收记录，避免N+1查询
// 参数：distributeFiles - 发放记录文件列表
// 返回值：发放记录文件ID到回收记录的映射，回收记录ID到权限列表的映射，错误信息
func (l *GetDocumentDistributeRecordsLogic) queryRecycleRecords(distributeFiles []mapper.DistributeRecordFile) (map[string]mapper.RecycleRecord, map[string][]mapper.RecycleRecordPermission, error) {
	if len(distributeFiles) == 0 {
		return make(map[string]mapper.RecycleRecord), make(map[string][]mapper.RecycleRecordPermission), nil
	}

	// 提取发放记录文件ID
	distributeFileIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		distributeFileIDs = append(distributeFileIDs, file.ID)
	}

	// 查询回收记录文件关联
	recycleRecordFileClient := mapper.NewRecycleRecordFileClient(l.svcCtx.DocvaultDB)
	recycleRecordFiles, err := recycleRecordFileClient.FindByDistributeRecordFileIDs(l.ctx, distributeFileIDs)
	if err != nil {
		l.Logger.Errorf("查询回收记录文件关联失败: %v", err)
		return nil, nil, fmt.Errorf("查询回收记录文件关联失败")
	}

	if len(recycleRecordFiles) == 0 {
		return make(map[string]mapper.RecycleRecord), make(map[string][]mapper.RecycleRecordPermission), nil
	}

	// 提取回收记录ID并构建映射
	recycleRecordIDs := make([]string, 0, len(recycleRecordFiles))
	fileToRecycleMap := make(map[string]string) // 发放记录文件ID -> 回收记录ID
	for _, recycleFile := range recycleRecordFiles {
		recycleRecordIDs = append(recycleRecordIDs, recycleFile.RecordID)
		fileToRecycleMap[recycleFile.DistributeRecordFileID] = recycleFile.RecordID
	}

	// 去重回收记录ID
	recycleRecordIDs = utils.SliceDuplicate(recycleRecordIDs)

	// 批量查询回收记录
	recycleRecordClient := mapper.NewRecycleRecordClient(l.svcCtx.DocvaultDB)
	recycleRecords, err := recycleRecordClient.FindByIDs(l.ctx, recycleRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询回收记录失败: %v", err)
		return nil, nil, fmt.Errorf("查询回收记录失败")
	}

	// 构建回收记录映射：回收记录ID -> 回收记录
	recycleRecordMap := make(map[string]mapper.RecycleRecord)
	for _, record := range recycleRecords {
		recycleRecordMap[record.ID] = record
	}

	// 查询回收记录权限
	recycleFileIDs := make([]string, 0, len(recycleRecordFiles))
	for _, recycleFile := range recycleRecordFiles {
		recycleFileIDs = append(recycleFileIDs, recycleFile.ID)
	}

	recyclePermissionClient := mapper.NewRecycleRecordPermissionClient(l.svcCtx.DocvaultDB)
	recyclePermissions, err := recyclePermissionClient.FindInternalElectronicPermissionsByFileRecordIDs(l.ctx, recycleFileIDs)
	if err != nil {
		l.Logger.Errorf("查询回收记录权限失败: %v", err)
		return nil, nil, fmt.Errorf("查询回收记录权限失败")
	}

	// 构建回收权限映射：回收记录文件ID -> 权限列表
	recyclePermissionMap := make(map[string][]mapper.RecycleRecordPermission)
	for _, permission := range recyclePermissions {
		if recyclePermissionMap[permission.FileRecordID] == nil {
			recyclePermissionMap[permission.FileRecordID] = make([]mapper.RecycleRecordPermission, 0)
		}
		recyclePermissionMap[permission.FileRecordID] = append(recyclePermissionMap[permission.FileRecordID], permission)
	}

	// 构建最终映射：发放记录文件ID -> 回收记录
	result := make(map[string]mapper.RecycleRecord)
	// 构建发放记录文件ID到回收记录文件ID的映射
	distributeToRecycleFileMap := make(map[string]string)

	for _, recycleFile := range recycleRecordFiles {
		distributeFileID := recycleFile.DistributeRecordFileID
		recycleFileID := recycleFile.ID

		// 建立发放记录文件ID到回收记录文件ID的映射
		distributeToRecycleFileMap[distributeFileID] = recycleFileID

		// 建立发放记录文件ID到回收记录的映射
		if recycleID, exists := fileToRecycleMap[distributeFileID]; exists {
			if recycleRecord, exists := recycleRecordMap[recycleID]; exists {
				result[distributeFileID] = recycleRecord
			}
		}
	}

	// 重新构建回收权限映射：发放记录文件ID -> 权限列表
	newRecyclePermissionMap := make(map[string][]mapper.RecycleRecordPermission)
	for distributeFileID, recycleFileID := range distributeToRecycleFileMap {
		if permissions, exists := recyclePermissionMap[recycleFileID]; exists {
			newRecyclePermissionMap[distributeFileID] = permissions
		}
	}

	return result, newRecyclePermissionMap, nil
}

// buildRecords 构建响应数据
// 功能：分别构建发放记录和回收记录，它们是独立的记录类型
// 参数：records - 发放记录列表，permissionMap - 权限映射，recycleRecordMap - 回收记录映射，recyclePermissionMap - 回收权限映射
// 返回值：包含发放记录和回收记录的完整列表
func (l *GetDocumentDistributeRecordsLogic) buildRecords(records []mapper.DistributeRecord, permissionMap map[string]PermissionChangeRecord, recycleRecordMap map[string]mapper.RecycleRecord, recyclePermissionMap map[string][]mapper.RecycleRecordPermission) []types.DocumentDistributeRecord {
	var allRecords []types.DocumentDistributeRecord

	// 1. 构建发放记录
	distributeRecords := l.buildDistributeRecords(records, permissionMap)
	allRecords = append(allRecords, distributeRecords...)

	// 2. 构建回收记录
	recycleRecords := l.buildRecycleRecordsFromMap(recycleRecordMap, recyclePermissionMap, permissionMap)
	allRecords = append(allRecords, recycleRecords...)

	return allRecords
}

// buildDistributeRecords 构建发放记录列表
// 功能：遍历发放记录，构建只包含发放状态的记录
// 参数：records - 发放记录列表，permissionMap - 权限映射
// 返回值：发放记录列表
func (l *GetDocumentDistributeRecordsLogic) buildDistributeRecords(records []mapper.DistributeRecord, permissionMap map[string]PermissionChangeRecord) []types.DocumentDistributeRecord {
	distributeRecords := make([]types.DocumentDistributeRecord, 0, len(records))

	for _, record := range records {
		// 检查是否有权限记录
		p, ok := permissionMap[record.ID]
		if !ok {
			continue
		}

		// 创建发放记录（只包含发放状态1,2的权限记录）
		distributeRecord := types.DocumentDistributeRecord{
			RecordID:                   record.ID,
			WorkflowID:                 record.WorkflowID, // 发放流程的WorkflowID
			ApplyTime:                  record.ApplyDate,
			InternalElectronicQuery:    p.InternalElectronicQuery,    // 只包含发放状态(1,2)
			InternalElectronicDownload: p.InternalElectronicDownload, // 只包含发放状态(1,2)
			Applicant:                  record.Applicant,
			ApplicantName:              l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, record.Applicant),
		}
		distributeRecords = append(distributeRecords, distributeRecord)
	}

	return distributeRecords
}

// buildRecycleRecordsFromMap 构建回收记录列表
// 功能：遍历回收记录，构建只包含回收状态的记录
// 参数：recycleRecordMap - 回收记录映射，recyclePermissionMap - 回收权限映射，permissionMap - 发放权限映射（用于获取用户昵称）
// 返回值：回收记录列表
func (l *GetDocumentDistributeRecordsLogic) buildRecycleRecordsFromMap(
	recycleRecordMap map[string]mapper.RecycleRecord,
	recyclePermissionMap map[string][]mapper.RecycleRecordPermission,
	permissionMap map[string]PermissionChangeRecord,
) []types.DocumentDistributeRecord {
	recycleRecords := make([]types.DocumentDistributeRecord, 0)

	// 遍历回收记录
	for distributeFileID, recycleRecord := range recycleRecordMap {
		// 根据发放记录文件ID获取对应的回收权限（现在recyclePermissionMap的键就是发放记录文件ID）
		currentRecyclePermissions := recyclePermissionMap[distributeFileID]

		// 构建当前回收记录的权限记录
		recycleQueryRecords, recycleDownloadRecords := l.buildRecyclePermissionRecordsForRecord(currentRecyclePermissions, permissionMap)

		// 如果有回收权限记录，则创建回收记录
		if len(recycleQueryRecords) > 0 || len(recycleDownloadRecords) > 0 {
			record := types.DocumentDistributeRecord{
				RecordID:                   recycleRecord.ID,
				WorkflowID:                 recycleRecord.WorkflowID, // 回收流程的WorkflowID
				ApplyTime:                  recycleRecord.UpdatedAt.UnixMilli(),
				InternalElectronicQuery:    recycleQueryRecords,    // 只包含回收状态(3)
				InternalElectronicDownload: recycleDownloadRecords, // 只包含回收状态(3)
				Applicant:                  recycleRecord.RecycleBy,
				ApplicantName:              l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, recycleRecord.RecycleBy),
			}
			recycleRecords = append(recycleRecords, record)
		}
	}

	return recycleRecords
}

// buildRecyclePermissionRecordsForRecord 为特定回收记录构建权限记录
// 功能：基于特定回收记录的权限列表构建回收状态的权限记录
// 参数：permissions - 特定回收记录的权限列表，permissionMap - 发放权限映射（用于获取用户昵称）
// 返回值：回收查询权限记录列表，回收下载权限记录列表
func (l *GetDocumentDistributeRecordsLogic) buildRecyclePermissionRecordsForRecord(
	permissions []mapper.RecycleRecordPermission,
	permissionMap map[string]PermissionChangeRecord,
) ([]types.InternalElectronicQueryRecord, []types.InternalElectronicDownloadRecord) {

	// 创建用户ID到昵称的映射（从发放权限记录中获取）
	userNicknameMap := make(map[string]string)
	for _, p := range permissionMap {
		for _, queryRecord := range p.InternalElectronicQuery {
			userNicknameMap[queryRecord.UserID] = queryRecord.UserNickname
		}
		for _, downloadRecord := range p.InternalElectronicDownload {
			userNicknameMap[downloadRecord.UserID] = downloadRecord.UserNickname
		}
	}

	recycleQueryRecords := make([]types.InternalElectronicQueryRecord, 0)
	recycleDownloadRecords := make([]types.InternalElectronicDownloadRecord, 0)

	// 遍历当前回收记录的权限列表
	for _, permission := range permissions {
		userNickname := userNicknameMap[permission.UserID]
		if userNickname == "" {
			// 如果没有找到昵称，则通过翻译器获取
			userNickname = l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, permission.UserID)
		}

		if permission.FilePermission == 1 { // 查询权限
			recycleRecord := types.InternalElectronicQueryRecord{
				UserID:       permission.UserID,
				UserNickname: userNickname,
				Status:       3, // 回收状态
			}
			recycleQueryRecords = append(recycleQueryRecords, recycleRecord)
		} else if permission.FilePermission == 2 { // 查询/下载权限
			recycleRecord := types.InternalElectronicDownloadRecord{
				UserID:       permission.UserID,
				UserNickname: userNickname,
				Status:       3, // 回收状态
			}
			recycleDownloadRecords = append(recycleDownloadRecords, recycleRecord)
		}
	}

	return recycleQueryRecords, recycleDownloadRecords
}

// applyPagination 应用分页
func (l *GetDocumentDistributeRecordsLogic) applyPagination(records []types.DocumentDistributeRecord, page, pageSize uint64) ([]types.DocumentDistributeRecord, uint64) {
	total := uint64(len(records))
	start := int((page - 1) * pageSize)
	end := int(start + int(pageSize))

	if start >= len(records) {
		return []types.DocumentDistributeRecord{}, total
	} else if end > len(records) {
		return records[start:], total
	} else {
		return records[start:end], total
	}
}

// sortRecordsByApplyTimeDesc 按申请时间倒序排列
func (l *GetDocumentDistributeRecordsLogic) sortRecordsByApplyTimeDesc(records []types.DocumentDistributeRecord) {
	sort.Slice(records, func(i, j int) bool {
		return records[i].ApplyTime > records[j].ApplyTime
	})
}
