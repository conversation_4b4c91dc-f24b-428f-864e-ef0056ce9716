package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/mapper"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
)

// TestAnalyzeBorrowPermission 测试分析借阅权限函数
// 功能：专门测试analyzeBorrowPermission函数的各种场景，包括新增的时间检查逻辑
func TestAnalyzeBorrowPermission(t *testing.T) {
	convey.Convey("测试分析借阅权限函数", t, func() {
		// 创建测试逻辑实例
		ctx := context.Background()
		logic := &GetLoanPermissionsLogic{
			ctx: ctx,
		}

		now := time.Now()
		versionNo := "V1.0"

		convey.Convey("测试空借阅记录列表 - 应该返回不可借阅", func() {
			// 测试空记录列表
			var emptyRecords []mapper.BorrowRecordView
			result := logic.analyzeBorrowPermission(emptyRecords, versionNo)
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅
		})

		convey.<PERSON>vey("测试版本号不匹配 - 应该返回不可借阅", func() {
			// 创建版本号不匹配的记录
			differentVersion := "V2.0"
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &differentVersion,
					BorrowTime:     now.Add(-1 * time.Hour),
					DueTime:        now.Add(24 * time.Hour),
					ApprovalStatus: 4, // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（版本不匹配）
		})

		convey.Convey("测试版本号为nil - 应该返回不可借阅", func() {
			// 创建版本号为nil的记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      nil,
					BorrowTime:     now.Add(-1 * time.Hour),
					DueTime:        now.Add(24 * time.Hour),
					ApprovalStatus: 4, // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（版本不匹配）
		})

		// 时间检查逻辑测试
		convey.Convey("测试已超过归还时间 - 应该返回不可借阅", func() {
			// 创建已超过归还时间的记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-48 * time.Hour), // 2天前
					DueTime:        now.Add(-24 * time.Hour), // 1天前（已超期）
					ApprovalStatus: 4,                        // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（已超过归还时间）
		})

		convey.Convey("测试借阅时间未到 - 应该返回4", func() {
			// 创建借阅时间未到的记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(24 * time.Hour), // 1天后（借阅时间未到）
					DueTime:        now.Add(48 * time.Hour), // 2天后
					ApprovalStatus: 4,                       // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 4) // 借阅时间未到
		})

		convey.Convey("测试时间正常的待审批记录 - 应该返回有正在审批的数据", func() {
			// 创建时间正常的待审批记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Hour), // 1小时前
					DueTime:        now.Add(24 * time.Hour), // 1天后
					ApprovalStatus: 2,                       // 待审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 2) // 有正在审批的数据
		})

		convey.Convey("测试时间正常的已审批且未归还记录 - 应该返回可借阅", func() {
			// 创建时间正常的已审批且未归还记录
			borrowStatus := 1 // 已借阅（未归还）
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Hour), // 1小时前
					DueTime:        now.Add(24 * time.Hour), // 1天后
					ApprovalStatus: 3,                       // 已审批
					BorrowStatus:   &borrowStatus,
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 1) // 可借阅
		})

		convey.Convey("测试时间正常的已审批且已归还记录 - 应该返回不可借阅", func() {
			// 创建时间正常的已审批且已归还记录
			borrowStatus := 3 // 已归还
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Hour), // 1小时前
					DueTime:        now.Add(24 * time.Hour), // 1天后
					ApprovalStatus: 3,                       // 已审批
					BorrowStatus:   &borrowStatus,
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（文档已被归还）
		})

		convey.Convey("测试时间正常的已驳回记录 - 应该返回可借阅", func() {
			// 创建时间正常的已驳回记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Hour), // 1小时前
					DueTime:        now.Add(24 * time.Hour), // 1天后
					ApprovalStatus: 3,                       // 已驳回
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			convey.So(result, convey.ShouldEqual, 1) // 可借阅（已驳回记录不影响借阅权限）
		})

		// 边界情况测试
		convey.Convey("测试归还时间接近当前时间 - 应该返回不可借阅", func() {
			// 创建归还时间接近当前时间的记录
			// 注意：由于函数内部会重新获取当前时间，即使设置为now，实际执行时也可能已经超期
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Hour), // 1小时前
					DueTime:        now,                     // 当前时间
					ApprovalStatus: 4,                       // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			// 由于函数内部重新获取时间，当前时间可能已经超过DueTime
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（已超过归还时间）
		})

		convey.Convey("测试借阅时间接近当前时间 - 应该返回可借阅", func() {
			// 创建借阅时间接近当前时间的记录
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(-1 * time.Millisecond), // 稍早于当前时间
					DueTime:        now.Add(24 * time.Hour),        // 1天后
					ApprovalStatus: 4,                              // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			// 借阅时间已到，应该可以借阅
			convey.So(result, convey.ShouldEqual, 1) // 可借阅
		})

		convey.Convey("测试时间检查优先级 - 超期时间检查应该优先于借阅时间检查", func() {
			// 创建同时满足超期和借阅时间未到的记录（理论上不可能，但测试优先级）
			records := []mapper.BorrowRecordView{
				{
					VersionNo:      &versionNo,
					BorrowTime:     now.Add(1 * time.Hour),  // 1小时后（借阅时间未到）
					DueTime:        now.Add(-1 * time.Hour), // 1小时前（已超期）
					ApprovalStatus: 4,                       // 已审批
				},
			}
			result := logic.analyzeBorrowPermission(records, versionNo)
			// 超期检查应该优先，返回不可借阅而不是借阅时间未到
			convey.So(result, convey.ShouldEqual, 3) // 不可借阅（已超过归还时间）
		})
	})
}