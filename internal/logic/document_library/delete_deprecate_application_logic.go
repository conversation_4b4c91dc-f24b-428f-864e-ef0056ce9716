package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDeprecateApplicationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDeprecateApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDeprecateApplicationLogic {
	return &DeleteDeprecateApplicationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDeprecateApplicationLogic) DeleteDeprecateApplication(req *types.DeleteDeprecateApplicationReq) (resp *types.DeleteDeprecateApplicationResp, err error) {
	// 1. 参数验证
	if err := l.validateRequest(req); err != nil {
		return nil, err
	}

	// 2. 调用 DocVault gRPC 服务删除作废记录
	if err := l.deleteDeprecationRecord(req.ID); err != nil {
		l.Errorf("删除作废申请失败: %v", err)
		return nil, err
	}

	l.Infof("成功删除作废申请: %s", req.ID)
	return &types.DeleteDeprecateApplicationResp{}, nil
}

// validateRequest 验证删除作废申请请求参数
func (l *DeleteDeprecateApplicationLogic) validateRequest(req *types.DeleteDeprecateApplicationReq) error {
	if req.ID == "" {
		return fmt.Errorf("作废申请ID不能为空")
	}
	return nil
}

// deleteDeprecationRecord 调用gRPC服务删除作废记录
func (l *DeleteDeprecateApplicationLogic) deleteDeprecationRecord(deprecationID string) error {
	client := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn)
	
	_, err := client.DeleteDeprecationRecord(l.ctx, &docvault.DeleteDeprecationRecordReq{
		DeprecationRecordId: deprecationID,
	})
	
	if err != nil {
		return fmt.Errorf("调用删除作废记录服务失败: %w", err)
	}
	
	return nil
}
