package document_library

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"nebula/internal/query/borrowrecord"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// GetLoanRecordsLogic 借阅记录查询逻辑处理器
type GetLoanRecordsLogic struct {
	logx.Logger
	ctx          context.Context
	svcCtx       *svc.ServiceContext
	queryService *borrowrecord.BorrowRecordQueryService
}

func NewGetLoanRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordsLogic {
	return &GetLoanRecordsLogic{
		Logger:       logx.WithContext(ctx),
		ctx:          ctx,
		svcCtx:       svcCtx,
		queryService: borrowrecord.NewBorrowRecordQueryService(svcCtx),
	}
}

// GetLoanRecords 获取借阅记录
// 功能：查询借阅记录并返回包含用户昵称和文档名称的详细信息
// 参数：req - 查询请求参数
// 返回值：resp - 查询结果响应，err - 错误信息
// 异常：数据库查询失败、并发查询失败等
func (l *GetLoanRecordsLogic) GetLoanRecords(req *types.GetLoanRecordsReq) (resp *types.GetLoanRecordsResp, err error) {
	// 实现步骤：
	// 1. 直接调用统一查询服务获取借阅记录

	// 调用统一查询服务
	return l.queryService.GetLoanRecords(l.ctx, req)
}
