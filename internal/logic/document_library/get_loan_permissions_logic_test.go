package document_library

import (
	"context"
	"fmt"
	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
)

// BorrowRecord 借阅记录结构体（对应borrow_records表）
type BorrowRecordTest struct {
	ID                string    `gorm:"column:id;primaryKey" json:"id"`
	UserID            string    `gorm:"column:user_id" json:"user_id"`
	BorrowTime        time.Time `gorm:"column:borrow_time" json:"borrow_time"`
	DueTime           time.Time `gorm:"column:due_time" json:"due_time"`
	Reason            int32     `gorm:"column:reason" json:"reason"`
	OtherReason       string    `gorm:"column:other_reason" json:"other_reason"`
	BorrowApplyTime   time.Time `gorm:"column:borrow_apply_time" json:"borrow_apply_time"`
	ApprovalStatus    int       `gorm:"column:approval_status" json:"approval_status"`
	ApprovalInfo      string    `gorm:"column:approval_info" json:"approval_info"`
	ApprovalApplyTime time.Time `gorm:"column:approval_apply_time" json:"approval_apply_time"`
	CreatedAt         time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt         time.Time `gorm:"column:updated_at" json:"updated_at"`
	CreatedBy         string    `gorm:"column:created_by" json:"created_by"`
	UpdatedBy         string    `gorm:"column:updated_by" json:"updated_by"`
}

// TableName 指定表名
func (BorrowRecordTest) TableName() string {
	return "borrow_records"
}

// BorrowDocumentRelationTest 借阅文档关系结构体（对应borrow_document_relations表）
type BorrowDocumentRelationTest struct {
	ID             string     `gorm:"column:id;primaryKey" json:"id"`
	BorrowRecordID string     `gorm:"column:borrow_record_id" json:"borrow_record_id"`
	DocumentID     string     `gorm:"column:document_id" json:"document_id"`
	VersionNo      string     `gorm:"column:version_no" json:"version_no"`
	ModuleType     int        `gorm:"column:module_type" json:"module_type"`
	BorrowStatus   int        `gorm:"column:borrow_status" json:"borrow_status"`
	RecoverUserID  string     `gorm:"column:recover_user_id" json:"recover_user_id"`
	RecoverTime    *time.Time `gorm:"column:recover_time" json:"recover_time"`
	CreatedAt      time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt      time.Time  `gorm:"column:updated_at" json:"updated_at"`
	CreatedBy      string     `gorm:"column:created_by" json:"created_by"`
	UpdatedBy      string     `gorm:"column:updated_by" json:"updated_by"`
}

// TableName 指定表名
func (BorrowDocumentRelationTest) TableName() string {
	return "borrow_document_relations"
}

// TestData 测试数据结构
type TestDataPermissions struct {
	BorrowRecords           []BorrowRecordTest
	BorrowDocumentRelations []BorrowDocumentRelationTest
	Users                   []mapper.User
	InternalDocuments       []mapper.InternalDocumentLibrary
	ExternalDocuments       []mapper.ExternalDocumentLibrary
}

// setupTestEnvironmentPermissions 设置测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupTestEnvironmentPermissions() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:     testConfig,
		DocvaultDB: docvaultDB,
		PhoenixDB:  phoenixDB,
		NebulaDB:   nebulaDB,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataPermissions(svcCtx)
	}

	return svcCtx, cleanup, nil
}

// createTestDataPermissions 创建测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createTestDataPermissions(svcCtx *svc.ServiceContext) (*TestDataPermissions, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataPermissions(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_perm_user_001",
			Username:  "test_perm_user_001",
			Nickname:  "权限测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_perm_user_002",
			Username:  "test_perm_user_002",
			Nickname:  "权限测试用户2",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_perm_doc_001",
			No:             "PERM-001",
			Name:           "权限测试文档1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_perm_001",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_perm_user_001",
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		{
			ID:             "test_perm_doc_002",
			No:             "PERM-002",
			Name:           "权限测试文档2",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_perm_002",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_perm_user_001",
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		{
			ID:             "test_perm_doc_003",
			No:             "PERM-003",
			Name:           "权限测试文档3",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_perm_003",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_perm_user_001",
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建测试借阅记录
	borrowRecords := []BorrowRecordTest{
		// 用户1对文档1的已审批借阅记录（文档未归还）
		{
			ID:                "test_perm_borrow_001",
			UserID:            "test_perm_user_001",
			BorrowTime:        now.Add(-7 * 24 * time.Hour), // 7天前
			DueTime:           now.Add(7 * 24 * time.Hour),  // 7天后
			Reason:            5,
			OtherReason:       "测试借阅权限",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-6 * 24 * time.Hour),
			CreatedAt:         now.Add(-7 * 24 * time.Hour),
			UpdatedAt:         now.Add(-6 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_001",
			UpdatedBy:         "test_perm_user_001",
		},
		// 用户1对文档2的待审批借阅记录
		{
			ID:                "test_perm_borrow_002",
			UserID:            "test_perm_user_001",
			BorrowTime:        now.Add(-3 * 24 * time.Hour), // 3天前
			DueTime:           now.Add(10 * 24 * time.Hour), // 10天后
			Reason:            5,
			OtherReason:       "测试待审批状态",
			ApprovalStatus:    2, // 待审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-3 * 24 * time.Hour),
			CreatedAt:         now.Add(-3 * 24 * time.Hour),
			UpdatedAt:         now.Add(-3 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_001",
			UpdatedBy:         "test_perm_user_001",
		},
		// 用户1对文档1的已驳回借阅记录
		{
			ID:                "test_perm_borrow_003",
			UserID:            "test_perm_user_001",
			BorrowTime:        now.Add(-10 * 24 * time.Hour), // 10天前
			DueTime:           now.Add(-3 * 24 * time.Hour),  // 3天前
			Reason:            5,
			OtherReason:       "测试驳回状态",
			ApprovalStatus:    3, // 已驳回
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-10 * 24 * time.Hour),
			CreatedAt:         now.Add(-10 * 24 * time.Hour),
			UpdatedAt:         now.Add(-10 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_001",
			UpdatedBy:         "test_perm_user_001",
		},
		// 用户1对文档1的已审批且已归还的借阅记录
		{
			ID:                "test_perm_borrow_004",
			UserID:            "test_perm_user_001",
			BorrowTime:        now.Add(-15 * 24 * time.Hour), // 15天前
			DueTime:           now.Add(-8 * 24 * time.Hour),  // 8天前
			Reason:            5,
			OtherReason:       "测试已归还状态",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-14 * 24 * time.Hour),
			CreatedAt:         now.Add(-15 * 24 * time.Hour),
			UpdatedAt:         now.Add(-14 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_001",
			UpdatedBy:         "test_perm_user_001",
		},
		// 用户2对文档1的已审批借阅记录（已超过归还时间）
		{
			ID:                "test_perm_borrow_005",
			UserID:            "test_perm_user_002",
			BorrowTime:        now.Add(-20 * 24 * time.Hour), // 20天前
			DueTime:           now.Add(-5 * 24 * time.Hour),  // 5天前（已超期）
			Reason:            5,
			OtherReason:       "测试超期状态",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-19 * 24 * time.Hour),
			CreatedAt:         now.Add(-20 * 24 * time.Hour),
			UpdatedAt:         now.Add(-19 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_002",
			UpdatedBy:         "test_perm_user_002",
		},
		// 用户2对文档2的已审批借阅记录（借阅时间未到）
		{
			ID:                "test_perm_borrow_006",
			UserID:            "test_perm_user_002",
			BorrowTime:        now.Add(2 * 24 * time.Hour),  // 2天后（借阅时间未到）
			DueTime:           now.Add(16 * 24 * time.Hour), // 16天后
			Reason:            5,
			OtherReason:       "测试借阅时间未到状态",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-1 * 24 * time.Hour),
			CreatedAt:         now.Add(-1 * 24 * time.Hour),
			UpdatedAt:         now.Add(-1 * 24 * time.Hour),
			CreatedBy:         "test_perm_user_002",
			UpdatedBy:         "test_perm_user_002",
		},
	}

	// 插入借阅记录数据
	for _, record := range borrowRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅记录失败: %w", err)
		}
	}

	// 4. 创建借阅文档关系
	borrowDocRelations := []BorrowDocumentRelationTest{
		// 文档1未归还的借阅关系
		{
			ID:             "test_perm_relation_001",
			BorrowRecordID: "test_perm_borrow_001",
			DocumentID:     "test_perm_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅（未归还）
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		// 文档2待审批的借阅关系
		{
			ID:             "test_perm_relation_002",
			BorrowRecordID: "test_perm_borrow_002",
			DocumentID:     "test_perm_doc_002",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-3 * 24 * time.Hour),
			UpdatedAt:      now.Add(-3 * 24 * time.Hour),
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		// 文档1已驳回的借阅关系
		{
			ID:             "test_perm_relation_003",
			BorrowRecordID: "test_perm_borrow_003",
			DocumentID:     "test_perm_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-10 * 24 * time.Hour),
			UpdatedAt:      now.Add(-10 * 24 * time.Hour),
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		// 文档1已归还的借阅关系
		{
			ID:             "test_perm_relation_004",
			BorrowRecordID: "test_perm_borrow_004",
			DocumentID:     "test_perm_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   3, // 已归还
			RecoverUserID:  "test_perm_user_002",
			RecoverTime:    &now,
			CreatedAt:      now.Add(-15 * 24 * time.Hour),
			UpdatedAt:      now,
			CreatedBy:      "test_perm_user_001",
			UpdatedBy:      "test_perm_user_001",
		},
		// 文档1已超期的借阅关系
		{
			ID:             "test_perm_relation_005",
			BorrowRecordID: "test_perm_borrow_005",
			DocumentID:     "test_perm_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅（未归还）
			CreatedAt:      now.Add(-20 * 24 * time.Hour),
			UpdatedAt:      now.Add(-20 * 24 * time.Hour),
			CreatedBy:      "test_perm_user_002",
			UpdatedBy:      "test_perm_user_002",
		},
		// 文档2借阅时间未到的借阅关系
		{
			ID:             "test_perm_relation_006",
			BorrowRecordID: "test_perm_borrow_006",
			DocumentID:     "test_perm_doc_002",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-1 * 24 * time.Hour),
			UpdatedAt:      now.Add(-1 * 24 * time.Hour),
			CreatedBy:      "test_perm_user_002",
			UpdatedBy:      "test_perm_user_002",
		},
	}

	// 插入借阅文档关系数据
	for _, relation := range borrowDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅文档关系失败: %w", err)
		}
	}

	return &TestDataPermissions{
		BorrowRecords:           borrowRecords,
		BorrowDocumentRelations: borrowDocRelations,
		Users:                   users,
		InternalDocuments:       internalDocs,
	}, nil
}

// cleanupTestDataPermissions 清理测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupTestDataPermissions(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除借阅文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_perm_relation_%").Delete(&BorrowDocumentRelationTest{})

	// 删除借阅记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_perm_borrow_%").Delete(&BorrowRecordTest{})

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_perm_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_perm_user_%").Delete(&mapper.User{})
}

// TestGetDocumentFileID 测试获取文档文件ID功能
func TestGetDocumentFileID(t *testing.T) {
	convey.Convey("测试获取文档文件ID功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentPermissions()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataPermissions(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		convey.Convey("测试通过all_documents_view获取存在文档的fileID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试获取存在文档的fileID
			fileID, err := logic.getDocumentFileID("test_perm_doc_001")
			convey.So(err, convey.ShouldBeNil)
			convey.So(fileID, convey.ShouldEqual, "test_file_perm_001")
		})

		convey.Convey("测试获取不存在文档的fileID - 应该返回错误", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试获取不存在文档的fileID
			fileID, err := logic.getDocumentFileID("non_existent_doc_id")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(fileID, convey.ShouldEqual, "")
			convey.So(err.Error(), convey.ShouldContainSubstring, "查询文档信息失败")
		})

		convey.Convey("测试获取多个文档的fileID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试获取多个文档的fileID
			testCases := []struct {
				documentID     string
				expectedFileID string
			}{
				{"test_perm_doc_001", "test_file_perm_001"},
				{"test_perm_doc_002", "test_file_perm_002"},
				{"test_perm_doc_003", "test_file_perm_003"},
			}

			for _, tc := range testCases {
				fileID, err := logic.getDocumentFileID(tc.documentID)
				convey.So(err, convey.ShouldBeNil)
				convey.So(fileID, convey.ShouldEqual, tc.expectedFileID)
			}
		})
	})
}

// TestGetLoanPermissions 测试获取借阅权限功能
func TestGetLoanPermissions(t *testing.T) {
	convey.Convey("测试获取借阅权限功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentPermissions()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataPermissions(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		convey.Convey("测试无借阅记录的文档 - 应该返回可借阅并包含fileID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试查询没有借阅记录的文档
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_003",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不能借阅
			// 验证返回了正确的fileID
			convey.So(resp.FileID, convey.ShouldEqual, "")
		})

		convey.Convey("测试有待审批记录的文档 - 应该返回有正在审批的数据且不包含fileID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试查询有待审批记录的文档
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_002",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Permission, convey.ShouldEqual, 2) // 有正在审批的数据
			// 验证不可借阅时不返回fileID
			convey.So(resp.FileID, convey.ShouldEqual, "")
		})

		convey.Convey("测试有未归还借阅记录的文档 - 应该返回不可借阅且不包含fileID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试查询有未归还借阅记录的文档
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_001",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不可借阅
			// 验证不可借阅时不返回fileID
			convey.So(resp.FileID, convey.ShouldEqual, "")
		})

		convey.Convey("测试不指定版本号的权限检查", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试不指定版本号的权限检查
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_001",
				DocumentVersionNo: "", // 不指定版本号
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 由于有未归还的借阅记录，应该返回不可借阅
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不可借阅
		})

		convey.Convey("测试用户上下文缺失的情况", func() {
			// 创建没有用户ID的上下文
			ctx := context.Background()
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试用户上下文缺失的情况
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_001",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "无法获取当前用户信息")
		})

		convey.Convey("测试不存在的文档ID", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_001")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试不存在的文档ID
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "non_existent_doc",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 没有借阅记录，应该返回不可借阅
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不可借阅
		})

		convey.Convey("测试其他用户的权限检查 - 应该返回可借阅并包含fileID", func() {
			// 创建带有其他用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_002")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试其他用户对已有借阅记录文档的权限检查
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_001",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 其他用户没有借阅记录，应该返回可借阅
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不可借阅
			// 验证返回了正确的fileID
			convey.So(resp.FileID, convey.ShouldEqual, "")
		})

		// 新增时间检查逻辑测试用例
		convey.Convey("测试已超过归还时间的借阅记录 - 应该返回不可借阅", func() {
			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), "UserId", "test_perm_user_002")
			logic := NewGetLoanPermissionsLogic(ctx, svcCtx)

			// 测试已超过归还时间的借阅记录
			req := &types.GetLoanPermissionsReq{
				DocumentID:        "test_perm_doc_001",
				DocumentVersionNo: "V1.0",
			}

			resp, err := logic.GetLoanPermissions(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 已超过归还时间，应该返回不可借阅
			convey.So(resp.Permission, convey.ShouldEqual, 3) // 不可借阅（已超过归还时间）
			// 验证不可借阅时不返回fileID
			convey.So(resp.FileID, convey.ShouldEqual, "")
		})
	})
}
