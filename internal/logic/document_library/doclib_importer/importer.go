package doclib_importer

import (
	"context"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
	"net/http"
	"time"

	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

// Importer 定义了导入数据的模板方法。
type ImportRequest struct {
	// 台账文件
	MainFileID string
	// 台账文件对应的文件清单
	ListFileIDs []string
	// 类型字典id
	TypeDictionaryID string
	// 领域字典id
	DomainDictionaryID string
	// 认证字典id
	AuthDictionaryID string
}

type DictionaryRelation struct {
	DictionaryNodeID string
	BusinessID       string
	BusinessType     string
}

// 导入器接口
type Importer interface {
	Import(ctx context.Context, req ImportRequest) error
}

// 导入器能力接口
type ImporterAbility[T any] interface {
	// 读取excel
	ReadExcel(ctx context.Context, req ImportRequest) ([][]string, error)
	// 解析excel
	Parse(ctx context.Context, req ImportRequest, data [][]string) ([]T, error)
	// 验证数据
	Validate(ctx context.Context, req ImportRequest, data []T) error
	// 保存数据
	Save(ctx context.Context, req ImportRequest, data []T) error
	// 获取字典关系
	GetDictionaryRelation(ctx context.Context, req ImportRequest) ([]DictionaryRelation, error)
	// 保存字典关系
	SaveDictionaryRelation(ctx context.Context, dictionaryRelations []DictionaryRelation) error
}

// 导入器能力实现
// 泛型参数数量修正，结构体和接口一致
// S ~[]E, E any
type ImporterAbilityImpl[T any] struct {
	svcCtx *svc.ServiceContext
}

// NewImporterAbility 返回 ImporterAbility[S, E]
func NewImporterAbility[T any](svcCtx *svc.ServiceContext) ImporterAbility[T] {
	return &ImporterAbilityImpl[T]{
		svcCtx: svcCtx,
	}
}

// Template 是 Importer 接口的基本实现。
type ImporterImpl[T any] struct {
	ImporterAbility[T]
}

// NewImporter 创建一个新的 ImporterImpl。
func NewImporter[T any](importerAbility ImporterAbility[T]) *ImporterImpl[T] {
	return &ImporterImpl[T]{
		ImporterAbility: importerAbility,
	}
}

// Import 执行导入过程。
func (t *ImporterImpl[T]) Import(ctx context.Context, req ImportRequest) error {
	rows, err := t.ReadExcel(ctx, req)
	if err != nil {
		logx.Errorf("解析请求失败: %v", err)
		return err
	}
	data, err := t.Parse(ctx, req, rows)
	if err != nil {
		logx.Errorf("解析数据失败: %v", err)
		return err
	}
	if err := t.Validate(ctx, req, data); err != nil {
		logx.Errorf("验证数据失败: %v", err)
		return err
	}

	if err := t.Save(ctx, req, data); err != nil {
		logx.Errorf("保存数据失败: %v", err)
		return err
	}
	dictionaryRelations, err := t.GetDictionaryRelation(ctx, req)
	if err != nil {
		logx.Errorf("获取字典关系失败: %v", err)
		return err
	}

	if err := t.SaveDictionaryRelation(ctx, dictionaryRelations); err != nil {
		logx.Errorf("保存字典关系失败: %v", err)
		return err
	}

	return nil
}

func (t *ImporterAbilityImpl[T]) ReadExcel(ctx context.Context, req ImportRequest) ([][]string, error) {
	excelFileInfo, err := t.svcCtx.PhoenixClient.GetFileInfo(ctx, req.MainFileID)
	if err != nil {
		return nil, err
	}
	// 下载文件
	respHttp, err := http.Get(excelFileInfo.Url)
	if err != nil {
		return nil, err
	}
	defer respHttp.Body.Close()

	// 用excelize解析
	f, err := excelize.OpenReader(respHttp.Body)
	if err != nil {
		return nil, err
	}

	// 获取第一个sheet名称
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, err
	}
	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}
	return rows[1:], nil
}

func (t *ImporterAbilityImpl[T]) Parse(ctx context.Context, req ImportRequest, rows [][]string) (data []T, err error) {
	return data, nil
}

func (t *ImporterAbilityImpl[T]) Validate(ctx context.Context, req ImportRequest, data []T) error {
	return nil
}

func (t *ImporterAbilityImpl[T]) Save(ctx context.Context, req ImportRequest, data []T) error {
	return nil
}

func (t *ImporterAbilityImpl[T]) GetDictionaryRelation(ctx context.Context, req ImportRequest) ([]DictionaryRelation, error) {
	return nil, nil
}

func (t *ImporterAbilityImpl[T]) SaveDictionaryRelation(ctx context.Context, dictionaryRelations []DictionaryRelation) error {
	if len(dictionaryRelations) == 0 {
		return nil
	}
	businessDictionaryRelations := make([]mapper.BusinessDictionaryRelation, len(dictionaryRelations))
	now := time.Now()
	for i, v := range dictionaryRelations {
		businessDictionaryRelations[i] = mapper.BusinessDictionaryRelation{
			ID:               t.svcCtx.IdGenerator.GenerateIDString(),
			DictionaryNodeID: v.DictionaryNodeID,
			BusinessID:       v.BusinessID,
			BusinessType:     v.BusinessType,
			CreatedAt:        now,
			CreatedBy:        utils.GetContextUserID(ctx),
		}
	}
	return mapper.NewBusinessDictionaryRelationClient(t.svcCtx.NebulaDB).BatchCreateBusinessDictionaryRelation(ctx, businessDictionaryRelations)
}
