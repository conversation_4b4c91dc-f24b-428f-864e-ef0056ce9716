package document_library

import (
	"context"
	"fmt"
	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
)

// TestData 测试数据结构
type TestData struct {
	BorrowRecords           []BorrowRecord
	BorrowDocumentRelations []BorrowDocumentRelation
	Users                   []mapper.User
	InternalDocuments       []mapper.InternalDocumentLibrary
	ExternalDocuments       []mapper.ExternalDocumentLibrary
}

// setupTestEnvironment 设置测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupTestEnvironment() (*svc.ServiceContext, func(), error) {
	// 1. 创建测试配置
	// 2. 初始化数据库连接
	// 3. 创建服务上下文
	// 4. 返回清理函数

	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:     testConfig,
		DocvaultDB: docvaultDB,
		PhoenixDB:  phoenixDB,
		NebulaDB:   nebulaDB,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestData(svcCtx)
	}

	return svcCtx, cleanup, nil
}

// createTestData 创建测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createTestData(svcCtx *svc.ServiceContext) (*TestData, error) {
	// 实现步骤：
	// 1. 创建测试用户
	// 2. 创建测试文档
	// 3. 创建测试借阅记录
	// 4. 创建借阅文档关系

	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestData(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_user_001",
			Username:  "test_user_001",
			Nickname:  "张三",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_user_002",
			Username:  "test_user_002",
			Nickname:  "李四",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_user_003",
			Username:  "test_user_003",
			Nickname:  "王五",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_internal_doc_001",
			No:             "INT-001",
			Name:           "内部文档测试1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_001",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001", // 0.4版本调整：编制部门（数据库中存储为字符串）
			AuthorIDs:      "test_user_001", // 0.4版本调整：编制人（数据库中存储为字符串）
			CreatedBy:      "test_user_001",
			UpdatedBy:      "test_user_001",
		},
		{
			ID:             "test_internal_doc_002",
			No:             "INT-002",
			Name:           "内部文档测试2",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_002",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001", // 0.4版本调整：编制部门（数据库中存储为字符串）
			AuthorIDs:      "test_user_001", // 0.4版本调整：编制人（数据库中存储为字符串）
			CreatedBy:      "test_user_001",
			UpdatedBy:      "test_user_001",
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:             "test_external_doc_001",
			Number:         "EXT-001",
			Name:           "外部文档测试1",
			Version:        "V1.0",
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_003",
			CreatedBy:      "test_user_001",
			UpdatedBy:      "test_user_001",
			TenantID:       "test_tenant_001",
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 4. 创建测试借阅记录
	borrowRecords := []BorrowRecord{
		{
			ID:                "test_borrow_001",
			UserID:            "test_user_001",
			BorrowTime:        now.Add(-7 * 24 * time.Hour), // 7天前
			DueTime:           now.Add(7 * 24 * time.Hour),  // 7天后
			Reason:            5,
			OtherReason:       "学习需要",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-6 * 24 * time.Hour),
			CreatedAt:         now.Add(-7 * 24 * time.Hour),
			UpdatedAt:         now.Add(-6 * 24 * time.Hour),
			CreatedBy:         "test_user_001",
			UpdatedBy:         "test_user_001",
		},
		{
			ID:                "test_borrow_002",
			UserID:            "test_user_002",
			BorrowTime:        now.Add(-3 * 24 * time.Hour), // 3天前
			DueTime:           now.Add(10 * 24 * time.Hour), // 10天后
			Reason:            5,
			OtherReason:       "工作需要",
			ApprovalStatus:    2, // 待审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-3 * 24 * time.Hour),
			CreatedAt:         now.Add(-3 * 24 * time.Hour),
			UpdatedAt:         now.Add(-3 * 24 * time.Hour),
			CreatedBy:         "test_user_002",
			UpdatedBy:         "test_user_002",
		},
		{
			ID:                "test_borrow_003",
			UserID:            "test_user_001",
			BorrowTime:        now.Add(-1 * 24 * time.Hour), // 1天前
			DueTime:           now.Add(14 * 24 * time.Hour), // 14天后
			Reason:            5,
			OtherReason:       "研究需要",
			ApprovalStatus:    3, // 已驳回
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-1 * 24 * time.Hour),
			CreatedAt:         now.Add(-1 * 24 * time.Hour),
			UpdatedAt:         now.Add(-1 * 24 * time.Hour),
			CreatedBy:         "test_user_001",
			UpdatedBy:         "test_user_001",
		},
	}

	// 插入借阅记录数据
	for _, record := range borrowRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅记录失败: %w", err)
		}
	}

	// 5. 创建借阅文档关系
	borrowDocRelations := []BorrowDocumentRelation{
		{
			ID:             "test_relation_001",
			BorrowRecordID: "test_borrow_001",
			DocumentID:     "test_internal_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
		},
		{
			ID:             "test_relation_002",
			BorrowRecordID: "test_borrow_001",
			DocumentID:     "test_internal_doc_002",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   3, // 已归还
			RecoverUserID:  "test_user_003",
			RecoverTime:    &now,
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now,
		},
		{
			ID:             "test_relation_003",
			BorrowRecordID: "test_borrow_002",
			DocumentID:     "test_external_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     3, // 外部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-3 * 24 * time.Hour),
			UpdatedAt:      now.Add(-3 * 24 * time.Hour),
		},
		{
			ID:             "test_relation_004",
			BorrowRecordID: "test_borrow_003",
			DocumentID:     "test_internal_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-1 * 24 * time.Hour),
			UpdatedAt:      now.Add(-1 * 24 * time.Hour),
		},
	}

	// 插入借阅文档关系数据
	for _, relation := range borrowDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅文档关系失败: %w", err)
		}
	}

	return &TestData{
		BorrowRecords:           borrowRecords,
		BorrowDocumentRelations: borrowDocRelations,
		Users:                   users,
		InternalDocuments:       internalDocs,
		ExternalDocuments:       externalDocs,
	}, nil
}

// cleanupTestData 清理测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除借阅文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_relation_%").Delete(&BorrowDocumentRelation{})

	// 删除借阅记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_borrow_%").Delete(&BorrowRecord{})

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_external_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_user_%").Delete(&mapper.User{})
}

// TestGetLoanRecords 测试获取借阅记录功能
func TestGetLoanRecords(t *testing.T) {
	convey.Convey("测试获取借阅记录功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironment()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestData(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有组织ID的上下文
		ctx := context.WithValue(context.Background(), "OrganizationId", "test_org_001")

		convey.Convey("测试按用户昵称过滤", func() {
			logic := NewGetLoanRecordsLogic(ctx, svcCtx)

			// 测试查询张三的借阅记录
			req := &types.GetLoanRecordsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				UserNickname: "张三",
			}

			resp, err := logic.GetLoanRecords(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 2) // 张三有2条借阅记录
			for _, record := range resp.Data {
				convey.So(record.UserNickname, convey.ShouldEqual, "张三")
			}
		})

		convey.Convey("测试按文档编号过滤", func() {
			logic := NewGetLoanRecordsLogic(ctx, svcCtx)

			// 测试查询特定文档编号的借阅记录
			req := &types.GetLoanRecordsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentNo: "INT-001",
			}

			resp, err := logic.GetLoanRecords(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该返回包含该文档的借阅记录
			convey.So(resp.Total, convey.ShouldBeGreaterThan, 0)
		})

		convey.Convey("测试按文档名称过滤", func() {
			logic := NewGetLoanRecordsLogic(ctx, svcCtx)

			// 测试查询特定文档名称的借阅记录
			req := &types.GetLoanRecordsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentName: "内部文档测试1",
			}

			resp, err := logic.GetLoanRecords(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该返回包含该文档的借阅记录
			convey.So(resp.Total, convey.ShouldBeGreaterThan, 0)
		})

		convey.Convey("测试组合条件过滤", func() {
			logic := NewGetLoanRecordsLogic(ctx, svcCtx)

			// 测试组合多个过滤条件
			req := &types.GetLoanRecordsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				UserNickname:       "张三",
				ApprovalStatus:     "4", // 已审批
				DocumentModuleType: 2,   // 内部文档
			}

			resp, err := logic.GetLoanRecords(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			// 应该返回符合所有条件的记录
			for _, record := range resp.Data {
				convey.So(record.UserNickname, convey.ShouldEqual, "张三")
				convey.So(record.ApprovalStatus, convey.ShouldEqual, 4)
			}
		})

		convey.Convey("测试空结果查询", func() {
			logic := NewGetLoanRecordsLogic(ctx, svcCtx)

			// 测试查询不存在的用户
			req := &types.GetLoanRecordsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				UserNickname: "不存在的用户",
			}

			resp, err := logic.GetLoanRecords(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 0)
			convey.So(len(resp.Data), convey.ShouldEqual, 0)
		})

	})
}
