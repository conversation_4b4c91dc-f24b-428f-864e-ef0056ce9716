package document_library

import (
	"context"
	"fmt"
	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
)

// BorrowRecord 借阅记录结构体（对应borrow_records表）
type BorrowRecord struct {
	ID                string    `gorm:"column:id;primaryKey" json:"id"`
	UserID            string    `gorm:"column:user_id" json:"user_id"`
	BorrowTime        time.Time `gorm:"column:borrow_time" json:"borrow_time"`
	DueTime           time.Time `gorm:"column:due_time" json:"due_time"`
	Reason            int32     `gorm:"column:reason" json:"reason"`
	OtherReason       string    `gorm:"column:other_reason" json:"other_reason"`
	BorrowApplyTime   time.Time `gorm:"column:borrow_apply_time" json:"borrow_apply_time"`
	ApprovalStatus    int       `gorm:"column:approval_status" json:"approval_status"`
	ApprovalInfo      string    `gorm:"column:approval_info" json:"approval_info"`
	ApprovalApplyTime time.Time `gorm:"column:approval_apply_time" json:"approval_apply_time"`
	CreatedAt         time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt         time.Time `gorm:"column:updated_at" json:"updated_at"`
	CreatedBy         string    `gorm:"column:created_by" json:"created_by"`
	UpdatedBy         string    `gorm:"column:updated_by" json:"updated_by"`
}

// TableName 指定表名
func (BorrowRecord) TableName() string {
	return "borrow_records"
}

// BorrowDocumentRelation 借阅文档关系结构体
type BorrowDocumentRelation struct {
	ID             string     `gorm:"column:id" json:"id"`                             // 关系ID
	BorrowRecordID string     `gorm:"column:borrow_record_id" json:"borrow_record_id"` // 借阅记录ID
	DocumentID     string     `gorm:"column:document_id" json:"document_id"`           // 文档ID
	VersionNo      string     `gorm:"column:version_no" json:"version_no"`             // 文档版本号
	ModuleType     int        `gorm:"column:module_type" json:"module_type"`           // 文档模块类型
	BorrowStatus   int        `gorm:"column:borrow_status" json:"borrow_status"`       // 借阅状态
	RecoverUserID  string     `gorm:"column:recover_user_id" json:"recover_user_id"`   // 回收用户ID
	RecoverTime    *time.Time `gorm:"column:recover_time" json:"recover_time"`         // 回收时间
	CreatedAt      time.Time  `gorm:"column:created_at" json:"created_at"`             // 创建时间
	UpdatedAt      time.Time  `gorm:"column:updated_at" json:"updated_at"`             // 更新时间
	CreatedBy      string     `gorm:"column:created_by" json:"created_by"`             // 创建人
	UpdatedBy      string     `gorm:"column:updated_by" json:"updated_by"`             // 更新人
}

// TableName 指定表名
func (BorrowDocumentRelation) TableName() string {
	return "borrow_document_relations"
}

// TestGetLoanRecordDocuments 测试获取借阅记录文档清单功能
func TestGetLoanRecordDocuments(t *testing.T) {
	convey.Convey("测试获取借阅记录文档清单功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForDocuments()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForDocuments(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有组织ID的上下文
		ctx := context.WithValue(context.Background(), "organizationId", "test_org_001")

		convey.Convey("测试正常查询借阅记录文档", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			// 测试查询存在的借阅记录文档
			req := &types.GetLoanRecordDocumentsReq{
				ID: "test_borrow_001",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(len(resp.Data), convey.ShouldBeGreaterThan, 0)

			// 验证返回的文档信息
			for _, doc := range resp.Data {
				convey.So(doc.DocumentID, convey.ShouldNotBeEmpty)
				convey.So(doc.DocumentName, convey.ShouldNotBeEmpty)
				convey.So(doc.DocumentModuleName, convey.ShouldNotBeEmpty)
				// 根据测试数据，test_borrow_001 包含两个内部文档（ModuleType=2）
				convey.So(doc.DocumentModuleType, convey.ShouldEqual, 2)
				// 验证借阅状态：1已借阅, 3已归还 (使用int32类型)
				convey.So(doc.BorrowStatus, convey.ShouldBeIn, int32(1), int32(3))
			}
		})

		convey.Convey("测试查询包含内部文档的借阅记录", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			req := &types.GetLoanRecordDocumentsReq{
				ID: "test_borrow_001",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// 验证内部文档信息
			found := false
			for _, doc := range resp.Data {
				if doc.DocumentModuleType == 2 { // 内部文档
					found = true
					convey.So(doc.DocumentModuleName, convey.ShouldEqual, "内部文档")
					convey.So(doc.DocumentNo, convey.ShouldNotBeEmpty)
					convey.So(doc.DocumentName, convey.ShouldNotBeEmpty)
				}
			}
			convey.So(found, convey.ShouldBeTrue)
		})

		convey.Convey("测试查询包含外部文档的借阅记录", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			req := &types.GetLoanRecordDocumentsReq{
				ID: "test_borrow_002",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// 验证外部文档信息
			found := false
			for _, doc := range resp.Data {
				if doc.DocumentModuleType == 3 { // 外部文档
					found = true
					convey.So(doc.DocumentModuleName, convey.ShouldEqual, "外部文档")
					convey.So(doc.DocumentNo, convey.ShouldNotBeEmpty)
					convey.So(doc.DocumentName, convey.ShouldNotBeEmpty)
				}
			}
			convey.So(found, convey.ShouldBeTrue)
		})

		convey.Convey("测试查询不存在的借阅记录", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			req := &types.GetLoanRecordDocumentsReq{
				ID: "non_existent_borrow_record",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(len(resp.Data), convey.ShouldEqual, 0)
		})

		convey.Convey("测试空借阅记录ID", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			req := &types.GetLoanRecordDocumentsReq{
				ID: "",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "借阅记录ID不能为空")
		})

		convey.Convey("测试文档状态验证", func() {
			logic := NewGetLoanRecordDocumentsLogic(ctx, svcCtx)

			req := &types.GetLoanRecordDocumentsReq{
				ID: "test_borrow_001",
			}

			resp, err := logic.GetLoanRecordDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// 验证借阅状态
			for _, doc := range resp.Data {
				convey.So(doc.BorrowStatus, convey.ShouldBeIn, int32(1), int32(3)) // 根据测试数据：1已借阅, 3已归还 (使用int32类型)
			}
		})
	})
}

// setupTestEnvironmentForDocuments 设置文档测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupTestEnvironmentForDocuments() (*svc.ServiceContext, func(), error) {
	// 1. 创建测试配置
	// 2. 初始化数据库连接
	// 3. 创建服务上下文
	// 4. 返回清理函数

	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:     testConfig,
		DocvaultDB: docvaultDB,
		PhoenixDB:  phoenixDB,
		NebulaDB:   nebulaDB,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForDocuments(svcCtx)
	}

	return svcCtx, cleanup, nil
}

// createTestDataForDocuments 创建文档测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createTestDataForDocuments(svcCtx *svc.ServiceContext) (*TestDataForDocuments, error) {
	// 实现步骤：
	// 1. 创建测试用户
	// 2. 创建测试文档
	// 3. 创建测试借阅记录
	// 4. 创建借阅文档关系
	// 5. 创建业务字典节点关系

	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForDocuments(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_doc_user_001",
			Username:  "test_doc_user_001",
			Nickname:  "文档测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_doc_user_002",
			Username:  "test_doc_user_002",
			Nickname:  "文档测试用户2",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_doc_internal_001",
			No:             "DOC-INT-001",
			Name:           "测试内部文档1",
			EnglishName:    "Test Internal Document 1", // 0.5版本新增：文件英文名称
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_001",
			DocCategoryID:  "test_category_001",
			DepartmentIDs:  "test_dept_001",     // 0.4版本调整：编制部门（数据库中存储为字符串）
			AuthorIDs:      "test_doc_user_001", // 0.4版本调整：编制人（数据库中存储为字符串）
			CreatedBy:      "test_doc_user_001",
			UpdatedBy:      "test_doc_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour), // 30天后过期
			Remark:         "测试备注1",                      // 0.5版本新增：备注
		},
		{
			ID:             "test_doc_internal_002",
			No:             "DOC-INT-002",
			Name:           "测试内部文档2",
			EnglishName:    "Test Internal Document 2", // 0.5版本新增：文件英文名称
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_file_002",
			DocCategoryID:  "test_category_002",
			DepartmentIDs:  "test_dept_001",     // 0.4版本调整：编制部门（数据库中存储为字符串）
			AuthorIDs:      "test_doc_user_001", // 0.4版本调整：编制人（数据库中存储为字符串）
			CreatedBy:      "test_doc_user_001",
			UpdatedBy:      "test_doc_user_001",
			EffectiveDate:  now.Add(60 * 24 * time.Hour), // 60天后过期
			Remark:         "测试备注2",                      // 0.5版本新增：备注
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:                   "test_doc_external_001",
			Number:               "DOC-EXT-001",
			Name:                 "测试外部文档1",
			Version:              "V1.0",
			Status:               1,
			CreatedAt:            now,
			UpdatedAt:            now,
			OrganizationID:       "test_org_001",
			FileID:               "test_file_003",
			CreatedBy:            "test_doc_user_001",
			UpdatedBy:            "test_doc_user_001",
			TenantID:             "test_tenant_001",
			TypeDictionaryNodeId: "test_category_003",
			EffectiveDate:        now.Add(45 * 24 * time.Hour), // 45天后过期
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 4. 创建测试借阅记录
	borrowRecords := []BorrowRecord{
		{
			ID:                "test_borrow_001",
			UserID:            "test_doc_user_001",
			BorrowTime:        now.Add(-7 * 24 * time.Hour), // 7天前
			DueTime:           now.Add(7 * 24 * time.Hour),  // 7天后
			Reason:            5,
			OtherReason:       "文档测试需要",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-6 * 24 * time.Hour),
			CreatedAt:         now.Add(-7 * 24 * time.Hour),
			UpdatedAt:         now.Add(-6 * 24 * time.Hour),
			CreatedBy:         "test_doc_user_001",
			UpdatedBy:         "test_doc_user_001",
		},
		{
			ID:                "test_borrow_002",
			UserID:            "test_doc_user_002",
			BorrowTime:        now.Add(-3 * 24 * time.Hour), // 3天前
			DueTime:           now.Add(10 * 24 * time.Hour), // 10天后
			Reason:            5,
			OtherReason:       "外部文档测试",
			ApprovalStatus:    4, // 已审批
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-3 * 24 * time.Hour),
			CreatedAt:         now.Add(-3 * 24 * time.Hour),
			UpdatedAt:         now.Add(-3 * 24 * time.Hour),
			CreatedBy:         "test_doc_user_002",
			UpdatedBy:         "test_doc_user_002",
		},
	}

	// 插入借阅记录数据
	for _, record := range borrowRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅记录失败: %w", err)
		}
	}

	// 5. 创建借阅文档关系
	borrowDocRelations := []BorrowDocumentRelation{
		{
			ID:             "test_doc_relation_001",
			BorrowRecordID: "test_borrow_001",
			DocumentID:     "test_doc_internal_001",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
			CreatedBy:      "test_doc_user_001",
			UpdatedBy:      "test_doc_user_001",
		},
		{
			ID:             "test_doc_relation_002",
			BorrowRecordID: "test_borrow_001",
			DocumentID:     "test_doc_internal_002",
			VersionNo:      "V1.0",
			ModuleType:     2, // 内部文档
			BorrowStatus:   3, // 已归还
			RecoverUserID:  "test_doc_user_002",
			RecoverTime:    &now,
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now,
			CreatedBy:      "test_doc_user_001",
			UpdatedBy:      "test_doc_user_001",
		},
		{
			ID:             "test_doc_relation_003",
			BorrowRecordID: "test_borrow_002",
			DocumentID:     "test_doc_external_001",
			VersionNo:      "V1.0",
			ModuleType:     3, // 外部文档
			BorrowStatus:   1, // 已借阅
			CreatedAt:      now.Add(-3 * 24 * time.Hour),
			UpdatedAt:      now.Add(-3 * 24 * time.Hour),
			CreatedBy:      "test_doc_user_002",
			UpdatedBy:      "test_doc_user_002",
		},
	}

	// 插入借阅文档关系数据
	for _, relation := range borrowDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试借阅文档关系失败: %w", err)
		}
	}

	// 6. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_category_001",
			Names:  "内部文档类别1",
		},
		{
			NodeID: "test_category_002",
			Names:  "内部文档类别2",
		},
		{
			NodeID: "test_category_003",
			Names:  "外部文档类别1",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	return &TestDataForDocuments{
		BorrowRecords:                   borrowRecords,
		BorrowDocumentRelations:         borrowDocRelations,
		Users:                           users,
		InternalDocuments:               internalDocs,
		ExternalDocuments:               externalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
	}, nil
}

// cleanupTestDataForDocuments 清理文档测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupTestDataForDocuments(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除借阅文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_doc_relation_%").Delete(&BorrowDocumentRelation{})

	// 删除借阅记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_borrow_%").Delete(&BorrowRecord{})

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_doc_internal_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_doc_external_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Where("node_id LIKE ?", "test_category_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_doc_user_%").Delete(&mapper.User{})
}

// TestDataForDocuments 文档测试数据结构
type TestDataForDocuments struct {
	BorrowRecords                   []BorrowRecord
	BorrowDocumentRelations         []BorrowDocumentRelation
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	ExternalDocuments               []mapper.ExternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
}
