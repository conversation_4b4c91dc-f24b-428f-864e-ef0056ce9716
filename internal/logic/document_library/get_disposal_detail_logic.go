package document_library

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDisposalDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDisposalDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDisposalDetailLogic {
	return &GetDisposalDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDisposalDetail 根据发放清单ID查询处置详情
// 参数:
//   - req: 查询请求，包含发放清单ID
//
// 返回值:
//   - resp: 处置详情响应，包含文件名称、文件编号和处置记录列表
//   - err: 错误信息，如果查询失败则返回相应错误
//
// 功能说明:
//   - 通过gRPC调用DocVault服务获取处置详情信息
//   - 将gRPC响应数据转换为API响应格式
//   - 处理交还人、回收人、处置人等相关信息
//   - 包含交还日期、回收日期、处置日期等时间信息
//   - 返回处置方式等详细信息
func (l *GetDisposalDetailLogic) GetDisposalDetail(req *types.GetDisposalDetailReq) (resp *types.GetDisposalDetailResp, err error) {
	// 调用DocVault gRPC服务获取处置详情
	detail, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDisposalDetail(l.ctx, &docvault.GetDisposalDetailReq{
		DistributeRecordFileId: req.DistributeListID,
	})
	if err != nil {
		l.Logger.Errorf("调用DocVault服务获取处置详情失败: %v", err)
		return nil, err
	}

	// 数据转换：将gRPC响应转换为API响应格式
	resp = l.convertToAPIResponse(detail)
	return resp, nil
}

// convertToAPIResponse 将gRPC响应数据转换为API响应格式
// 参数:
//   - detail: DocVault gRPC服务返回的处置详情数据
//
// 返回值:
//   - *types.GetDisposalDetailResp: 转换后的API响应数据
//
// 功能说明:
//   - 转换文件基本信息（文件名称、文件编号）
//   - 转换处置记录列表，包括交还人、回收人、处置人信息
//   - 转换时间戳信息（交还日期、回收日期、处置日期）
//   - 转换处置方式信息
func (l *GetDisposalDetailLogic) convertToAPIResponse(detail *docvault.GetDisposalDetailResp) *types.GetDisposalDetailResp {
	if detail == nil {
		l.Logger.Info("DocVault服务返回的处置详情数据为空")
		return &types.GetDisposalDetailResp{}
	}

	// 转换处置记录列表
	disposalRecords := make([]types.DisposalRecord, 0, len(detail.DisposalRecords))
	for _, record := range detail.DisposalRecords {
		if record != nil {
			disposalRecords = append(disposalRecords, types.DisposalRecord{
				HandoverPerson: record.HandoverPerson, // 交还人
				HandoverDate:   record.HandoverDate,   // 交还日期（毫秒级时间戳）
				RecyclePerson:  record.RecyclePerson,  // 回收人
				RecycleDate:    record.RecycleDate,    // 回收日期（毫秒级时间戳）
				DisposalPerson: record.DisposalPerson, // 处置人
				DisposalDate:   record.DisposalDate,   // 处置日期（毫秒级时间戳）
				DisposalMethod: record.DisposalMethod, // 处置方式
			})
		}
	}

	return &types.GetDisposalDetailResp{
		FileName:        detail.FileName,   // 文件名称
		FileNumber:      detail.FileNumber, // 文件编号
		DisposalRecords: disposalRecords,   // 处置记录列表
	}
}
