package document_library

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalPaperDownloadRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalPaperDownloadRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalPaperDownloadRecordsLogic {
	return &GetInternalPaperDownloadRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetInternalPaperDownloadRecords 获取内发纸质文件一次下载变更记录
// 功能: 根据文档ID分页查询内发纸质文件一次下载变更记录，只显示通过发放审批的数据
// 参数:
//   - req: 请求参数，包含文档ID和分页信息
//
// 返回值:
//   - resp: 内发纸质文件下载记录列表响应
//   - err: 错误信息
func (l *GetInternalPaperDownloadRecordsLogic) GetInternalPaperDownloadRecords(req *types.GetInternalPaperDownloadRecordsReq) (resp *types.GetInternalPaperDownloadRecordsResp, err error) {
	// 1. 查询发放记录文件
	distributeFiles, err := l.queryDistributeFiles(req.DocumentID)
	if err != nil {
		return nil, err
	}

	if len(distributeFiles) == 0 {
		return l.buildEmptyResponse(req), nil
	}

	// 2. 查询发放记录
	records, fileRecordMap, err := l.queryDistributeRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 3. 查询并过滤权限记录
	paperDownloadPermissions, err := l.queryAndFilterPermissions(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 4. 查询处置记录
	disposalRecordMap, err := l.queryDisposalRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 5. 查询回收记录
	recycleRecordMap, err := l.queryRecycleRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 6. 构建响应数据
	paperDownloadRecords := l.buildRecords(paperDownloadPermissions, fileRecordMap, records, disposalRecordMap, recycleRecordMap)

	// 5. 排序和分页
	l.sortRecordsByDistributeApplyTimeDesc(paperDownloadRecords)
	pagedRecords, total := l.applyPagination(paperDownloadRecords, req.PageInfo)

	return &types.GetInternalPaperDownloadRecordsResp{
		Data: pagedRecords,
		PageInfo: types.PageInfo{
			Page:     req.PageInfo.Page,
			PageSize: req.PageInfo.PageSize,
			Total:    total,
		},
	}, nil
}

// getPaperFileStatus 获取纸质文件状态
// 功能: 根据处置状态获取纸质文件状态
// 参数:
//   - disposeStatus: 处置状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
//
// 返回值:
//   - int32: 纸质文件状态（1未回收 | 2回收中 | 3已回收）
func (l *GetInternalPaperDownloadRecordsLogic) getPaperFileStatus(disposeStatus int32) int32 {
	switch disposeStatus {
	case 1: // 未回收
		return 1
	case 2: // 回收审批中
		return 2
	case 3, 4, 5: // 已回收、处置审批中、已处置
		return 3
	default:
		return 1
	}
}

// getFileDisposeStatus 获取文件处置状态
// 功能: 根据处置状态获取文件处置状态
// 参数:
//   - disposeStatus: 处置状态
//
// 返回值:
//   - int32: 文件处置状态（1未处置 | 2处置中 | 3已处置）
func (l *GetInternalPaperDownloadRecordsLogic) getFileDisposeStatus(disposeStatus int32) int32 {
	switch disposeStatus {
	case 1, 2, 3: // 未回收、回收审批中、已回收
		return 1 // 未处置
	case 4: // 处置审批中
		return 2 // 处置中
	case 5: // 已处置
		return 3 // 已处置
	default:
		return 1
	}
}

// getUserNickname 获取用户昵称
// 功能: 根据用户ID从Redis查询用户昵称，如果获取失败则返回用户ID
// 参数:
//   - userID: 用户ID
//
// 返回值:
//   - string: 用户昵称或用户ID
func (l *GetInternalPaperDownloadRecordsLogic) getUserNickname(userID string) string {
	if userID == "" {
		return ""
	}

	// 使用QuickNameTranslator从Redis查询用户昵称
	nickname := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userID)
	if nickname == "" {
		// 如果查询失败，返回用户ID作为备用
		return userID
	}

	return nickname
}

// queryDistributeFiles 查询发放记录文件
func (l *GetInternalPaperDownloadRecordsLogic) queryDistributeFiles(documentID string) ([]mapper.DistributeRecordFile, error) {
	distributeRecordFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeFiles, err := distributeRecordFileClient.FindApprovedByFileID(l.ctx, documentID)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录失败")
	}
	return distributeFiles, nil
}

// buildEmptyResponse 构建空响应
func (l *GetInternalPaperDownloadRecordsLogic) buildEmptyResponse(req *types.GetInternalPaperDownloadRecordsReq) *types.GetInternalPaperDownloadRecordsResp {
	return &types.GetInternalPaperDownloadRecordsResp{
		Data: []types.InternalPaperDownloadRecord{},
		PageInfo: types.PageInfo{
			Page:     req.PageInfo.Page,
			PageSize: req.PageInfo.PageSize,
			Total:    0,
		},
	}
}

// queryDistributeRecords 查询发放记录
func (l *GetInternalPaperDownloadRecordsLogic) queryDistributeRecords(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecord, map[string]mapper.DistributeRecordFile, error) {
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	recordIDs := make([]string, 0, len(distributeFiles))
	fileRecordMap := make(map[string]mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		recordIDs = append(recordIDs, file.RecordID)
		fileRecordMap[file.ID] = file
	}

	uniqueRecordIDs := utils.SliceDuplicate(recordIDs)
	records := make([]mapper.DistributeRecord, 0)
	for _, recordID := range uniqueRecordIDs {
		record, err := distributeRecordClient.FindByID(l.ctx, recordID)
		if err != nil {
			l.Logger.Errorf("查询发放记录失败, recordID: %s, error: %v", recordID, err)
			continue
		}
		if record.Status == 3 && record.DistributeType == 1 {
			records = append(records, record)
		}
	}

	return records, fileRecordMap, nil
}

// queryAndFilterPermissions 查询并过滤权限记录
func (l *GetInternalPaperDownloadRecordsLogic) queryAndFilterPermissions(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecordPermission, error) {
	distributeRecordPermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	fileRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	permissions, err := distributeRecordPermissionClient.FindByFileRecordIDs(l.ctx, fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询权限记录失败: %v", err)
		return nil, fmt.Errorf("查询权限记录失败")
	}

	paperDownloadPermissions := make([]mapper.DistributeRecordPermission, 0)
	for _, perm := range permissions {
		if perm.FileForm == 2 && perm.FilePermission == 3 {
			paperDownloadPermissions = append(paperDownloadPermissions, perm)
		}
	}

	return paperDownloadPermissions, nil
}

// queryDisposalRecords 查询处置记录
// 功能：通过发放记录文件ID查询对应的处置记录
// 参数：distributeFiles - 发放记录文件列表
// 返回值：发放记录文件ID到处置记录的映射，错误信息
func (l *GetInternalPaperDownloadRecordsLogic) queryDisposalRecords(distributeFiles []mapper.DistributeRecordFile) (map[string]mapper.DisposalRecord, error) {
	if len(distributeFiles) == 0 {
		return make(map[string]mapper.DisposalRecord), nil
	}

	// 提取发放记录文件ID
	distributeFileIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		distributeFileIDs = append(distributeFileIDs, file.ID)
	}

	// 查询处置记录文件关联
	disposalRecordFileClient := mapper.NewDisposalRecordFileClient(l.svcCtx.DocvaultDB)
	disposalRecordFiles, err := disposalRecordFileClient.FindByDistributeRecordFileIDs(l.ctx, distributeFileIDs)
	if err != nil {
		l.Logger.Errorf("查询处置记录文件关联失败: %v", err)
		return nil, fmt.Errorf("查询处置记录文件关联失败")
	}

	if len(disposalRecordFiles) == 0 {
		return make(map[string]mapper.DisposalRecord), nil
	}

	// 提取处置记录ID
	disposalRecordIDs := make([]string, 0, len(disposalRecordFiles))
	fileToDisposalMap := make(map[string]string) // 发放记录文件ID -> 处置记录ID
	for _, disposalFile := range disposalRecordFiles {
		disposalRecordIDs = append(disposalRecordIDs, disposalFile.RecordID)
		fileToDisposalMap[disposalFile.DistributeRecordFileID] = disposalFile.RecordID
	}

	// 去重处置记录ID
	disposalRecordIDs = utils.SliceDuplicate(disposalRecordIDs)

	// 查询处置记录
	disposalRecordClient := mapper.NewDisposalRecordClient(l.svcCtx.DocvaultDB)
	disposalRecords, err := disposalRecordClient.FindByIDs(l.ctx, disposalRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询处置记录失败: %v", err)
		return nil, fmt.Errorf("查询处置记录失败")
	}

	// 构建处置记录映射：处置记录ID -> 处置记录
	disposalRecordMap := make(map[string]mapper.DisposalRecord)
	for _, record := range disposalRecords {
		disposalRecordMap[record.ID] = record
	}

	// 构建最终映射：发放记录文件ID -> 处置记录
	result := make(map[string]mapper.DisposalRecord)
	for fileID, disposalID := range fileToDisposalMap {
		if disposalRecord, exists := disposalRecordMap[disposalID]; exists {
			result[fileID] = disposalRecord
		}
	}

	return result, nil
}

// queryRecycleRecords 查询回收记录
// 功能：通过发放记录文件ID查询对应的回收记录
// 参数：distributeFiles - 发放记录文件列表
// 返回值：发放记录文件ID到回收记录的映射，错误信息
func (l *GetInternalPaperDownloadRecordsLogic) queryRecycleRecords(distributeFiles []mapper.DistributeRecordFile) (map[string]mapper.RecycleRecord, error) {
	if len(distributeFiles) == 0 {
		return make(map[string]mapper.RecycleRecord), nil
	}

	// 提取发放记录文件ID
	distributeFileIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		distributeFileIDs = append(distributeFileIDs, file.ID)
	}

	// 查询回收记录文件关联
	recycleRecordFileClient := mapper.NewRecycleRecordFileClient(l.svcCtx.DocvaultDB)
	recycleRecordFiles, err := recycleRecordFileClient.FindByDistributeRecordFileIDs(l.ctx, distributeFileIDs)
	if err != nil {
		l.Logger.Errorf("查询回收记录文件关联失败: %v", err)
		return nil, fmt.Errorf("查询回收记录文件关联失败")
	}

	if len(recycleRecordFiles) == 0 {
		return make(map[string]mapper.RecycleRecord), nil
	}

	// 提取回收记录ID
	recycleRecordIDs := make([]string, 0, len(recycleRecordFiles))
	fileToRecycleMap := make(map[string]string) // 发放记录文件ID -> 回收记录ID
	for _, recycleFile := range recycleRecordFiles {
		recycleRecordIDs = append(recycleRecordIDs, recycleFile.RecordID)
		fileToRecycleMap[recycleFile.DistributeRecordFileID] = recycleFile.RecordID
	}

	// 去重回收记录ID
	recycleRecordIDs = utils.SliceDuplicate(recycleRecordIDs)

	// 查询回收记录
	recycleRecordClient := mapper.NewRecycleRecordClient(l.svcCtx.DocvaultDB)
	recycleRecords, err := recycleRecordClient.FindByIDs(l.ctx, recycleRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询回收记录失败: %v", err)
		return nil, fmt.Errorf("查询回收记录失败")
	}

	// 构建回收记录映射：回收记录ID -> 回收记录
	recycleRecordMap := make(map[string]mapper.RecycleRecord)
	for _, record := range recycleRecords {
		recycleRecordMap[record.ID] = record
	}

	// 构建最终映射：发放记录文件ID -> 回收记录
	result := make(map[string]mapper.RecycleRecord)
	for fileID, recycleID := range fileToRecycleMap {
		if recycleRecord, exists := recycleRecordMap[recycleID]; exists {
			result[fileID] = recycleRecord
		}
	}

	return result, nil
}

// buildRecords 构建响应数据
func (l *GetInternalPaperDownloadRecordsLogic) buildRecords(permissions []mapper.DistributeRecordPermission, fileRecordMap map[string]mapper.DistributeRecordFile, records []mapper.DistributeRecord, disposalRecordMap map[string]mapper.DisposalRecord, recycleRecordMap map[string]mapper.RecycleRecord) []types.InternalPaperDownloadRecord {
	paperDownloadRecords := make([]types.InternalPaperDownloadRecord, 0)

	for _, perm := range permissions {
		fileRecord := fileRecordMap[perm.FileRecordID]

		var distributeRecord *mapper.DistributeRecord
		for _, record := range records {
			if record.ID == fileRecord.RecordID {
				distributeRecord = &record
				break
			}
		}

		if distributeRecord == nil {
			continue
		}

		// 获取处置记录和回收记录信息
		disposalRecord, hasDisposal := disposalRecordMap[fileRecord.ID]
		recycleRecord, hasRecycle := recycleRecordMap[fileRecord.ID]

		paperDownloadRecord := types.InternalPaperDownloadRecord{
			RecordID:                perm.ID,
			WorkflowID:              distributeRecord.WorkflowID,
			DistributeRecordID:      distributeRecord.ID, // 发放记录ID
			RecipientUserID:         perm.UserID,
			RecipientUserName:       perm.UserName,
			PaperFileStatus:         l.getPaperFileStatus(perm.DisposeStatus),
			FileDisposeStatus:       l.getFileDisposeStatus(perm.DisposeStatus),
			DisposalMethod:          l.getDisposalMethodFromRecord(hasDisposal, disposalRecord),
			DistributeApplicant:     distributeRecord.Applicant,
			DistributeApplicantName: l.getUserNickname(distributeRecord.Applicant),
			DistributeApplyTime:     distributeRecord.ApplyDate,
			RecycleApplicant:        l.getRecycleApplicantByStatus(perm.DisposeStatus, hasRecycle, recycleRecord),
			RecycleApplicantName:    l.getRecycleApplicantNameByStatus(perm.DisposeStatus, hasRecycle, recycleRecord),
			RecycleApplyTime:        l.getRecycleApplyTimeByStatus(perm.DisposeStatus, hasRecycle, recycleRecord),
			InventoryID:             fileRecord.ID, // 清单ID（发放记录文件ID）
			DistributeApprovalInfo:  l.getDistributeApprovalInfoByStatus(perm.DisposeStatus, *distributeRecord),
			RecycleApprovalInfo:     l.getRecycleApprovalInfoByStatus(perm.DisposeStatus, hasRecycle, recycleRecord),
			DisposalApprovalInfo:    l.getDisposalApprovalInfoByStatus(perm.DisposeStatus, hasDisposal, disposalRecord),
		}

		paperDownloadRecords = append(paperDownloadRecords, paperDownloadRecord)
	}

	return paperDownloadRecords
}

// applyPagination 应用分页
func (l *GetInternalPaperDownloadRecordsLogic) applyPagination(records []types.InternalPaperDownloadRecord, pageInfo types.PageInfo) ([]types.InternalPaperDownloadRecord, uint64) {
	total := uint64(len(records))
	start := int((pageInfo.Page - 1) * pageInfo.PageSize)
	end := int(start + int(pageInfo.PageSize))

	if start >= len(records) {
		return []types.InternalPaperDownloadRecord{}, total
	} else if end > len(records) {
		return records[start:], total
	} else {
		return records[start:end], total
	}
}

// sortRecordsByDistributeApplyTimeDesc 按发放申请时间倒序排列
func (l *GetInternalPaperDownloadRecordsLogic) sortRecordsByDistributeApplyTimeDesc(records []types.InternalPaperDownloadRecord) {
	sort.Slice(records, func(i, j int) bool {
		return records[i].DistributeApplyTime > records[j].DistributeApplyTime
	})
}

// getDisposalMethodFromRecord 从处置记录获取处置方法
// 功能：从处置记录中获取真实的处置方法
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：处置方法
func (l *GetInternalPaperDownloadRecordsLogic) getDisposalMethodFromRecord(hasDisposal bool, disposalRecord mapper.DisposalRecord) string {
	if hasDisposal {
		return disposalRecord.Reason // 处置记录中的处置方法
	}
	return ""
}

// getRecycleApplicantFromRecord 从处置记录获取处置人（回收申请人）
// 功能：从处置记录中获取处置人作为回收申请人
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：回收申请人ID
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplicantFromRecord(hasDisposal bool, disposalRecord mapper.DisposalRecord) string {
	if hasDisposal {
		return disposalRecord.DisposalBy // 处置记录中的处置人
	}
	return ""
}

// getRecycleApplicantNameFromRecord 从处置记录获取处置人姓名
// 功能：从处置记录中获取处置人姓名作为回收申请人姓名
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：回收申请人姓名
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplicantNameFromRecord(hasDisposal bool, disposalRecord mapper.DisposalRecord) string {
	if hasDisposal {
		return l.getUserNickname(disposalRecord.DisposalBy) // 处置人的姓名
	}
	return ""
}

// getRecycleApplyTimeFromRecord 从处置记录获取处置时间
// 功能：从处置记录中获取处置时间作为回收申请时间
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：回收申请时间（毫秒级时间戳）
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplyTimeFromRecord(hasDisposal bool, disposalRecord mapper.DisposalRecord) int64 {
	if hasDisposal {
		return disposalRecord.UpdatedAt.UnixMilli() // 处置记录的更新时间
	}
	return 0
}

// getDistributeApprovalInfo 获取发放审批信息
// 功能：从发放记录中解析审批信息
// 参数：distributeRecord - 发放记录
// 返回值：审批信息
func (l *GetInternalPaperDownloadRecordsLogic) getDistributeApprovalInfo(distributeRecord mapper.DistributeRecord) types.ApprovalInfo {
	var mapperApprovalInfo mapper.ApprovalInfo
	if len(distributeRecord.ApprovalInfo) > 0 {
		if err := json.Unmarshal(distributeRecord.ApprovalInfo, &mapperApprovalInfo); err != nil {
			l.Logger.Errorf("解析发放审批信息失败: %v", err)
			return types.ApprovalInfo{}
		}
	}

	// 转换为 types.ApprovalInfo
	return l.convertMapperApprovalInfoToTypes(mapperApprovalInfo)
}

// getRecycleApprovalInfo 获取回收审批信息（已废弃，使用 getRecycleApprovalInfoFromRecord）
// 功能：目前回收审批信息暂时返回空，后续可以从回收记录中获取
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：审批信息
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApprovalInfo(hasDisposal bool, disposalRecord mapper.DisposalRecord) types.ApprovalInfo {
	// 已废弃：现在使用 getRecycleApprovalInfoFromRecord 从回收记录中获取
	return types.ApprovalInfo{}
}

// getRecycleApprovalInfoFromRecord 从回收记录获取回收审批信息
// 功能：从回收记录中解析审批信息
// 参数：recycleRecord - 回收记录
// 返回值：审批信息
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApprovalInfoFromRecord(recycleRecord mapper.RecycleRecord) types.ApprovalInfo {
	var mapperApprovalInfo mapper.ApprovalInfo
	if len(recycleRecord.ApprovalInfo) > 0 {
		if err := json.Unmarshal(recycleRecord.ApprovalInfo, &mapperApprovalInfo); err != nil {
			l.Logger.Errorf("解析回收审批信息失败: %v", err)
			return types.ApprovalInfo{}
		}
	}

	// 转换为 types.ApprovalInfo
	return l.convertMapperApprovalInfoToTypes(mapperApprovalInfo)
}

// getDisposalApprovalInfo 获取处置审批信息
// 功能：从处置记录中解析审批信息
// 参数：hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：审批信息
func (l *GetInternalPaperDownloadRecordsLogic) getDisposalApprovalInfo(hasDisposal bool, disposalRecord mapper.DisposalRecord) types.ApprovalInfo {
	if !hasDisposal {
		return types.ApprovalInfo{}
	}

	var mapperApprovalInfo mapper.ApprovalInfo
	if len(disposalRecord.ApprovalInfo) > 0 {
		if err := json.Unmarshal(disposalRecord.ApprovalInfo, &mapperApprovalInfo); err != nil {
			l.Logger.Errorf("解析处置审批信息失败: %v", err)
			return types.ApprovalInfo{}
		}
	}

	// 转换为 types.ApprovalInfo
	return l.convertMapperApprovalInfoToTypes(mapperApprovalInfo)
}

// convertMapperApprovalInfoToTypes 转换审批信息格式
// 功能：将 mapper.ApprovalInfo 转换为 types.ApprovalInfo，并填充用户昵称
// 参数：mapperInfo - mapper格式的审批信息
// 返回值：types格式的审批信息
func (l *GetInternalPaperDownloadRecordsLogic) convertMapperApprovalInfoToTypes(mapperInfo mapper.ApprovalInfo) types.ApprovalInfo {
	// 转换审核人
	auditors := make([]types.Approval, 0, len(mapperInfo.Auditors))
	for _, auditor := range mapperInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       auditor.UserID,
			UserNickname: l.getUserNickname(auditor.UserID),
			PassedDate:   auditor.PassedDate,
		})
	}

	// 转换批准人
	approvers := make([]types.Approval, 0, len(mapperInfo.Approvers))
	for _, approver := range mapperInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       approver.UserID,
			UserNickname: l.getUserNickname(approver.UserID),
			PassedDate:   approver.PassedDate,
		})
	}

	return types.ApprovalInfo{
		Auditors:  auditors,
		Approvers: approvers,
	}
}

// getDistributeApprovalInfoByStatus 根据流程状态获取发放审批信息
// 功能：根据处置状态判断是否显示发放审批信息
// 参数：disposeStatus - 处置状态，distributeRecord - 发放记录
// 返回值：审批信息（发放阶段及以后都显示）
func (l *GetInternalPaperDownloadRecordsLogic) getDistributeApprovalInfoByStatus(disposeStatus int32, distributeRecord mapper.DistributeRecord) types.ApprovalInfo {
	// 发放审批信息在所有阶段都显示（状态1-5）
	return l.getDistributeApprovalInfo(distributeRecord)
}

// getRecycleApprovalInfoByStatus 根据流程状态获取回收审批信息
// 功能：根据处置状态判断是否显示回收审批信息
// 参数：disposeStatus - 处置状态，hasRecycle - 是否有回收记录，recycleRecord - 回收记录
// 返回值：审批信息（已回收才显示）
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApprovalInfoByStatus(disposeStatus int32, hasRecycle bool, recycleRecord mapper.RecycleRecord) types.ApprovalInfo {
	// 只有已回收才显示回收审批信息（状态3-5）
	if disposeStatus >= 3 && hasRecycle {
		return l.getRecycleApprovalInfoFromRecord(recycleRecord)
	}
	return types.ApprovalInfo{} // 未回收和回收审批中不显示回收审批信息
}

// getDisposalApprovalInfoByStatus 根据流程状态获取处置审批信息
// 功能：根据处置状态判断是否显示处置审批信息
// 参数：disposeStatus - 处置状态，hasDisposal - 是否有处置记录，disposalRecord - 处置记录
// 返回值：审批信息（处置阶段才显示）
func (l *GetInternalPaperDownloadRecordsLogic) getDisposalApprovalInfoByStatus(disposeStatus int32, hasDisposal bool, disposalRecord mapper.DisposalRecord) types.ApprovalInfo {
	// 只有在处置阶段才显示处置审批信息（状态4-5）
	if disposeStatus >= 4 {
		return l.getDisposalApprovalInfo(hasDisposal, disposalRecord)
	}
	return types.ApprovalInfo{} // 发放和回收阶段不显示处置审批信息
}

// getRecycleApplicantByStatus 根据流程状态获取回收申请人
// 功能：根据处置状态判断是否显示回收申请人
// 参数：disposeStatus - 处置状态，hasRecycle - 是否有回收记录，recycleRecord - 回收记录
// 返回值：回收申请人ID（已回收才显示）
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplicantByStatus(disposeStatus int32, hasRecycle bool, recycleRecord mapper.RecycleRecord) string {
	// 只有已回收才显示回收申请人（状态3-5）
	if disposeStatus >= 3 && hasRecycle {
		return recycleRecord.RecycleBy // 回收记录中的回收申请人
	}
	return "" // 未回收和回收审批中不显示回收申请人
}

// getRecycleApplicantNameByStatus 根据流程状态获取回收申请人姓名
// 功能：根据处置状态判断是否显示回收申请人姓名
// 参数：disposeStatus - 处置状态，hasRecycle - 是否有回收记录，recycleRecord - 回收记录
// 返回值：回收申请人姓名（已回收才显示）
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplicantNameByStatus(disposeStatus int32, hasRecycle bool, recycleRecord mapper.RecycleRecord) string {
	// 只有已回收才显示回收申请人姓名（状态3-5）
	if disposeStatus >= 3 && hasRecycle {
		return l.getUserNickname(recycleRecord.RecycleBy) // 回收申请人的姓名
	}
	return "" // 未回收和回收审批中不显示回收申请人姓名
}

// getRecycleApplyTimeByStatus 根据流程状态获取回收申请时间
// 功能：根据处置状态判断是否显示回收申请时间
// 参数：disposeStatus - 处置状态，hasRecycle - 是否有回收记录，recycleRecord - 回收记录
// 返回值：回收申请时间（已回收才显示）
func (l *GetInternalPaperDownloadRecordsLogic) getRecycleApplyTimeByStatus(disposeStatus int32, hasRecycle bool, recycleRecord mapper.RecycleRecord) int64 {
	// 只有已回收才显示回收申请时间（状态3-5）
	if disposeStatus >= 3 && hasRecycle {
		return recycleRecord.UpdatedAt.UnixMilli() // 回收记录的更新时间
	}
	return 0 // 未回收和回收审批中不显示回收申请时间
}
