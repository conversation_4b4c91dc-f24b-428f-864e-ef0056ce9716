package document_library

import (
	"context"
	"k8s.io/apimachinery/pkg/util/json"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackRecycleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackRecycleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackRecycleLogic {
	return &WorkflowPreCallbackRecycleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WorkflowPreCallbackRecycleLogic) WorkflowPreCallbackRecycle(req *types.WorkflowInfoReq) (resp *types.WorkflowInfoResp, err error) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)

	// 处理表单信息
	var recycleApprovalInfo RecycleApprovalInfo
	if err = json.Unmarshal([]byte(req.FormContent), &recycleApprovalInfo); err != nil {
		l.Logger.Errorf("处理表单信息失败: %v", err)
		return nil, err
	}

	data := recycleApprovalInfo.Data

	updateUserDisposalStatusReq := l.apiReqToRpcReq(data)
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).UpdateUserDisposalStatus(l.ctx, updateUserDisposalStatusReq)
	if err != nil {
		l.Logger.Errorf("预处理回收审批失败: %v", err)
		return nil, err
	}

	return &types.WorkflowInfoResp{}, nil
}

func (l *WorkflowPreCallbackRecycleLogic) apiReqToRpcReq(recycleApprovalInfo RecycleApprovalInfoData) *docvault.UpdateUserDisposalStatusReq {
	updateUserDisposalStatusReq := &docvault.UpdateUserDisposalStatusReq{}
	updateUserDisposalStatusReq.DistributeId = recycleApprovalInfo.DistributeID
	var recycles []*docvault.RecycleList
	for _, v := range recycleApprovalInfo.RecycleList {
		var permissions []*docvault.FilePermission
		for _, permission := range v.Permissions {
			permissions = append(permissions, &docvault.FilePermission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				ReceivedBy:     permission.ReceivedBy,
			})

			recycles = append(recycles, &docvault.RecycleList{
				InventoryId: v.InventoryID,
				Permissions: permissions,
			})
		}
	}
	updateUserDisposalStatusReq.Recycles = recycles
	updateUserDisposalStatusReq.DisposalStatus = 2
	return updateUserDisposalStatusReq
}

type RecycleApprovalInfo struct {
	Data RecycleApprovalInfoData `json:"data"`
}

type RecycleApprovalInfoData struct {
	DistributeID  string        `json:"distributeId"`  // 发放列表ID
	RecycleDate   int64         `json:"recycleDate"`   // 回收日期
	RecycleReason string        `json:"recycleReason"` // 回收原因
	OtherReason   string        `json:"otherReason"`   // 其他原因
	RecycleList   []RecycleList `json:"recycleList"`   // 回收清单
	WorkflowID    string        `json:"workflowId"`    // 工作流ID
}

type RecycleList struct {
	InventoryID string        `json:"inventoryId"` // 清单列表ID
	FileID      string        `json:"fileId"`      // 文档id
	Permissions []Permissions `json:"permissions"` // 文件权限
}

type Permissions struct {
	FileForm       int      `json:"fileForm"`       // 文件形式
	FilePermission int      `json:"filePermission"` // 文件权限
	ReceivedBy     []string `json:"receivedBy"`     // 接收人
}
