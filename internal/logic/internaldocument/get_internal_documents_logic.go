package internaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDocumentsLogic {
	return &GetInternalDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDocumentsLogic) GetInternalDocuments(req *types.GetInternalDocumentsReq) (resp *types.GetInternalDocumentsResp, err error) {

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Page(
		l.ctx,
		&docvault.InternalDocumentPageReq{
			PageInfo: &docvault.PageInfo{
				Page:     int32(req.PageInfo.Page),
				PageSize: int32(req.PageInfo.PageSize),
				NoPage:   req.PageInfo.NoPage,
			},
			DocCategoryIds: req.DocCategoryIDs,
			DepartmentIds:  req.DepartmentIDs,
			Status:         int32(req.Status),
			HasAttachment:  int32(req.HasAttachment),
			No:             req.No,
			OriginalNo:     req.OriginalNo,
			Name:           req.Name,
		},
	)
	if err != nil {
		return
	}

	data := l.buildData(rs)

	resp = &types.GetInternalDocumentsResp{
		PageInfo: types.PageInfo{
			Total: uint64(rs.Total),
		},
		Data: data,
	}
	return
}

func (l *GetInternalDocumentsLogic) buildData(rs *docvault.InternalDocumentPageResp) []types.InternalDocumentInfo {
	if len(rs.Data) == 0 {
		return []types.InternalDocumentInfo{}
	}
	data := make([]types.InternalDocumentInfo, 0)
	docCategoryIDs := make([]string, 0)
	for _, item := range rs.Data {
		docCategoryIDs = append(docCategoryIDs, item.DocCategoryId)
		auditors, approvers := l.buildApprovalInfo(item)

		// 处理多选字段的名称翻译
		departmentNames := make([]string, 0, len(item.DepartmentIds))
		for _, deptID := range item.DepartmentIds {
			departmentNames = append(departmentNames, l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, deptID))
		}

		authorNicknames := make([]string, 0, len(item.AuthorIds))
		for _, authorID := range item.AuthorIds {
			authorNicknames = append(authorNicknames, l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, authorID))
		}

		data = append(data, types.InternalDocumentInfo{
			ID:                    item.Id,
			No:                    item.No,
			VersionNo:             item.VersionNo,
			OriginalNo:            item.OriginalNo,
			OriginalVersionNo:     item.OriginalVersionNo,
			Name:                  item.Name,
			EnglishName:           item.EnglishName, // 0.5版本新增：文件英文名称
			DocCategoryID:         item.DocCategoryId,
			DepartmentIDs:         item.DepartmentIds, // 0.4版本调整：编制部门改为多选数组
			DepartmentNames:       departmentNames,    // 0.4版本调整：编制部门名称数组
			AuthorIDs:             item.AuthorIds,     // 0.4版本调整：编制人改为多选数组
			AuthorNicknames:       authorNicknames,    // 0.4版本调整：编制人姓名数组
			PublishDate:           item.PublishDate,
			EffectiveDate:         item.EffectiveDate,
			ReplacementDocName:    item.ReplacementDocName,    // 0.5版本新增：替代文档名称
			ReplacementDocVersion: item.ReplacementDocVersion, // 0.5版本新增：替代文档版本
			Remark:                item.Remark,                // 0.5版本新增：备注
			Status:                int(item.Status),
			ApprovalInfo: types.ApprovalInfo{
				Auditors:  auditors,
				Approvers: approvers,
			},
		})
	}

	// 查询分类名称
	docCategories, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, docCategoryIDs)
	if err != nil {
		return data
	}

	docCategoryMap := make(map[string]string)
	for _, docCategory := range docCategories {
		docCategoryMap[docCategory.NodeID] = docCategory.Names
	}

	for i := range data {
		data[i].DocCategoryName = docCategoryMap[data[i].DocCategoryID]
	}

	return data
}

func (l *GetInternalDocumentsLogic) buildApprovalInfo(rs *docvault.InternalDocumentPageItem) ([]types.Approval, []types.Approval) {
	auditors := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	approvers := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	return auditors, approvers
}
