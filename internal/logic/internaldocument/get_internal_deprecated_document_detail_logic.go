package internaldocument

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDeprecatedDocumentDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDeprecatedDocumentDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDeprecatedDocumentDetailLogic {
	return &GetInternalDeprecatedDocumentDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDeprecatedDocumentDetailLogic) GetInternalDeprecatedDocumentDetail(req *types.GetInternalDeprecatedDocumentDetailReq) (resp *types.GetInternalDeprecatedDocumentDetailResp, err error) {
	// 使用作废记录查询服务获取内部作废文档详情
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetInternalDeprecatedDocumentDetail(l.ctx, req)
}
