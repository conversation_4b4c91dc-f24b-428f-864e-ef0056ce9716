package internaldocument

import (
	"context"
	"fmt"
	"time"

	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"nebula/internal/utils/statusx"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateInternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateInternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInternalDocumentLogic {
	return &CreateInternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateInternalDocumentLogic) CreateInternalDocument(req *types.CreateInternalDocumentReq) (resp *types.CreateInternalDocumentResp, err error) {
	// 查询文件编号
	noPrefix, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, req.DocCategoryID)
	if err != nil {
		l.Logger.Errorf("GetBusinessDictionaryNodeRelationByNodeID error: %v", err)
		return
	}

	// 查询组织架构信息
	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, utils.GetContextOrganizationID(l.ctx))
	if err != nil {
		l.Logger.Errorf("GetOrganizationInfo error: %v", err)
		return
	}

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Create(
		l.ctx,
		&docvault.InternalDocumentCreateReq{
			NoPrefix:          fmt.Sprintf("%s/%s", organizationInfo.Code, noPrefix.Codes),
			Name:              req.Name,
			EnglishName:       req.EnglishName, // 0.5版本新增：文件英文名称
			FileId:            req.FileID,
			DocCategoryId:     req.DocCategoryID,
			DepartmentIds:     req.DepartmentIDs, // 0.4版本调整：编制部门改为多选数组
			AuthorIds:         req.AuthorIDs,     // 0.4版本调整：编制人改为多选数组
			PublishDate:       req.PublishDate,
			EffectiveDate:     req.EffectiveDate,
			OriginalNo:        req.OriginalNo,
			OriginalVersionNo: req.OriginalVersionNo,
			Remark:            req.Remark, // 0.5版本新增：备注
		},
	)
	if err != nil {
		if statusx.IsRpcAlreadyExists(err) {
			respx.NewDefaultError("有文件已存在，新增失败，请检查后重新新增！")
		}
		return
	}

	err = l.createBusinessDictionaryRelation(req, rs.Id)

	return
}

// 创建业务字典关系
func (l *CreateInternalDocumentLogic) createBusinessDictionaryRelation(req *types.CreateInternalDocumentReq, businessID string) error {
	return mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).CreateBusinessDictionaryRelation(l.ctx, mapper.BusinessDictionaryRelation{
		ID:               l.svcCtx.IdGenerator.GenerateIDString(),
		BusinessID:       businessID,
		BusinessType:     consts.BusinessDictionaryBusinessTypeInternalDocumentCategory,
		DictionaryNodeID: req.DocCategoryID,
		CreatedAt:        time.Now(),
		CreatedBy:        utils.GetContextUserID(l.ctx),
	})
}
