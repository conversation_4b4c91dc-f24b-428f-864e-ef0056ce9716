package internaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDocumentLogic {
	return &GetInternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDocumentLogic) GetInternalDocument(req *types.GetInternalDocumentReq) (resp *types.GetInternalDocumentResp, err error) {

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Get(
		l.ctx,
		&docvault.InternalDocumentGetReq{
			Id: req.ID,
		},
	)
	if err != nil {
		return
	}

	auditors, approvers := l.buildApprovalInfo(rs)

	// 处理多选字段的用户昵称翻译
	departmentNames := make([]string, 0, len(rs.DepartmentIds))
	for _, deptID := range rs.DepartmentIds {
		// 暂时使用部门ID作为名称，后续可以通过其他服务获取部门名称
		departmentNames = append(departmentNames, l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, deptID))
	}

	authorNicknames := make([]string, 0, len(rs.AuthorIds))
	for _, authorID := range rs.AuthorIds {
		authorNicknames = append(authorNicknames, l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, authorID))
	}

	resp = &types.GetInternalDocumentResp{
		ID:                rs.Id,
		No:                rs.No,
		VersionNo:         rs.VersionNo,
		OriginalNo:        rs.OriginalNo,
		OriginalVersionNo: rs.OriginalVersionNo,
		Name:              rs.Name,
		EnglishName:       rs.EnglishName, // 0.5版本新增：文件英文名称
		DocCategoryID:     rs.DocCategoryId,
		DepartmentIDs:     rs.DepartmentIds, // 0.4版本调整：编制部门改为多选数组
		DepartmentNames:   departmentNames,  // 0.4版本调整：编制部门名称数组
		AuthorIDs:         rs.AuthorIds,     // 0.4版本调整：编制人改为多选数组
		AuthorNicknames:   authorNicknames,  // 0.4版本调整：编制人姓名数组
		PublishDate:       rs.PublishDate,
		EffectiveDate:     rs.EffectiveDate,
		Remark:            rs.Remark, // 0.5版本新增：备注
		Status:            int(rs.Status),
		FileID:            rs.FileId,
		ApprovalInfo: types.ApprovalInfo{
			Auditors:  auditors,
			Approvers: approvers,
		},
	}

	return
}

func (l *GetInternalDocumentLogic) buildApprovalInfo(rs *docvault.InternalDocumentGetResp) ([]types.Approval, []types.Approval) {
	auditors := make([]types.Approval, 0)
	for _, auditor := range rs.ApprovalInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       auditor.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, auditor.UserId),
			PassedDate:   auditor.PassedDate,
		})
	}

	approvers := make([]types.Approval, 0)
	for _, approver := range rs.ApprovalInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       approver.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, approver.UserId),
			PassedDate:   approver.PassedDate,
		})
	}
	return auditors, approvers
}
