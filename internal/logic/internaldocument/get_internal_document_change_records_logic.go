package internaldocument

import (
	"context"
	"strings"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDocumentChangeRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewGetInternalDocumentChangeRecordsLogic 创建内部文档变更记录查询逻辑处理器
// 功能：初始化内部文档变更记录查询的业务逻辑处理器
// 参数：ctx - 上下文，svcCtx - 服务上下文
// 返回值：内部文档变更记录查询逻辑处理器实例
func NewGetInternalDocumentChangeRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDocumentChangeRecordsLogic {
	return &GetInternalDocumentChangeRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetInternalDocumentChangeRecords 获取内部文档变更记录
// 功能：根据文档ID查询该文档的所有历史版本变更记录，按更新时间倒序排列
// 参数：req - 查询请求参数，包含文档ID和分页信息
// 返回值：变更记录列表响应，错误信息
// 注意事项：
//   - 查询条件为 main_id = DocumentId 的所有历史版本记录
//   - 按 updated_at 倒序排列
//   - 支持分页查询
func (l *GetInternalDocumentChangeRecordsLogic) GetInternalDocumentChangeRecords(req *types.GetInternalDocumentChangeRecordsReq) (resp *types.GetInternalDocumentChangeRecordsResp, err error) {
	// 参数验证
	if req.DocumentId == "" {
		l.Logger.Error("文档ID不能为空")
		return &types.GetInternalDocumentChangeRecordsResp{
			Data: []types.InternalDocumentChangeRecord{},
			PageInfo: types.PageInfo{
				Page:     req.Page,
				PageSize: req.PageSize,
				Total:    0,
			},
		}, nil
	}

	// 设置默认分页参数
	page := int(req.Page)
	if page <= 0 {
		page = 1
	}
	pageSize := int(req.PageSize)
	if pageSize <= 0 {
		pageSize = 10
	}

	// 创建内部文档库客户端
	internalDocClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)

	// 查询变更记录
	records, total, err := internalDocClient.GetChangeRecords(l.ctx, req.DocumentId, page, pageSize, req.NoPage)
	if err != nil {
		l.Logger.Errorf("查询内部文档变更记录失败：%v", err)
		return nil, err
	}

	// 收集需要批量查询的类别ID
	categoryIDs := utils.ExtractSliceFieldAndDuplicate(records, func(record mapper.InternalDocumentLibrary) string {
		return record.DocCategoryID
	})

	// 批量查询类别名称
	categoryNameMap := make(map[string]string)
	if len(categoryIDs) > 0 {
		businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB)
		if relations, err := businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, categoryIDs); err == nil {
			for _, relation := range relations {
				categoryNameMap[relation.NodeID] = relation.Names
			}
		}
	}

	// 转换数据格式
	changeRecords := l.convertToChangeRecords(records, categoryNameMap)

	return &types.GetInternalDocumentChangeRecordsResp{
		Data: changeRecords,
		PageInfo: types.PageInfo{
			Page:     uint64(page),
			PageSize: uint64(pageSize),
			Total:    uint64(total),
		},
	}, nil
}

// convertToChangeRecords 转换数据格式
// 功能：将数据库记录转换为API响应格式
// 参数：records - 数据库记录列表，categoryNameMap - 类别名称映射
// 返回值：变更记录列表
func (l *GetInternalDocumentChangeRecordsLogic) convertToChangeRecords(records []mapper.InternalDocumentLibrary, categoryNameMap map[string]string) []types.InternalDocumentChangeRecord {
	changeRecords := make([]types.InternalDocumentChangeRecord, 0, len(records))

	for _, record := range records {
		// 获取审批信息
		approvalInfo := record.GetApprovalInfo()

		// 转换审核人信息
		auditors := l.convertApprovalItems(approvalInfo.Auditors)

		// 转换批准人信息
		approvers := l.convertApprovalItems(approvalInfo.Approvers)

		// 处理多选字段的名称翻译（0.4版本调整：支持多选字段）
		departmentNames := l.translateDepartmentNames(record.DepartmentIDs)
		authorNames := l.translateAuthorNames(record.AuthorIDs)

		changeRecords = append(changeRecords, types.InternalDocumentChangeRecord{
			ID:                    record.ID,
			DocumentNo:            record.No,
			DocumentVersion:       record.Version,
			OriginalNo:            record.OriginalNo,
			OriginalVersion:       record.OriginalVersionNo,
			DocumentName:          record.Name,
			EnglishName:           record.EnglishName, // 0.5版本新增：文件英文名称（等待数据库模型更新）
			DocumentCategory:      categoryNameMap[record.DocCategoryID],
			DepartmentNames:       departmentNames,              // 0.4版本调整：编制部门名称数组
			AuthorNames:           authorNames,                  // 0.4版本调整：编制人姓名数组
			ReplacementDocName:    record.ReplacementDocName,    // 0.5版本新增：替代文档名称（等待数据库模型更新）
			ReplacementDocVersion: record.ReplacementDocVersion, // 0.5版本新增：替代文档版本（等待数据库模型更新）
			Remark:                record.Remark,                // 0.5版本新增：备注（等待数据库模型更新）
			Auditors:              auditors,
			Approvers:             approvers,
			PublishDate:           record.PublishDate.UnixMilli(),
			EffectiveDate:         record.EffectiveDate.UnixMilli(),
			OperationType:         int(record.OperationType),
			UpdatedAt:             record.UpdatedAt.UnixMilli(),
		})
	}

	return changeRecords
}

// convertApprovalItems 转换审批项
// 功能：将数据库中的审批项转换为API格式
// 参数：items - 审批项列表
// 返回值：转换后的审批项列表
func (l *GetInternalDocumentChangeRecordsLogic) convertApprovalItems(items []mapper.ApprovalItem) []types.Approval {
	approvals := make([]types.Approval, 0, len(items))
	for _, item := range items {
		approvals = append(approvals, types.Approval{
			UserID:       item.UserID,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserID),
			PassedDate:   item.PassedDate,
		})
	}
	return approvals
}

// translateDepartmentNames 翻译部门ID字符串为部门名称数组
// 参数 departmentIDs: 部门ID字符串，多个ID用顿号分隔
// 返回值: 部门名称数组
func (l *GetInternalDocumentChangeRecordsLogic) translateDepartmentNames(departmentIDs string) []string {
	if departmentIDs == "" {
		return []string{}
	}

	// 分割部门ID字符串
	ids := strings.Split(departmentIDs, "、")
	names := make([]string, 0, len(ids))

	for _, id := range ids {
		id = strings.TrimSpace(id)
		if id != "" {
			name := l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, id)
			if name != "" {
				names = append(names, name)
			}
		}
	}

	return names
}

// translateAuthorNames 翻译编制人ID字符串为编制人姓名数组
// 参数 authorIDs: 编制人ID字符串，多个ID用顿号分隔
// 返回值: 编制人姓名数组
func (l *GetInternalDocumentChangeRecordsLogic) translateAuthorNames(authorIDs string) []string {
	if authorIDs == "" {
		return []string{}
	}

	// 分割编制人ID字符串
	ids := strings.Split(authorIDs, "、")
	names := make([]string, 0, len(ids))

	for _, id := range ids {
		id = strings.TrimSpace(id)
		if id != "" {
			name := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, id)
			if name != "" {
				names = append(names, name)
			}
		}
	}

	return names
}
