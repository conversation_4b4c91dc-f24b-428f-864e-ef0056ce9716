package internaldocument

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDeprecatedDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDeprecatedDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDeprecatedDocumentsLogic {
	return &GetInternalDeprecatedDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDeprecatedDocumentsLogic) GetInternalDeprecatedDocuments(req *types.GetInternalDeprecatedDocumentsReq) (resp *types.GetInternalDeprecatedDocumentsResp, err error) {
	// 使用现有的作废记录查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetInternalDeprecatedDocuments(l.ctx, req)
}
