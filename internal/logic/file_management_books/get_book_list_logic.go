package file_management_books

import (
	"context"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBookListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetBookListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBookListLogic {
	return &GetBookListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBookListLogic) GetBookList(req *types.GetBookListReq) (resp *types.GetBookListResp, err error) {
	orgId := utils.GetContextOrganizationID(l.ctx)
	pageInfo := &docvault.PageInfo{
		Page:     int32(req.Page),
		PageSize: int32(req.PageSize),
		NoPage:   req.NoPage,
	}
	bookInfoResp, err := docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).GetBookList(l.ctx, &docvault.GetBookListReq{
		PageInfo:          pageInfo,
		Number:            req.Number,
		Name:              req.Name,
		Author:            req.Author,
		Publisher:         req.Publisher,
		DictionaryNodeIds: req.DictionaryNodeIds,
		OnBorrow:          req.OnBorrow,
		OrganizationId:    orgId,
	})
	if err != nil {
		l.Logger.Errorf("查询书籍列表信息失败：%v", err)
		return nil, err
	}

	bookListResp := l.rpcRespToApiResp(bookInfoResp)

	return bookListResp, nil
}

func (l *GetBookListLogic) rpcRespToApiResp(bookInfoResp *docvault.GetBookListResp) *types.GetBookListResp {
	var bookInfo []types.BookListInfo
	for _, v := range bookInfoResp.Data {
		// 借用人
		var bookBorrowUsers []types.BookUser
		for _, user := range v.BorrowUsers {
			bookBorrowUsers = append(bookBorrowUsers, types.BookUser{
				UserID:   user.UserId,
				Nickname: user.Nickname,
				Date:     user.BorrowTime,
			})
		}
		// 领用人
		var bookReceiveUsers []types.BookUser
		for _, user := range v.ReceiveUsers {
			bookReceiveUsers = append(bookReceiveUsers, types.BookUser{
				UserID:   user.UserId,
				Nickname: user.Nickname,
				Date:     user.ReceiveTime,
			})
		}
		// 书籍列表信息
		bookInfo = append(bookInfo, types.BookListInfo{
			ID:               v.Id,
			Status:           v.Status,
			Number:           v.Number,
			Name:             v.Name,
			Author:           v.Author,
			BookType:         v.BookType,
			Publisher:        v.Publisher,
			RegisterCount:    int(v.RegisterCount),
			ReceiveCount:     int(v.ReceiveCount),
			BorrowCount:      int(v.BorrowCount),
			OnBorrow:         v.OnBorrow,
			SurplusCount:     int(v.SurplusCount),
			Remark:           v.Remark, // 0.5版本新增：备注
			DictionaryNodeID: v.DictionaryNodeId,
			BookFileInfo: types.BookFileInfo{
				FileId:   v.FileId,
				FileName: addons.NewQuickNameTranslatorImpl(l.svcCtx.RedisAddons).TranslateFileName(l.ctx, v.FileId),
			},
			BookBorrowUsers:  bookBorrowUsers,
			BookReceiveUsers: bookReceiveUsers,
		})
	}
	return &types.GetBookListResp{
		Total: int64(bookInfoResp.Total),
		Data:  bookInfo,
	}
}
