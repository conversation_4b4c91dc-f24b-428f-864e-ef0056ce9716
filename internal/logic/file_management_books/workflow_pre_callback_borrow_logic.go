package file_management_books

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackBorrowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackBorrowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackBorrowLogic {
	return &WorkflowPreCallbackBorrowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// WorkflowPreCallbackBorrow 处理工作流预回调借用事件
// 参数:
//   - req: 工作流信息请求
//
// 返回值:
//   - resp: 工作流信息响应
//   - err: 错误信息
//
// 功能: 在工作流启动前保存书籍借用信息，设置状态为待审批
func (l *WorkflowPreCallbackBorrowLogic) WorkflowPreCallbackBorrow(req *types.WorkflowInfoReq) (resp *types.WorkflowInfoResp, err error) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)

	// 处理表单信息
	var bookBorrow BookBorrow
	if err = json.Unmarshal([]byte(req.FormContent), &bookBorrow); err != nil {
		l.Logger.Errorf("处理表单信息失败: %v", err)
		return nil, err
	}

	data := bookBorrow.Data

	// 调用保存借书接口
	_, err = docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).SaveBorrowInfo(l.ctx, &docvault.BookBorrowReq{
		BookId:     data.BookID,
		Reason:     data.Reason,
		Status:     2, // 待审批
		WorkflowId: req.WorkflowID,
	})
	if err != nil {
		l.Logger.Errorf("调用保存借书接口失败: %v", err)
		return nil, err
	}

	return nil, nil
}

type BookBorrow struct {
	Data BookBorrowInfo `json:"data"`
}

type BookBorrowInfo struct {
	BookID string `json:"bookId"`
	Reason string `json:"reason"`
}
