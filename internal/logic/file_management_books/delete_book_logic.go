package file_management_books

import (
	"context"
	"errors"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteBookLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBookLogic {
	return &DeleteBookLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBookLogic) DeleteBook(req *types.DeleteBookReq) (resp *types.DeleteBookResp, err error) {
	deleteBookResp, err := docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).DeleteBook(l.ctx, &docvault.DeleteBookReq{Id: req.ID})
	if err != nil {
		l.Logger.Errorf("书籍信息删除失败:%v", err)
		return nil, err
	}

	if deleteBookResp.Code != 0 {
		return nil, errors.New(deleteBookResp.Msg)
	}

	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).DeleteBusinessDictionaryRelationByBusinessID(l.ctx, req.ID)
	if err != nil {
		l.Logger.Errorf("删除节点关联信息失败：%v", err)
		return nil, errors.New("书籍信息删除成功，节点关联信息删除失败")
	}

	return &types.DeleteBookResp{}, nil
}
