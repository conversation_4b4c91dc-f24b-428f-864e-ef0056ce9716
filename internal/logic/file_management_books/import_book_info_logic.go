package file_management_books

import (
	"context"
	"errors"
	"fmt"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"net/http"
	"time"

	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type ImportBookInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewImportBookInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportBookInfoLogic {
	return &ImportBookInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type ExcelInfo struct {
	Name          string
	Author        string
	Publisher     string
	RegisterCount int
	BookType      string
}

func (l *ImportBookInfoLogic) ImportBookInfo(req *types.ImportBookReq) (resp *types.ImportBookResp, err error) {
	excelFileInfo, err := l.svcCtx.PhoenixClient.GetFileInfo(l.ctx, req.ExcelID)
	if err != nil {
		return nil, err
	}

	// 解析excel文件
	excelInfos, err := l.parseTheFile(excelFileInfo.Url)
	if err != nil {
		l.Logger.Errorf("解析excel文件错误：%v", err)
		return nil, err
	}

	// 信息校验
	excelInfoMap, excelBookTypes, err := l.fileInformationVerification(excelInfos, req.FileInfo)
	if err != nil {
		return nil, err
	}

	//类型校验
	excelBookTypeMap, err := l.typeValidation(excelBookTypes, req.DictionaryID)
	if err != nil {
		return nil, err
	}

	var importBookInfo []*docvault.ImportBookInfo

	for _, v := range excelInfos {
		importBookInfo = append(importBookInfo, &docvault.ImportBookInfo{
			DictionaryNodeId: excelBookTypeMap[v.BookType].NodeID,
			Name:             v.Name,
			Author:           v.Author,
			Publisher:        v.Publisher,
			RegisterCount:    int32(v.RegisterCount),
			BookType:         v.BookType,
			Number:           excelBookTypeMap[v.BookType].Codes,
			FileId:           excelInfoMap[v.Name],
		})
	}

	importBookResp, err := docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).ImportBook(l.ctx, &docvault.ImportBookReq{
		ImportBookInfo: importBookInfo,
	})
	if err != nil {
		l.Logger.Errorf("导入书籍信息失败：%v", err)
		return nil, err
	}

	var businessDictionaryRelations []mapper.BusinessDictionaryRelation
	for _, v := range importBookResp.Data {
		businessDictionaryRelations = append(businessDictionaryRelations, mapper.BusinessDictionaryRelation{
			ID:               l.svcCtx.IdGenerator.GenerateIDString(),
			DictionaryNodeID: v.DictionaryNodeId,
			BusinessID:       v.BookId,
			BusinessType:     consts.BusinessDictionaryBusinessTypeBookType,
			CreatedAt:        time.Now(),
			CreatedBy:        utils.GetContextUserID(l.ctx),
		})
	}

	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).BatchCreateBusinessDictionaryRelation(l.ctx, businessDictionaryRelations)
	if err != nil {
		return nil, err
	}

	return &types.ImportBookResp{}, nil
}

func (l *ImportBookInfoLogic) typeValidation(excelBookTypes []string, dictID string) (map[string]mapper.BusinessDictionaryNodeRelation, error) {
	excelBookTypeMap := make(map[string]mapper.BusinessDictionaryNodeRelation)
	var excelTypes []string
	for _, v := range excelBookTypes {
		if _, ok := excelBookTypeMap[v]; !ok {
			excelBookTypeMap[v] = mapper.BusinessDictionaryNodeRelation{}
			excelTypes = append(excelTypes, v)
		}
	}
	nodeRelations, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(l.ctx, excelTypes, dictID)
	if err != nil {
		return nil, err
	}
	if len(nodeRelations) != len(excelTypes) {
		return nil, errors.New("存在书籍类型错误")
	}

	// 获取组织code
	currentLoginUser := utils.GetCurrentLoginUser(l.ctx)
	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, currentLoginUser.OrganizationId)
	if err != nil {
		return nil, err
	}

	for _, v := range nodeRelations {
		if v.Codes != "" {
			v.Codes = organizationInfo.Code + "/" + v.Codes + "-"
		} else {
			v.Codes = organizationInfo.Code + "/"
		}
		excelBookTypeMap[v.Names] = v
	}
	return excelBookTypeMap, nil
}

func (l *ImportBookInfoLogic) fileInformationVerification(excelInfos []ExcelInfo, fileInfos []types.FileInfo) (map[string]string, []string, error) {
	if len(excelInfos) < len(fileInfos) {
		return nil, nil, errors.New("台账与文件不匹配，提交失败")
	}

	var excelBookTypes []string

	excelInfoMap := make(map[string]string, len(excelInfos))
	for _, v := range excelInfos {
		excelBookTypes = append(excelBookTypes, v.BookType)
		excelInfoMap[v.Name] = ""
	}
	for _, v := range fileInfos {
		_, ok := excelInfoMap[v.FileName]
		if !ok {
			return nil, nil, errors.New("台账与文件不匹配，提交失败")
		}
		excelInfoMap[v.FileName] = v.FileID
	}
	return excelInfoMap, excelBookTypes, nil
}

func (l *ImportBookInfoLogic) parseTheFile(url string) ([]ExcelInfo, error) {
	// 下载文件
	respHttp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer respHttp.Body.Close()

	// 用excelize解析
	f, err := excelize.OpenReader(respHttp.Body)
	if err != nil {
		return nil, err
	}

	// 获取第一个sheet名称
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, err
	}

	var result []ExcelInfo

	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}

	// 从第二行开始遍历
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) < 5 {
			continue // 跳过无效行
		}
		info := ExcelInfo{
			Name:          row[0],
			Author:        row[1],
			Publisher:     row[2],
			RegisterCount: 0,
			BookType:      row[4],
		}
		if row[3] != "" {
			fmt.Sscanf(row[3], "%d", &info.RegisterCount)
		}
		result = append(result, info)
	}

	return result, nil
}
