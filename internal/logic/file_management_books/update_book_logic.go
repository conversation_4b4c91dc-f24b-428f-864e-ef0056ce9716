package file_management_books

import (
	"context"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBookLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBookLogic {
	return &UpdateBookLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBookLogic) UpdateBook(req *types.BookInfo) (resp *types.UpdateBookResp, err error) {
	userLoginInfo := utils.GetCurrentLoginUser(l.ctx)
	bookType, number, err := GetBookTypeAndBookNumber(req.DictionaryNodeID, l.svcCtx.NebulaDB, l.ctx)
	if err != nil {
		l.Logger.Errorf("获取字典信息错误：&v", err)
		return nil, err
	}

	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, userLoginInfo.OrganizationId)
	if err != nil {
		return nil, err
	}
	// 设置编号
	if number != "" {
		number = organizationInfo.Code + "/" + number + "-"
	} else {
		number = organizationInfo.Code + "/"
	}
	// 调用更新服务
	_, err = docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).UpdateBook(l.ctx, &docvault.BookInfo{
		Id:               req.ID,
		Number:           number,
		Name:             req.Name,
		Author:           req.Author,
		Publisher:        req.Publisher,
		BookType:         bookType,
		RegisterCount:    int32(req.RegisterCount),
		UpdatedBy:        userLoginInfo.UserId,
		DictionaryNodeId: req.DictionaryNodeID,
		FileId:           req.FileID,
		OrganizationId:   userLoginInfo.OrganizationId,
		Remark:           req.Remark, // 0.5版本新增：备注
	})
	if err != nil {
		l.Logger.Errorf("更新书籍信息错误：%v", err)
		return nil, err
	}

	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryRelationByBusinessID(l.ctx, req.ID, consts.BusinessDictionaryBusinessTypeBookType, req.DictionaryNodeID)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBookResp{}, nil
}
