package file_management_books

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GiveBackBookLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGiveBackBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GiveBackBookLogic {
	return &GiveBackBookLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GiveBackBook 归还书籍
// 参数:
//   - req: 归还请求参数
//
// 返回值:
//   - resp: 归还响应
//   - err: 错误信息
//
// 功能: 根据书籍ID和归还类型归还相应的书籍
func (l *GiveBackBookLogic) GiveBackBook(req *types.GiveBackBookReq) (resp *types.GiveBackBookResp, err error) {
	_, err = docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).GiveBackBook(l.ctx, &docvault.GiveBackBookReq{
		BookId:       req.BookID,
		GiveBackType: req.GiveBackType,
	})
	if err != nil {
		return nil, err
	}
	return &types.GiveBackBookResp{}, nil
}
