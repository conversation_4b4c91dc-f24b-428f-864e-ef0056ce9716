package file_management_books

import (
	"context"
	"errors"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckDuplicateBorrowOrReceiveLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckDuplicateBorrowOrReceiveLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckDuplicateBorrowOrReceiveLogic {
	return &CheckDuplicateBorrowOrReceiveLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// CheckDuplicateBorrowOrReceive 执行借用和领用查重检查
// 参数:
//   - req: 查重请求参数
//
// 返回值:
//   - resp: 空响应
//   - err: 错误信息
//
// 功能: 根据查重类型检查用户是否已经借用或领用了指定书籍
func (l *CheckDuplicateBorrowOrReceiveLogic) CheckDuplicateBorrowOrReceive(req *types.CheckDuplicateBorrowOrReceiveReq) (resp *types.EmptyResp, err error) {
	userID := utils.GetContextUserID(l.ctx)

	// 查重，根据查重类型进行不同的处理
	switch req.PlagiarismCheckType {
	case "1":
		// 校验借用重复
		if err := l.checkBorrowDuplicate(req.BookID, userID); err != nil {
			return nil, err
		}
	case "2":
		// 校验领用重复
		if err := l.checkReceiveDuplicate(req.BookID, userID); err != nil {
			return nil, err
		}
	default:
		return nil, errors.New("无效的查重类型")
	}

	// 查询书籍隐式剩余数数量是否充足
	book, err := mapper.NewBooksClient(l.svcCtx.DocvaultDB).GetHiddenSurplusCountByID(l.ctx, req.BookID)
	if err != nil {
		l.Logger.Errorf("查询书籍隐式剩余数数量失败：%v", err)
		return nil, err
	}
	if book.HiddenSurplusCount <= 0 {
		return nil, errors.New("当前书籍库存不足，请联系管理员检查库或审批数据。")
	}

	return &types.EmptyResp{}, nil
}

// checkBorrowDuplicate 校验借用重复
// 功能: 检查用户是否已经借用或正在审批借用指定书籍
// 参数:
//   - bookID: 书籍ID
//   - userID: 用户ID
//
// 返回值:
//   - error: 错误信息
func (l *CheckDuplicateBorrowOrReceiveLogic) checkBorrowDuplicate(bookID, userID string) error {
	borrowUser, err := mapper.NewBookUsersBorrowClient(l.svcCtx.DocvaultDB).GetByBookIDAndUserID(l.ctx, bookID, userID)
	if err != nil {
		l.Logger.Errorf("查询借用信息失败：%v", err)
		return err
	}

	// 如果查询出的数据不为空，则表示已经借用了
	if borrowUser.ID != "" {
		return errors.New("无法借用，处于借用审批中或已借用未归还。")
	}
	return nil
}

// checkReceiveDuplicate 校验领用重复
// 功能: 检查用户是否已经领用或正在审批领用指定书籍
// 参数:
//   - bookID: 书籍ID
//   - userID: 用户ID
//
// 返回值:
//   - error: 错误信息
func (l *CheckDuplicateBorrowOrReceiveLogic) checkReceiveDuplicate(bookID, userID string) error {
	receiveUser, err := mapper.NewBookUsersReceiveClient(l.svcCtx.DocvaultDB).GetByBookIDAndUserID(l.ctx, bookID, userID)
	if err != nil {
		l.Logger.Errorf("查询领用信息失败：%v", err)
		return err
	}

	// 如果查询出的数据不为空，则表示已经领用了
	if receiveUser.ID != "" {
		return errors.New("无法领用，处于领用审批中或已领用未归还。")
	}
	return nil
}
