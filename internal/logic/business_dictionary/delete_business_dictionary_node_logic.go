package business_dictionary

import (
	"context"
	"errors"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteBusinessDictionaryNodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteBusinessDictionaryNodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBusinessDictionaryNodeLogic {
	return &DeleteBusinessDictionaryNodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBusinessDictionaryNodeLogic) DeleteBusinessDictionaryNode(req *types.DeleteBusinessDictionaryNodeReq) (resp *types.DeleteBusinessDictionaryNodeResp, err error) {
	node, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetById(l.ctx, req.NodeID)
	if err != nil {
		return nil, err
	}

	if node.ID == "" {
		return
	}

	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryIDsByParentID(l.ctx, req.NodeID)
	if err != nil {
		return nil, err
	}
	nodes = append(nodes, req.NodeID)

	// 查询关系
	relations, err := mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryRelationsByNodeID(l.ctx, nodes)
	if err != nil {
		return nil, err
	}

	if len(relations) > 0 {
		return nil, errors.New("节该字段存在引用数据，无法删除")
	}

	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)

	err = mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).DeleteBusinessDictionaryNode(l.ctx, tx, nodes)
	if err != nil {
		return nil, err
	}

	err = mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryUpdatedAt(l.ctx, tx, node.DictionaryID, time.Now(), utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	// 删除节点关系
	mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).DeleteBusinessDictionaryNodeRelation(l.ctx, tx, nodes)

	return &types.DeleteBusinessDictionaryNodeResp{}, nil
}
