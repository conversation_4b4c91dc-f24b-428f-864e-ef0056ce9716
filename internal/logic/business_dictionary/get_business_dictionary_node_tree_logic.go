package business_dictionary

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBusinessDictionaryNodeTreeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetBusinessDictionaryNodeTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBusinessDictionaryNodeTreeLogic {
	return &GetBusinessDictionaryNodeTreeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBusinessDictionaryNodeTreeLogic) GetBusinessDictionaryNodeTree(req *types.GetBusinessDictionaryNodeTreeReq) (resp []*types.GetBusinessDictionaryNodeTreeResp, err error) {
	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeByDictionaryId(l.ctx, req.ID)
	if err != nil {
		l.Logger.Errorf("查询字典节点失败：%v", err)
		return nil, err
	}
	tree := l.buildNodeTree(nodes, "")
	return tree, nil
}

// 组装树
func (l *GetBusinessDictionaryNodeTreeLogic) buildNodeTree(nodes []mapper.BusinessDictionaryNode, parentID string) []*types.GetBusinessDictionaryNodeTreeResp {
	nodeMap := make(map[string]*types.GetBusinessDictionaryNodeTreeResp)
	for _, n := range nodes {
		nodeMap[n.ID] = &types.GetBusinessDictionaryNodeTreeResp{
			ID:   n.ID,
			Code: n.Code,
			Name: n.Name,
		}
	}

	var roots []*types.GetBusinessDictionaryNodeTreeResp
	for _, n := range nodes {
		if n.ParentID == "" || n.ParentID == parentID {
			// 根节点
			roots = append(roots, nodeMap[n.ID])
		} else if parent, ok := nodeMap[n.ParentID]; ok {
			parent.Children = append(parent.Children, nodeMap[n.ID])
		}
	}

	return roots
}
