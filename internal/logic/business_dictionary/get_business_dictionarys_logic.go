package business_dictionary

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBusinessDictionarysLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetBusinessDictionarysLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBusinessDictionarysLogic {
	return &GetBusinessDictionarysLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBusinessDictionarysLogic) GetBusinessDictionarys(req *types.GetBusinessDictionarysReq) (resp *types.GetBusinessDictionarysResp, err error) {
	dictionaries, total, err := mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).GetBusinessDictionarys(l.ctx, mapper.GetBusinessDictionarysParams{
		Search:   req.Search,
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
		TenantID: utils.GetContextTenantID(l.ctx),
	})
	if err != nil {
		return nil, err
	}
	dictionariesInfo, err := l.build(dictionaries)
	if err != nil {
		return nil, err
	}

	resp = &types.GetBusinessDictionarysResp{
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    uint64(total),
		},
		Data: dictionariesInfo,
	}
	return
}

func (l *GetBusinessDictionarysLogic) build(dictionaries []mapper.BusinessDictionary) ([]types.BusinessDictionaryInfo, error) {
	userIds := utils.ExtractSliceField(dictionaries, func(dictionary mapper.BusinessDictionary) string {
		return dictionary.UpdatedBy
	})
	userNicknames, err := l.svcCtx.PhoenixClient.GetUserNicknames(l.ctx, userIds)
	if err != nil {
		return nil, err
	}
	dictionariesInfo := make([]types.BusinessDictionaryInfo, 0)
	for _, dictionary := range dictionaries {
		n := types.BusinessDictionaryInfo{
			ID:         dictionary.ID,
			ModuleName: dictionary.ModuleName,
			FieldName:  dictionary.FieldName,
			Status:     dictionary.Status,
			UpdatedAt:  dictionary.UpdatedAt.UnixMilli(),
			UpdatedBy:  dictionary.UpdatedBy,
		}
		if nickname, ok := userNicknames[dictionary.UpdatedBy]; ok {
			n.UpdatedBy = nickname
		}

		dictionariesInfo = append(dictionariesInfo, n)
	}
	return dictionariesInfo, nil
}
