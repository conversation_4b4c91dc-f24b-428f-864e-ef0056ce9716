package business_dictionary

import (
	"testing"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

func TestMoveBusinessDictionaryNodeLogic_sortNodes(t *testing.T) {
	Convey("测试业务字典节点排序逻辑", t, func() {
		logic := &MoveBusinessDictionaryNodeLogic{}

		<PERSON><PERSON>("向前移动节点", func() {
			Convey("将节点从位置4移动到位置2", func() {
				// 准备测试数据：4个节点，排序分别为1,2,3,4
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-d",
					Sort: 4, // 原始位置
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1},
					{ID: "node-b", Sort: 2},
					{ID: "node-c", Sort: 3},
					{ID: "node-d", Sort: 4}, // 目标节点
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-d",
					Sort: 2, // 新位置
				}

				// 执行排序
				result := logic.sortNodes(targetNode, nodes, req)

				// 验证结果
				So(len(result), ShouldEqual, 3) // 目标节点 + 2个受影响的节点

				// 构建结果映射便于验证
				resultMap := make(map[string]int)
				for _, node := range result {
					resultMap[node.ID] = node.Sort
				}

				// 验证排序结果
				So(resultMap["node-d"], ShouldEqual, 2) // 目标节点移动到位置2
				So(resultMap["node-b"], ShouldEqual, 3) // 原位置2的节点移动到位置3
				So(resultMap["node-c"], ShouldEqual, 4) // 原位置3的节点移动到位置4
				// node-a 不受影响，仍在位置1
			})

			Convey("将节点从位置5移动到位置1", func() {
				// 准备测试数据：5个节点
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-e",
					Sort: 5,
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1},
					{ID: "node-b", Sort: 2},
					{ID: "node-c", Sort: 3},
					{ID: "node-d", Sort: 4},
					{ID: "node-e", Sort: 5},
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-e",
					Sort: 1,
				}

				result := logic.sortNodes(targetNode, nodes, req)

				// 验证结果
				So(len(result), ShouldEqual, 5) // 所有节点都受影响

				resultMap := make(map[string]int)
				for _, node := range result {
					resultMap[node.ID] = node.Sort
				}

				So(resultMap["node-e"], ShouldEqual, 1) // 目标节点移动到位置1
				So(resultMap["node-a"], ShouldEqual, 2) // 原位置1的节点移动到位置2
				So(resultMap["node-b"], ShouldEqual, 3) // 原位置2的节点移动到位置3
				So(resultMap["node-c"], ShouldEqual, 4) // 原位置3的节点移动到位置4
				So(resultMap["node-d"], ShouldEqual, 5) // 原位置4的节点移动到位置5
			})
		})

		Convey("向后移动节点", func() {
			Convey("将节点从位置2移动到位置4", func() {
				// 准备测试数据
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-b",
					Sort: 2,
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1},
					{ID: "node-b", Sort: 2}, // 目标节点
					{ID: "node-c", Sort: 3},
					{ID: "node-d", Sort: 4},
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-b",
					Sort: 4,
				}

				result := logic.sortNodes(targetNode, nodes, req)

				// 验证结果
				So(len(result), ShouldEqual, 3) // 目标节点 + 2个受影响的节点

				resultMap := make(map[string]int)
				for _, node := range result {
					resultMap[node.ID] = node.Sort
				}

				So(resultMap["node-b"], ShouldEqual, 4) // 目标节点移动到位置4
				So(resultMap["node-c"], ShouldEqual, 2) // 原位置3的节点移动到位置2
				So(resultMap["node-d"], ShouldEqual, 3) // 原位置4的节点移动到位置3
				// node-a 不受影响，仍在位置1
			})

			Convey("将节点从位置1移动到位置5", func() {
				// 准备测试数据
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-a",
					Sort: 1,
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1}, // 目标节点
					{ID: "node-b", Sort: 2},
					{ID: "node-c", Sort: 3},
					{ID: "node-d", Sort: 4},
					{ID: "node-e", Sort: 5},
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-a",
					Sort: 5,
				}

				result := logic.sortNodes(targetNode, nodes, req)

				// 验证结果
				So(len(result), ShouldEqual, 5) // 所有节点都受影响

				resultMap := make(map[string]int)
				for _, node := range result {
					resultMap[node.ID] = node.Sort
				}

				So(resultMap["node-a"], ShouldEqual, 5) // 目标节点移动到位置5
				So(resultMap["node-b"], ShouldEqual, 1) // 原位置2的节点移动到位置1
				So(resultMap["node-c"], ShouldEqual, 2) // 原位置3的节点移动到位置2
				So(resultMap["node-d"], ShouldEqual, 3) // 原位置4的节点移动到位置3
				So(resultMap["node-e"], ShouldEqual, 4) // 原位置5的节点移动到位置4
			})
		})

		Convey("不移动节点（相同位置）", func() {
			Convey("节点保持在原位置", func() {
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-b",
					Sort: 2,
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1},
					{ID: "node-b", Sort: 2},
					{ID: "node-c", Sort: 3},
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-b",
					Sort: 2, // 相同位置
				}

				result := logic.sortNodes(targetNode, nodes, req)

				// 验证结果：只有目标节点被返回，其他节点不受影响
				So(len(result), ShouldEqual, 1)
				So(result[0].ID, ShouldEqual, "node-b")
				So(result[0].Sort, ShouldEqual, 2)
			})
		})

		Convey("边界情况测试", func() {
			Convey("只有一个节点", func() {
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-a",
					Sort: 1,
				}
				nodes := []mapper.BusinessDictionaryNode{
					{ID: "node-a", Sort: 1},
				}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-a",
					Sort: 1,
				}

				result := logic.sortNodes(targetNode, nodes, req)

				So(len(result), ShouldEqual, 1)
				So(result[0].ID, ShouldEqual, "node-a")
				So(result[0].Sort, ShouldEqual, 1)
			})

			Convey("空节点列表", func() {
				targetNode := mapper.BusinessDictionaryNode{
					ID:   "node-a",
					Sort: 1,
				}
				nodes := []mapper.BusinessDictionaryNode{}
				req := &types.MoveBusinessDictionaryNodeReq{
					ID:   "node-a",
					Sort: 2,
				}

				result := logic.sortNodes(targetNode, nodes, req)

				So(len(result), ShouldEqual, 1)
				So(result[0].ID, ShouldEqual, "node-a")
				So(result[0].Sort, ShouldEqual, 2)
			})
		})
	})
}
