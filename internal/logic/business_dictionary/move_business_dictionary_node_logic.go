package business_dictionary

import (
	"context"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type MoveBusinessDictionaryNodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMoveBusinessDictionaryNodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoveBusinessDictionaryNodeLogic {
	return &MoveBusinessDictionaryNodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MoveBusinessDictionaryNodeLogic) MoveBusinessDictionaryNode(req *types.MoveBusinessDictionaryNodeReq) (resp *types.MoveBusinessDictionaryNodeResp, err error) {
	node, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetById(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}
	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodesByDictionaryIDAndParentID(l.ctx, node.DictionaryID, node.ParentID)
	if err != nil {
		return nil, err
	}

	// 对指定循序后面的节点进行排序，忽略当前节点
	nodesToUpdate := l.sortNodes(node, nodes, req)

	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)

	if err = l.updateNodes(nodesToUpdate, tx); err != nil {
		return nil, err
	}

	err = mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryUpdatedAt(l.ctx, tx, node.DictionaryID, time.Now(), utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	return &types.MoveBusinessDictionaryNodeResp{}, err
}

// sortNodes 对节点进行排序调整，实现正确的节点位置移动
// 移动逻辑：将目标节点插入到指定位置，其他节点相应调整
func (*MoveBusinessDictionaryNodeLogic) sortNodes(targetNode mapper.BusinessDictionaryNode, nodes []mapper.BusinessDictionaryNode, req *types.MoveBusinessDictionaryNodeReq) []mapper.BusinessDictionaryNode {
	nodesToUpdate := []mapper.BusinessDictionaryNode{}

	// 获取目标节点的原始排序位置和新位置
	originalSort := targetNode.Sort
	newSort := req.Sort

	// 如果位置没有变化，只更新目标节点
	if originalSort == newSort {
		targetNode.Sort = newSort
		nodesToUpdate = append(nodesToUpdate, targetNode)
		return nodesToUpdate
	}

	// 设置目标节点的新排序位置
	targetNode.Sort = newSort
	nodesToUpdate = append(nodesToUpdate, targetNode)

	// 调整其他节点的排序位置
	for _, currentNode := range nodes {
		// 跳过目标节点本身，避免重复处理
		if currentNode.ID == req.ID {
			continue
		}

		if originalSort > newSort {
			// 向前移动：目标节点从后面位置移动到前面位置
			// 原来在新位置及其后面（直到原位置之前）的节点需要向后移动一位
			if currentNode.Sort >= newSort && currentNode.Sort < originalSort {
				currentNode.Sort++
				nodesToUpdate = append(nodesToUpdate, currentNode)
			}
		} else {
			// 向后移动：目标节点从前面位置移动到后面位置
			// 原来在原位置之后（直到新位置）的节点需要向前移动一位
			if currentNode.Sort > originalSort && currentNode.Sort <= newSort {
				currentNode.Sort--
				nodesToUpdate = append(nodesToUpdate, currentNode)
			}
		}
	}

	return nodesToUpdate
}

func (l *MoveBusinessDictionaryNodeLogic) updateNodes(nodesToUpdate []mapper.BusinessDictionaryNode, tx *mapper.NebulaTX) (err error) {
	for _, node := range nodesToUpdate {
		err = mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryNodeWithTransaction(l.ctx, node, tx)
		if err != nil {
			return err
		}
	}
	return nil
}
