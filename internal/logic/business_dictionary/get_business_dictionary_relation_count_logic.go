package business_dictionary

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBusinessDictionaryRelationCountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetBusinessDictionaryRelationCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBusinessDictionaryRelationCountLogic {
	return &GetBusinessDictionaryRelationCountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBusinessDictionaryRelationCountLogic) GetBusinessDictionaryRelationCount(req *types.GetBusinessDictionaryRelationCountReq) (resp *types.GetBusinessDictionaryRelationCountResp, err error) {
	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryIDsByParentID(l.ctx, req.NodeID)
	if err != nil {
		return nil, err
	}

	nodes = append(nodes, req.NodeID)
	count, err := mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryRelationsByNodeID(l.ctx, nodes)
	if err != nil {
		return nil, err
	}

	resp = &types.GetBusinessDictionaryRelationCountResp{
		Count: int64(len(count)),
	}
	return
}
