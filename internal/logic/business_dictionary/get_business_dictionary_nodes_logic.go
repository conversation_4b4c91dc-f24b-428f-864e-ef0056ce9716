package business_dictionary

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBusinessDictionaryNodesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetBusinessDictionaryNodesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBusinessDictionaryNodesLogic {
	return &GetBusinessDictionaryNodesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBusinessDictionaryNodesLogic) GetBusinessDictionaryNodes(req *types.GetBusinessDictionaryNodesReq) (resp *types.GetBusinessDictionaryNodesResp, err error) {
	nodes, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodesByDictionaryIDAndParentID(l.ctx, req.DictionaryID, req.ParentID)
	if err != nil {
		return nil, err
	}
	nodesInfo, err := l.build(nodes)
	if err != nil {
		return nil, err
	}

	resp = &types.GetBusinessDictionaryNodesResp{
		Data: nodesInfo,
	}
	return
}

func (l *GetBusinessDictionaryNodesLogic) build(nodes []mapper.BusinessDictionaryNode) ([]types.BusinessDictionaryNodeInfo, error) {
	userIds := utils.ExtractSliceField(nodes, func(node mapper.BusinessDictionaryNode) string {
		return node.CreatedBy
	})

	userNicknames, err := l.svcCtx.PhoenixClient.GetUserNicknames(l.ctx, userIds)
	if err != nil {
		return nil, err
	}

	nodesInfo := make([]types.BusinessDictionaryNodeInfo, 0)
	for _, node := range nodes {
		n := types.BusinessDictionaryNodeInfo{
			ID:        node.ID,
			ParentID:  node.ParentID,
			Code:      node.Code,
			Name:      node.Name,
			Remark:    node.Remark,
			Status:    node.Status,
			Sort:      node.Sort,
			UpdatedAt: node.UpdatedAt.UnixMilli(),
			UpdatedBy: node.UpdatedBy,
		}
		if nickname, ok := userNicknames[node.CreatedBy]; ok {
			n.UpdatedBy = nickname
		}

		nodesInfo = append(nodesInfo, n)
	}

	return nodesInfo, nil
}
