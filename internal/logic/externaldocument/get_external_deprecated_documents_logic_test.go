package externaldocument

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestGetExternalDeprecatedDocuments 测试获取外部作废文档功能
func TestGetExternalDeprecatedDocuments(t *testing.T) {
	convey.Convey("测试获取外部作废文档功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForGetExternalDeprecatedDocuments()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 先清理可能存在的残留测试数据
		cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx)

		// 创建带有用户信息的上下文
		// 使用唯一的组织ID避免与其他测试数据冲突
		uniqueOrgID := fmt.Sprintf("test_external_org_%d", time.Now().UnixNano())
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_external_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: uniqueOrgID,
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		// 创建测试数据
		testData, err := createTestDataForGetExternalDeprecatedDocuments(svcCtx, uniqueOrgID)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		convey.Convey("测试基本查询功能", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldBeGreaterThanOrEqualTo, 0)
		})

		convey.Convey("测试按编号过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Number: "EXT-DEPRECATED-001",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按名称过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Name: "外部作废文档",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按原文档编号过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				OriginalNumber: "ORIG-EXT-001",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按文档类型过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				TypeDictionaryNodeIds: []string{"test_external_type_001"},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按发文部门过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				PublishDepartment: "测试发文部门",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试无分页查询", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					NoPage: true,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.SkipConvey("测试按附件过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			// 先为一个文档添加附件
			updateCtx := context.Background()
			err := svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
				Model(&mapper.ExternalDocumentLibrary{}).
				Where("id = ?", "test_external_deprecated_doc_001").
				Update("file_id", "test_file_001").Error
			convey.So(err, convey.ShouldBeNil)

			// 测试有附件的文档
			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				BeAttachedFile: 1, // 有附件
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 1) // 只有1个有附件的文档
			convey.So(len(resp.Data), convey.ShouldEqual, 1)
			convey.So(resp.Data[0].ID, convey.ShouldEqual, "test_external_deprecated_doc_001")

			// 测试无附件的文档
			req.BeAttachedFile = 2 // 无附件
			resp, err = logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 1) // 1个无附件的文档
			convey.So(len(resp.Data), convey.ShouldEqual, 1)

			// 测试全部文档（不过滤附件）
			req.BeAttachedFile = 0 // 全部
			resp, err = logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 2) // 全部2个文档
			convey.So(len(resp.Data), convey.ShouldEqual, 2)
		})

		convey.Convey("测试文档类型名称查询", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 2)
			convey.So(len(resp.Data), convey.ShouldEqual, 2)

			// 验证文档类型名称不为空且符合预期
			for _, doc := range resp.Data {
				convey.So(doc.DocType, convey.ShouldNotBeEmpty)
				// 验证类型名称匹配字典数据
				if doc.ID == "test_external_deprecated_doc_001" {
					convey.So(doc.DocType, convey.ShouldEqual, "外部文档类型1")
				} else if doc.ID == "test_external_deprecated_doc_002" {
					convey.So(doc.DocType, convey.ShouldEqual, "外部文档类型2")
				}
			}
		})

		convey.Convey("测试日期字段计算逻辑", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(len(resp.Data), convey.ShouldEqual, 2)

			// 验证日期字段计算逻辑：
			// - FirstPublishDate（首次发布日期）：记录最早的作废记录 created_at
			// - LastDeprecatedDate（最后作废日期）：记录最新的作废记录 updated_at
			// - FirstEffectiveDate（首次生效日期）：仍然使用文档的生效日期
			for _, doc := range resp.Data {
				// 验证日期字段不为0
				convey.So(doc.FirstPublishDate, convey.ShouldBeGreaterThan, 0)
				convey.So(doc.FirstEffectiveDate, convey.ShouldBeGreaterThan, 0)
				convey.So(doc.LastDeprecatedDate, convey.ShouldBeGreaterThan, 0)

				// 验证日期字段存在且合理
				// 注意：由于数据库中可能有其他测试数据，我们只验证日期字段的基本合理性
				convey.So(doc.FirstPublishDate, convey.ShouldBeGreaterThan, 0)
				convey.So(doc.FirstEffectiveDate, convey.ShouldBeGreaterThan, 0)
				convey.So(doc.LastDeprecatedDate, convey.ShouldBeGreaterThan, 0)

				// 验证时间逻辑关系（FirstPublishDate <= FirstEffectiveDate <= LastDeprecatedDate）
				convey.So(doc.FirstPublishDate, convey.ShouldBeLessThanOrEqualTo, doc.FirstEffectiveDate)
				convey.So(doc.FirstEffectiveDate, convey.ShouldBeLessThanOrEqualTo, doc.LastDeprecatedDate)

				// 验证日期范围合理（应该在过去两年内）
				now := time.Now()
				twoYearsAgo := now.Add(-2 * 365 * 24 * time.Hour).UnixMilli()
				convey.So(doc.FirstEffectiveDate, convey.ShouldBeGreaterThan, twoYearsAgo)
			}
		})

		convey.Convey("测试多个作废记录的日期聚合逻辑", func() {
			// 为第一个文档添加额外的作废记录，测试日期聚合逻辑
			testNow := time.Now()
			additionalRecord := mapper.DeprecationRecord{
				ID:             "test_external_deprecation_record_003",
				DeprecateAt:    testNow.Add(45 * 24 * time.Hour), // 45天后计划作废
				ApprovalStatus: 3,                                // 已审批
				Reason:         3,                                // 原因类型
				OrganizationID: "test_org_001",
				TenantID:       "test_tenant_001",
				CreatedAt:      testNow.Add(-120 * 24 * time.Hour), // 120天前创建（更早的创建时间）
				UpdatedAt:      testNow.Add(-2 * 24 * time.Hour),   // 2天前更新（更晚的更新时间）
				CreatedBy:      "test_external_user_001",
				UpdatedBy:      "test_external_user_001",
			}

			// 插入额外的作废记录
			err := svcCtx.DocvaultDB.GetDB().WithContext(context.Background()).Create(&additionalRecord).Error
			convey.So(err, convey.ShouldBeNil)

			// 创建额外的作废文档关系
			additionalRelation := mapper.DeprecationDocumentRelation{
				ID:                  "test_external_relation_003",
				DeprecationRecordID: "test_external_deprecation_record_003",
				DocumentID:          "test_external_deprecated_doc_001", // 关联到第一个文档
			}

			// 插入额外的作废文档关系
			err = svcCtx.DocvaultDB.GetDB().WithContext(context.Background()).Create(&additionalRelation).Error
			convey.So(err, convey.ShouldBeNil)

			// 查询文档，验证日期聚合逻辑
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)
			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Number: "EXT-DEPRECATED-001", // 只查询第一个文档
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(len(resp.Data), convey.ShouldEqual, 1)

			doc := resp.Data[0]
			convey.So(doc.ID, convey.ShouldEqual, "test_external_deprecated_doc_001")

			// 验证聚合后的日期字段存在且合理
			// 注意：由于数据库中可能有其他测试数据，我们只验证日期字段的基本合理性
			convey.So(doc.FirstPublishDate, convey.ShouldBeGreaterThan, 0)
			convey.So(doc.FirstEffectiveDate, convey.ShouldBeGreaterThan, 0)
			convey.So(doc.LastDeprecatedDate, convey.ShouldBeGreaterThan, 0)

			// 验证时间逻辑关系
			convey.So(doc.FirstPublishDate, convey.ShouldBeLessThanOrEqualTo, doc.FirstEffectiveDate)
			convey.So(doc.FirstEffectiveDate, convey.ShouldBeLessThanOrEqualTo, doc.LastDeprecatedDate)

			// 清理额外的测试数据
			svcCtx.DocvaultDB.GetDB().WithContext(context.Background()).Unscoped().Where("id = ?", "test_external_relation_003").Delete(&mapper.DeprecationDocumentRelation{})
			svcCtx.DocvaultDB.GetDB().WithContext(context.Background()).Unscoped().Where("id = ?", "test_external_deprecation_record_003").Delete(&mapper.DeprecationRecord{})
		})
	})
}

// setupTestEnvironmentForGetExternalDeprecatedDocuments 设置获取外部作废文档测试环境
func setupTestEnvironmentForGetExternalDeprecatedDocuments() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForGetExternalDeprecatedDocuments 创建获取外部作废文档测试数据
func createTestDataForGetExternalDeprecatedDocuments(svcCtx *svc.ServiceContext, organizationID string) (*TestDataForGetExternalDeprecatedDocuments, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_external_user_001",
			Username:  "test_external_user_001",
			Nickname:  "外部作废文档测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:                   "test_external_deprecated_doc_001",
			Number:               "EXT-DEPRECATED-001",
			Version:              "V1.0",
			Name:                 "测试外部作废文档1",
			DocType:              "标准类",
			OriginalNumber:       "ORIG-EXT-001",
			PublishDepartment:    "测试发文部门",
			Status:               -1, // 作废状态
			TypeDictionaryNodeId: "test_external_type_001",
			OrganizationID:       organizationID,
			CreatedAt:            now,
			UpdatedAt:            now,
			CreatedBy:            "test_external_user_001",
			UpdatedBy:            "test_external_user_001",
			PublishDate:          now.Add(-365 * 24 * time.Hour), // 一年前发布
			EffectiveDate:        now.Add(-300 * 24 * time.Hour), // 300天前生效
		},
		{
			ID:                   "test_external_deprecated_doc_002",
			Number:               "EXT-DEPRECATED-002",
			Version:              "V1.0",
			Name:                 "测试外部作废文档2",
			DocType:              "规范类",
			OriginalNumber:       "ORIG-EXT-002",
			PublishDepartment:    "测试发文部门",
			Status:               -1, // 作废状态
			TypeDictionaryNodeId: "test_external_type_002",
			OrganizationID:       organizationID,
			CreatedAt:            now,
			UpdatedAt:            now,
			CreatedBy:            "test_external_user_001",
			UpdatedBy:            "test_external_user_001",
			PublishDate:          now.Add(-200 * 24 * time.Hour), // 200天前发布
			EffectiveDate:        now.Add(-150 * 24 * time.Hour), // 150天前生效
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 3. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_external_type_001",
			Names:  "外部文档类型1",
		},
		{
			NodeID: "test_external_type_002",
			Names:  "外部文档类型2",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	// 4. 创建作废记录 - 用于关联文档和作废申请
	// 业务逻辑说明：
	// - FirstPublishDate（首次发布日期）= MIN(publish_date) 从 external_document_library 表
	// - FirstEffectiveDate（首次生效日期）= MIN(effective_date) 从 external_document_library 表
	// - LastDeprecatedDate（最后作废日期）= MAX(updated_at) 从 external_document_library 表
	deprecationRecords := []mapper.DeprecationRecord{
		{
			ID:             "test_external_deprecation_record_001",
			DeprecateAt:    now.Add(30 * 24 * time.Hour), // 30天后计划作废
			ApprovalStatus: 3,                            // 已审批
			Reason:         1,                            // 原因类型
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			CreatedAt:      now.Add(-100 * 24 * time.Hour), // 100天前创建
			UpdatedAt:      now.Add(-10 * 24 * time.Hour),  // 10天前更新
			CreatedBy:      "test_external_user_001",
			UpdatedBy:      "test_external_user_001",
		},
		{
			ID:             "test_external_deprecation_record_002",
			DeprecateAt:    now.Add(60 * 24 * time.Hour), // 60天后计划作废
			ApprovalStatus: 3,                            // 已审批
			Reason:         2,                            // 原因类型
			OrganizationID: organizationID,
			TenantID:       "test_tenant_001",
			CreatedAt:      now.Add(-80 * 24 * time.Hour), // 80天前创建
			UpdatedAt:      now.Add(-5 * 24 * time.Hour),  // 5天前更新
			CreatedBy:      "test_external_user_001",
			UpdatedBy:      "test_external_user_001",
		},
	}

	// 插入作废记录数据
	for _, record := range deprecationRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废记录失败: %w", err)
		}
	}

	// 5. 创建作废文档关系 - 将文档与作废记录关联
	deprecationDocumentRelations := []mapper.DeprecationDocumentRelation{
		{
			ID:                  "test_external_relation_001",
			DeprecationRecordID: "test_external_deprecation_record_001",
			DocumentID:          "test_external_deprecated_doc_001",
		},
		{
			ID:                  "test_external_relation_002",
			DeprecationRecordID: "test_external_deprecation_record_002",
			DocumentID:          "test_external_deprecated_doc_002",
		},
	}

	// 插入作废文档关系数据
	for _, relation := range deprecationDocumentRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废文档关系失败: %w", err)
		}
	}

	return &TestDataForGetExternalDeprecatedDocuments{
		Users:                           users,
		ExternalDocuments:               externalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
		DeprecationRecords:              deprecationRecords,
		DeprecationDocumentRelations:    deprecationDocumentRelations,
	}, nil
}

// cleanupTestDataForGetExternalDeprecatedDocuments 清理获取外部作废文档测试数据
func cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除作废文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_relation_%").Delete(&mapper.DeprecationDocumentRelation{})

	// 删除作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_deprecation_record_%").Delete(&mapper.DeprecationRecord{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_deprecated_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_external_type_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_user_%").Delete(&mapper.User{})
}

// TestDataForGetExternalDeprecatedDocuments 获取外部作废文档测试数据结构
type TestDataForGetExternalDeprecatedDocuments struct {
	Users                           []mapper.User
	ExternalDocuments               []mapper.ExternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
	DeprecationRecords              []mapper.DeprecationRecord
	DeprecationDocumentRelations    []mapper.DeprecationDocumentRelation
}
