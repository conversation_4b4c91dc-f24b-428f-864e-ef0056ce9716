package externaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDocumentChangeRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewGetExternalDocumentChangeRecordsLogic 创建外部文档变更记录查询逻辑处理器
// 功能：初始化外部文档变更记录查询的业务逻辑处理器
// 参数：ctx - 上下文，svcCtx - 服务上下文
// 返回值：外部文档变更记录查询逻辑处理器实例
func NewGetExternalDocumentChangeRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDocumentChangeRecordsLogic {
	return &GetExternalDocumentChangeRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetExternalDocumentChangeRecords 获取外部文档变更记录
// 功能：根据文档ID查询该文档的所有历史版本变更记录，按更新时间倒序排列
// 参数：req - 查询请求参数，包含文档ID和分页信息
// 返回值：变更记录列表响应，错误信息
// 注意事项：
//   - 查询条件为 main_id = DocumentId 的所有历史版本记录
//   - 按 updated_at 倒序排列
//   - 支持分页查询
func (l *GetExternalDocumentChangeRecordsLogic) GetExternalDocumentChangeRecords(req *types.GetExternalDocumentChangeRecordsReq) (resp *types.GetExternalDocumentChangeRecordsResp, err error) {
	// 查询文件信息
	documentID := req.DocumentId
	doc, err := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB).GetByID(l.ctx, documentID)
	if err != nil {
		return nil, err
	}

	// 公司文件的变更历史是集团文件的变更历史
	if doc.GroupDocID != "" {
		documentID = doc.GroupDocID
	}

	// 查询变更记录
	records, total, err := l.queryChangeRecords(documentID, int(req.Page), int(req.PageSize), req.NoPage)
	if err != nil {
		return nil, err
	}

	// 批量查询字典名称
	typeNameMap, domainNameMap := l.batchQueryDictionaryNames(records)

	// 转换数据格式
	changeRecords := l.convertToChangeRecords(records, typeNameMap, domainNameMap)

	return &types.GetExternalDocumentChangeRecordsResp{
		Data: changeRecords,
		PageInfo: types.PageInfo{
			Total: uint64(total),
		},
	}, nil
}

// queryChangeRecords 查询变更记录
// 功能：调用数据库客户端查询变更记录
// 参数：documentID - 文档ID，page - 页码，pageSize - 每页大小，noPage - 是否不分页
// 返回值：变更记录列表，总数，错误信息
func (l *GetExternalDocumentChangeRecordsLogic) queryChangeRecords(documentID string, page, pageSize int, noPage bool) ([]mapper.ExternalDocumentLibrary, int64, error) {
	externalDocClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
	records, total, err := externalDocClient.GetChangeRecords(l.ctx, documentID, page, pageSize, noPage)
	if err != nil {
		l.Logger.Errorf("查询外部文档变更记录失败：%v", err)
		return nil, 0, err
	}
	return records, total, nil
}

// batchQueryDictionaryNames 批量查询字典名称
// 功能：批量查询类别名称和领域名称，避免N+1查询问题
// 参数：records - 变更记录列表
// 返回值：类别名称映射，领域名称映射
func (l *GetExternalDocumentChangeRecordsLogic) batchQueryDictionaryNames(records []mapper.ExternalDocumentLibrary) (map[string]string, map[string]string) {
	// 收集需要批量查询的字典节点ID
	typeNodeIDs := utils.ExtractSliceFieldAndDuplicate(records, func(record mapper.ExternalDocumentLibrary) string {
		return record.TypeDictionaryNodeId
	})
	domainNodeIDs := utils.ExtractSliceFieldAndDuplicate(records, func(record mapper.ExternalDocumentLibrary) string {
		return record.DomainDictionaryNodeId
	})

	// 批量查询类别名称
	typeNameMap := make(map[string]string)
	if len(typeNodeIDs) > 0 {
		businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB)
		if relations, err := businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, typeNodeIDs); err == nil {
			for _, relation := range relations {
				typeNameMap[relation.NodeID] = relation.Names
			}
		}
	}

	// 批量查询领域名称
	domainNameMap := make(map[string]string)
	if len(domainNodeIDs) > 0 {
		businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB)
		if relations, err := businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, domainNodeIDs); err == nil {
			for _, relation := range relations {
				domainNameMap[relation.NodeID] = relation.Names
			}
		}
	}

	return typeNameMap, domainNameMap
}

// convertToChangeRecords 转换数据格式
// 功能：将数据库记录转换为API响应格式
// 参数：records - 数据库记录列表，typeNameMap - 类别名称映射，domainNameMap - 领域名称映射
// 返回值：变更记录列表
func (l *GetExternalDocumentChangeRecordsLogic) convertToChangeRecords(records []mapper.ExternalDocumentLibrary, typeNameMap, domainNameMap map[string]string) []types.ExternalDocumentChangeRecord {
	changeRecords := make([]types.ExternalDocumentChangeRecord, 0, len(records))

	for _, record := range records {
		// 获取审批信息
		approvalInfo := record.GetApprovalInfo()

		// 转换审核人信息
		auditors := l.convertApprovalItems(approvalInfo.Auditors)

		// 转换批准人信息
		approvers := l.convertApprovalItems(approvalInfo.Approvers)

		// 获取类别名称和领域名称
		docType := l.getNameWithFallback(typeNameMap, record.TypeDictionaryNodeId, record.DocType)
		domain := l.getNameWithFallback(domainNameMap, record.DomainDictionaryNodeId, record.Domain)

		changeRecords = append(changeRecords, types.ExternalDocumentChangeRecord{
			DocumentNo:            record.Number,
			DocumentVersion:       record.Version,
			OriginalNo:            record.OriginalNumber,
			OriginalVersion:       record.OriginalVersion,
			DocumentName:          record.Name,
			DocumentCategory:      docType,
			Domain:                domain,
			OriginalDocNumber:     record.OriginalDocNumber,
			PublishDocNumber:      record.PublishDocNumber,
			PublishDepartment:     record.PublishDepartment,
			Auditors:              auditors,
			Approvers:             approvers,
			PublishDate:           record.PublishDate.UnixMilli(),
			EffectiveDate:         record.EffectiveDate.UnixMilli(),
			Authentications:       record.GetAuthenticationsSlice(),
			OperationType:         int(record.OperationType),
			UpdatedAt:             record.UpdatedAt.UnixMilli(),
			EnglishName:           record.EnglishName,
			Remark:                record.Remark,
			ReplacementDocName:    record.ReplacementDocName,    // 0.5版本新增：替代文档名称（等待数据库模型更新）
			ReplacementDocVersion: record.ReplacementDocVersion, // 0.5版本新增：替代文档版本（等待数据库模型更新）
		})
	}

	return changeRecords
}

// convertApprovalItems 转换审批项
// 功能：将数据库中的审批项转换为API格式
// 参数：items - 审批项列表
// 返回值：转换后的审批项列表
func (l *GetExternalDocumentChangeRecordsLogic) convertApprovalItems(items []mapper.ApprovalItem) []types.Approval {
	approvals := make([]types.Approval, 0, len(items))
	for _, item := range items {
		approvals = append(approvals, types.Approval{
			UserID:       item.UserID,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserID),
			PassedDate:   item.PassedDate,
		})
	}
	return approvals
}

// getNameWithFallback 获取名称并提供降级处理
// 功能：从映射中获取名称，如果不存在则使用原始值
// 参数：nameMap - 名称映射，nodeID - 节点ID，fallback - 降级值
// 返回值：名称
func (l *GetExternalDocumentChangeRecordsLogic) getNameWithFallback(nameMap map[string]string, nodeID, fallback string) string {
	if name := nameMap[nodeID]; name != "" {
		return name
	}
	return fallback
}
