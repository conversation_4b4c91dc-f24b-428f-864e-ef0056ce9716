package externaldocument

import (
	"context"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"
	"time"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateExternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateExternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateExternalDocumentLogic {
	return &CreateExternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateExternalDocumentLogic) CreateExternalDocument(req *types.CreateExternalDocumentReq) (resp *types.CreateExternalDocumentResp, err error) {
	// 查询组织信息
	organizationInfo, err := GetOrgInfo(l.ctx, l.svcCtx, req.OrgType)
	if err != nil {
		return nil, err
	}

	//封装NumberPrefix（编号前缀）、type（类型）、domain（所属领域）、authentication（认证方式）
	credential, err := GetCredential(req.TypeDictionaryNodeId, req.DomainDictionaryNodeId, req.AuthenticationDictionaryNodeIds, l.svcCtx.NebulaDB, l.ctx, organizationInfo.Code)
	if err != nil {
		l.Logger.Errorf("获取编号、类型、领域和认证方式失败：%v", err)
		return nil, err
	}

	var auth []string
	var authDictNodeIds []string
	for _, v := range credential.Authentications {
		auth = append(auth, v.Names)
		authDictNodeIds = append(authDictNodeIds, v.DictNodeID)
	}

	data := []*docvault.ExternalDocumentCreateInfo{
		{
			TypeDictionaryNodeId:            req.TypeDictionaryNodeId,
			DomainDictionaryNodeId:          req.DomainDictionaryNodeId,
			AuthenticationDictionaryNodeIds: authDictNodeIds,
			NumberPrefix:                    credential.NumberPrefix,
			DocType:                         credential.DocType,
			Domain:                          credential.Domain,
			Authentications:                 auth,
			Name:                            req.Name,
			EnglishName:                     req.EnglishName, // 0.4版本新增：文件英文名称
			OriginalDocNumber:               req.OriginalDocNumber,
			PublishDocNumber:                req.PublishDocNumber,
			PublishDepartment:               req.PublishDepartment,
			FileId:                          req.FileID,
			PublishDate:                     req.PublishDate,
			EffectiveDate:                   req.EffectiveDate,
			OriginalNumber:                  req.OriginalNumber,
			OriginalVersion:                 req.OriginalVersion,
			Remark:                          req.Remark, // 0.5版本新增：备注
		},
	}

	rpcData, err := docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Create(l.ctx, &docvault.ExternalDocumentCreateReq{
		Data:    data,
		OrgType: int32(req.OrgType),
		OrgId:   organizationInfo.Id,
	})
	if err != nil {
		l.Logger.Errorf("新建外部文档信息失败：%v", err)
		return nil, err
	}

	//添加字典节点联系
	err = CreateBusinessDictionaryRelation(l.ctx, l.svcCtx, rpcData)
	if err != nil {
		l.Logger.Errorf("添加字典节点关系失败：%v", err)
		return nil, err
	}

	return &types.CreateExternalDocumentResp{}, nil
}

func GetOrgInfo(ctx context.Context, svcCtx *svc.ServiceContext, orgType int) (organizationInfo entity.OrganizationInfo, err error) {
	orgID := utils.GetContextOrganizationID(ctx)
	organizationInfo, err = svcCtx.PhoenixClient.GetOrganizationInfo(ctx, orgID)
	if err != nil {
		return entity.OrganizationInfo{}, err
	}
	if orgType == 1 && organizationInfo.ParentId != "" {
		organizationInfo, err = svcCtx.PhoenixClient.GetOrganizationInfo(ctx, organizationInfo.ParentId)
		if err != nil {
			return entity.OrganizationInfo{}, err
		}
	}

	return organizationInfo, nil
}

// 创建业务字典关系
func CreateBusinessDictionaryRelation(ctx context.Context, svcCtx *svc.ServiceContext, rpcDatas *docvault.ExternalDocumentCreateResp) error {
	var businessDictionaryRelation []mapper.BusinessDictionaryRelation
	for _, rpcData := range rpcDatas.Data {
		businessDictionaryRelation = append(businessDictionaryRelation, mapper.BusinessDictionaryRelation{
			ID:               svcCtx.IdGenerator.GenerateIDString(),
			DictionaryNodeID: rpcData.TypeDictionaryNodeId,
			BusinessID:       rpcData.Id,
			BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentCategory,
			CreatedAt:        time.Now(),
			CreatedBy:        utils.GetContextUserID(ctx),
		})
		businessDictionaryRelation = append(businessDictionaryRelation, mapper.BusinessDictionaryRelation{
			ID:               svcCtx.IdGenerator.GenerateIDString(),
			DictionaryNodeID: rpcData.DomainDictionaryNodeId,
			BusinessID:       rpcData.Id,
			BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentDomain,
			CreatedAt:        time.Now(),
			CreatedBy:        utils.GetContextUserID(ctx),
		})
		for _, v := range rpcData.AuthenticationDictionaryNodeIds {
			businessDictionaryRelation = append(businessDictionaryRelation, mapper.BusinessDictionaryRelation{
				ID:               svcCtx.IdGenerator.GenerateIDString(),
				DictionaryNodeID: v,
				BusinessID:       rpcData.Id,
				BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentAuthentication,
				CreatedAt:        time.Now(),
				CreatedBy:        utils.GetContextUserID(ctx),
			})
		}
	}

	return mapper.NewBusinessDictionaryRelationClient(svcCtx.NebulaDB).BatchCreateBusinessDictionaryRelation(ctx, businessDictionaryRelation)
}

func GetCredential(typeDictNodeID, domainDictNodeID string, AuthDictNodeIds []string, db *mapper.NebulaDB, ctx context.Context, orgCode string) (Credential, error) {

	businessDictionaryNodeRelationClient := mapper.NewBusinessDictionaryNodeRelationClient(db)
	docType, err := businessDictionaryNodeRelationClient.GetBusinessDictionaryNodeRelationByNodeID(ctx, typeDictNodeID)
	if err != nil {
		return Credential{}, err
	}
	domain, err := businessDictionaryNodeRelationClient.GetBusinessDictionaryNodeRelationByNodeID(ctx, domainDictNodeID)
	if err != nil {
		return Credential{}, err
	}
	authentication, err := businessDictionaryNodeRelationClient.GetBusinessDictionaryNodeRelationByNodeIDs(ctx, AuthDictNodeIds)
	if err != nil {
		return Credential{}, err
	}

	var authentications []Authentications
	for _, v := range authentication {
		authentications = append(authentications, Authentications{
			DictNodeID: v.NodeID,
			Names:      v.Names,
		})
	}

	if docType.Codes != "" {
		docType.Codes += "-"
	}
	credential := Credential{
		NumberPrefix:    orgCode + "/" + docType.Codes,
		DocType:         docType.Names,
		Domain:          domain.Names,
		Authentications: authentications,
	}
	return credential, nil
}

type Authentications struct {
	DictNodeID string
	Names      string
}

type Credential struct {
	NumberPrefix    string
	DocType         string
	Domain          string
	Authentications []Authentications
}
