package externaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDocumentLogic {
	return &GetExternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExternalDocumentLogic) GetExternalDocument(req *types.GetExternalDocumentReq) (resp *types.ExternalDocumentInfo, err error) {
	rs, err := docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Get(
		l.ctx,
		&docvault.ExternalDocumentGetReq{
			Id: req.DistributeID,
		},
	)
	if err != nil {
		return
	}

	resp = &types.ExternalDocumentInfo{
		ID:                              rs.Id,
		Number:                          rs.Number,
		Version:                         rs.Version,
		OriginalNumber:                  rs.OriginalNumber,
		OriginalVersion:                 rs.OriginalVersion,
		Name:                            rs.Name,
		EnglishName:                     rs.EnglishName, // 0.4版本新增：文件英文名称
		DocType:                         rs.DocType,
		Domain:                          rs.Domain,
		OriginalDocNumber:               rs.OriginalDocNumber,
		PublishDocNumber:                rs.PublishDocNumber,
		PublishDepartment:               rs.PublishDepartment,
		ReplacementDocName:              "",        // 0.5版本调整：替代文档名称（ExternalDocumentGetResp暂无此字段）
		ReplacementDocVersion:           "",        // 0.5版本调整：替代文档版本（ExternalDocumentGetResp暂无此字段）
		Remark:                          rs.Remark, // 0.5版本新增：备注
		PublishDate:                     rs.PublishDate,
		EffectiveDate:                   rs.EffectiveDate,
		Authentication:                  rs.Authentication,
		Status:                          int(rs.Status),
		TypeDictionaryNodeId:            rs.TypeDictionaryNodeId,
		DomainDictionaryNodeId:          rs.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeIds: rs.AuthenticationDictionaryNodeIds,
		ApprovalInfo: types.ApprovalInfo{
			Auditors:  []types.Approval{}, // 暂时为空，等待 proto 更新
			Approvers: []types.Approval{}, // 暂时为空，等待 proto 更新
		},
		FileInfo: types.FileInfo{
			FileID: rs.FileId,
		},
	}

	return
}
