package externaldocument

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PlagiarismCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPlagiarismCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PlagiarismCheckLogic {
	return &PlagiarismCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PlagiarismCheckLogic) PlagiarismCheck(req *types.PlagiarismCheckReq) (resp *types.PlagiarismCheckResp, err error) {
	_, err = docvault.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).ImportCompanyPlagiarismCheck(l.ctx, &docvault.PlagiarismCheckReq{
		Ids: req.Ids,
	})
	if err != nil {
		return nil, err
	}
	return &types.PlagiarismCheckResp{}, nil
}
