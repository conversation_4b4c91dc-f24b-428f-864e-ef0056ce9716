package externaldocument

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDeprecatedDocumentDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDeprecatedDocumentDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDeprecatedDocumentDetailLogic {
	return &GetExternalDeprecatedDocumentDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExternalDeprecatedDocumentDetailLogic) GetExternalDeprecatedDocumentDetail(req *types.GetExternalDeprecatedDocumentDetailReq) (resp *types.GetExternalDeprecatedDocumentDetailResp, err error) {
	// 使用作废记录查询服务获取外部作废文档详情
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetExternalDeprecatedDocumentDetail(l.ctx, req)
}
