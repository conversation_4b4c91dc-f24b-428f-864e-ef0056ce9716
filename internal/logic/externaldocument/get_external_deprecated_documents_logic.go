package externaldocument

import (
	"context"

	"nebula/internal/query/deprecationrecord"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDeprecatedDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDeprecatedDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDeprecatedDocumentsLogic {
	return &GetExternalDeprecatedDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExternalDeprecatedDocumentsLogic) GetExternalDeprecatedDocuments(req *types.GetExternalDeprecatedDocumentsReq) (resp *types.GetExternalDeprecatedDocumentsResp, err error) {
	// 使用现有的作废记录查询服务
	queryService := deprecationrecord.NewDeprecationRecordQueryService(l.svcCtx)
	return queryService.GetExternalDeprecatedDocuments(l.ctx, req)
}
