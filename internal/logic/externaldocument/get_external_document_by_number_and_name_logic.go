package externaldocument

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDocumentByNumberAndNameLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDocumentByNumberAndNameLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDocumentByNumberAndNameLogic {
	return &GetExternalDocumentByNumberAndNameLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetExternalDocumentByNumberAndName 外部文档库批量纳入的查询接口
// 功能：查询集团文档时去除当前登录公司已纳入的文档
// 参数：req - 查询参数（number, name）
// 返回值：去除已纳入的外部文档 id, number, name
func (l *GetExternalDocumentByNumberAndNameLogic) GetExternalDocumentByNumberAndName(req *types.GetExternalDocumentByNumberAndNameReq) (resp *types.GetExternalDocumentByNumberAndNameResp, err error) {
	// 获取组织信息
	orgID := utils.GetContextOrganizationID(l.ctx)
	organization, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, orgID)
	if err != nil {
		logc.Errorf(l.ctx, "获取组织信息失败: %v", err)
		return nil, err
	}

	// 实例化外部文档客户端
	externalDocumentLibraryClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)

	// 查询公司外部文档
	companyDocs, err := externalDocumentLibraryClient.GetExternalDocumentByNumberAndName(l.ctx, "", "", orgID)
	if err != nil {
		logc.Errorf(l.ctx, "获取外部文件失败: %v", err)
		return nil, err
	}
	docs := make(map[string]interface{})
	for _, doc := range companyDocs {
		docs[doc.Name] = 1
	}

	// 查询集团外部文档
	groupDocs, err := externalDocumentLibraryClient.GetExternalDocumentByNumberAndName(l.ctx, req.Number, req.Name, organization.ParentId)
	if err != nil {
		logc.Errorf(l.ctx, "获取外部文件失败: %v", err)
		return nil, err
	}

	// 封装返回值，将集团中与公司重复的文件去除
	var externalDocs []types.ExternalDocInfo
	for _, doc := range groupDocs {
		if _, ok := docs[doc.Name]; !ok {
			externalDocs = append(externalDocs, types.ExternalDocInfo{
				ID:     doc.ID,
				Number: doc.Number,
				Name:   doc.Name,
			})
		}
	}

	return &types.GetExternalDocumentByNumberAndNameResp{
		Data: externalDocs,
	}, nil
}
