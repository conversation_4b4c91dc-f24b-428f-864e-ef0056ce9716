package signature

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadSignatureLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUploadSignatureLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadSignatureLogic {
	return &UploadSignatureLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UploadSignatureLogic) UploadSignature(req *types.UploadSignatureReq) (resp *types.UploadSignatureResp, err error) {
	err = l.svcCtx.Factory.SignatureService().UploadSignature(l.ctx, req.TaskId, req.SignatureBase64)
	if err != nil {
		return nil, err
	}
	return &types.UploadSignatureResp{}, nil
}
