package signature

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateSignatureTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateSignatureTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateSignatureTaskLogic {
	return &CreateSignatureTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateSignatureTaskLogic) CreateSignatureTask(req *types.CreateSignatureTaskReq) (resp *types.CreateSignatureTaskResp, err error) {
	taskId, err := l.svcCtx.Factory.SignatureService().CreateSignatureTask(l.ctx)
	if err != nil {
		return nil, err
	}
	return &types.CreateSignatureTaskResp{TaskId: taskId}, nil
}
