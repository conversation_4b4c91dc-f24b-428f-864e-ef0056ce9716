package signature

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCurrentSignatureLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCurrentSignatureLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCurrentSignatureLogic {
	return &GetCurrentSignatureLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCurrentSignatureLogic) GetCurrentSignature() (resp *types.GetCurrentSignatureResp, err error) {
	userId := utils.GetContextUserID(l.ctx)
	sigDao := mapper.NewSignatureClient(l.svcCtx.NebulaDB)
	sig, err := sigDao.GetLatestActiveSignature(l.ctx, userId)
	if err != nil {
		return nil, err
	}
	if sig.ID == "" {
		return &types.GetCurrentSignatureResp{}, nil
	}

	return &types.GetCurrentSignatureResp{
		ID:                 sig.ID,
		SignatureBase64:    sig.SignatureBase64,
		AuthLetterFileID:   sig.AuthLetterFileID,
		AuthLetterFileName: sig.AuthLetterFileName,
		EffectiveDate:      sig.CreatedAt.UnixMilli(),
		ApproverName:       l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, sig.ApproverID),
	}, nil
}
