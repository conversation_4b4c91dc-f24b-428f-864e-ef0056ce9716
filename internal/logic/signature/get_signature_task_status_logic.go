package signature

import (
	"context"

	"nebula/internal/infrastructure/referenceimpl"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSignatureTaskStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSignatureTaskStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSignatureTaskStatusLogic {
	return &GetSignatureTaskStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSignatureTaskStatusLogic) GetSignatureTaskStatus(req *types.GetSignatureTaskStatusReq) (resp *types.GetSignatureTaskStatusResp, err error) {
	task, err := referenceimpl.NewSignatureReferenceImpl(l.svcCtx.RedisAddons, l.svcCtx.MacrohardClient, l.svcCtx.ConfigManager.GetConfig(), l.svcCtx.NebulaDB).GetSignatureTask(l.ctx, req.TaskId)
	if err != nil {
		return nil, err
	}

	return &types.GetSignatureTaskStatusResp{
		SignatureBase64: task.SignatureBase64,
	}, nil
}
