package signature

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSignatureHistoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSignatureHistoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSignatureHistoryLogic {
	return &GetSignatureHistoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSignatureHistoryLogic) GetSignatureHistory(req *types.GetSignatureHistoryReq) (resp *types.GetSignatureHistoryResp, err error) {
	userId := utils.GetContextUserID(l.ctx)

	sigDao := mapper.NewSignatureClient(l.svcCtx.NebulaDB)
	list, total, err := sigDao.Page(l.ctx, mapper.PageSignatureReq{
		UserID:   userId,
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		Status:   false, // Query for expired signatures
		NoPage:   req.NoPage,
	})
	if err != nil {
		return nil, err
	}
	respList := make([]types.SignatureHistoryItem, 0, len(list))
	for _, sig := range list {
		respList = append(respList, types.SignatureHistoryItem{
			ID:                 sig.ID,
			SignatureBase64:    sig.SignatureBase64,
			AuthLetterFileID:   sig.AuthLetterFileID,
			AuthLetterFileName: sig.AuthLetterFileName,
			EffectiveDate:      sig.CreatedAt.UnixMilli(),
			ExpirationDate:     sig.UpdatedAt.UnixMilli(),
		})
	}
	return &types.GetSignatureHistoryResp{
		Data: respList,
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    uint64(total),
		},
	}, nil
}
