package respx

import (
	"context"
	"net/http"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type Body struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data,omitempty"`
}

func WriteResp(ctx context.Context, w http.ResponseWriter, resp interface{}, err error) {
	var body Body
	if err != nil {
		logc.Error(ctx, err)
		httpx.ErrorCtx(ctx, w, err)
	} else {
		body.Code = OK.Code
		body.Message = OK.Message
		body.Data = resp
		httpx.OkJsonCtx(ctx, w, body)
	}
}
