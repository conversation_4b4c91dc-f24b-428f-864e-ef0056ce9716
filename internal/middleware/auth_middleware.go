package middleware

import (
	"context"
	"nebula/internal/respx"
	"nebula/internal/utils"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

const (
	SessionPrefix = "X-Session-"
)

type AuthMiddleware struct {
}

func NewAuthMiddleware() *AuthMiddleware {
	return &AuthMiddleware{}
}

func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 遍历header
		ctx := r.Context()
		for key, values := range r.Header {
			if strings.HasPrefix(key, SessionPrefix) && len(values) > 0 {
				sessionKey := strings.TrimPrefix(key, SessionPrefix)
				ctx = context.WithValue(ctx, utils.KebabToCamel(sessionKey), values[0])
			}
		}
		if utils.GetContextUserID(ctx) == "" || utils.GetContextTenantID(ctx) == "" {
			logc.Error(r.Context(), "userId or tenantId not exist")
			w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusUnauthorized)
			httpx.Error(w, respx.TokenError)
			return
		}

		r = r.WithContext(ctx)
		next(w, r)
	}
}
