package utils

import (
	"slices"
	"strings"
)

func ExtractSliceField[S ~[]E, E any, T any](s S, item func(E) T) (result []T) {
	if len(s) == 0 {
		return make([]T, 0)
	}
	result = make([]T, len(s))
	for i := range s {
		result[i] = item(s[i])
	}
	return
}

func ExtractSliceFieldAndDuplicate[S ~[]E, E any, T comparable](s S, item func(E) T) (result []T) {
	if len(s) == 0 {
		return make([]T, 0)
	}
	m := make(map[T]struct{})
	for i := range s {
		t := item(s[i])
		if _, ok := m[t]; ok {
			continue
		}
		m[t] = struct{}{}
	}

	result = make([]T, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return
}

func JoinSliceField[S ~[]E, E any](s S, separator string, item func(E) string) (result string) {
	if len(s) == 0 {
		return ""
	}
	results := make([]string, 0, len(s))
	for i := range s {
		results = append(results, item(s[i]))
	}
	results = slices.DeleteFunc(results, func(s string) bool { return s == "" })
	return strings.Join(results, separator)
}

// 提取切片中的元素，并返回元素和是否存在的布尔值 如果true，则跳过
func ExtractSliceFieldWithSkip[S ~[]E, E any, T any](s S, item func(E) (T, bool)) (result []T) {
	if len(s) == 0 {
		return make([]T, 0)
	}
	result = make([]T, 0)
	for i := range s {
		t, ok := item(s[i])
		if ok {
			continue
		}
		result = append(result, t)
	}
	return
}

// tomap
func ExtractSliceFieldToMap[S ~[]E, E any, K comparable, V any](s S, key func(E) (K, V)) (result map[K]V) {
	if len(s) == 0 {
		return make(map[K]V)
	}
	result = make(map[K]V)
	for i := range s {
		k, v := key(s[i])
		result[k] = v
	}
	return
}

// 切片去重
func SliceDuplicate[S ~[]E, E comparable](s S) (result []E) {
	if len(s) == 0 {
		return make([]E, 0)
	}

	m := make(map[E]struct{})
	for i := range s {
		m[s[i]] = struct{}{}
	}
	result = make([]E, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return
}
