package utils

import (
	"encoding/json"
	"fmt"
	"time"
)

// ApprovalParser 审批信息解析器
// 功能：解析borrow_record表中的approval_info JSON字段
// 格式：{"auditors": [{"user_id": "577978478150839958", "passed_date": 1754544073000}], "approvers": [{"user_id": "577978478150839958", "passed_date": 1754544073000}]}
type ApprovalParser struct{}

// NewApprovalParser 创建审批信息解析器实例
// 返回值：*ApprovalParser - 解析器实例
func NewApprovalParser() *ApprovalParser {
	return &ApprovalParser{}
}

// ApprovalData 审批数据内部结构
// 用于解析JSON数据结构
type ApprovalData struct {
	Auditors []struct {
		UserID     string `json:"user_id"`
		PassedDate int64  `json:"passed_date"`
	} `json:"auditors"`
	Approvers []struct {
		UserID     string `json:"user_id"`
		PassedDate int64  `json:"passed_date"`
	} `json:"approvers"`
}

// ParseApprovalInfo 解析审批信息JSON
// 功能：解析approval_info字段，提取auditors和approvers信息
// 参数：approvalInfo - JSON字符串格式的审批信息
// 返回值：auditors - 审核人列表，approvers - 批准人列表，err - 解析错误
func (p *ApprovalParser) ParseApprovalInfo(approvalInfo string) ([]string, []string, error) {
	if approvalInfo == "" || approvalInfo == "{}" {
		return []string{}, []string{}, nil
	}

	var data ApprovalData
	if err := json.Unmarshal([]byte(approvalInfo), &data); err != nil {
		return nil, nil, fmt.Errorf("解析审批信息失败: %w", err)
	}

	// 格式化审核人为字符串列表
	formattedAuditors := make([]string, len(data.Auditors))
	for i, auditor := range data.Auditors {
		date := time.UnixMilli(auditor.PassedDate).Format("2006-01-02")
		formattedAuditors[i] = auditor.UserID + " " + date
	}

	// 格式化批准人为字符串列表
	formattedApprovers := make([]string, len(data.Approvers))
	for i, approver := range data.Approvers {
		date := time.UnixMilli(approver.PassedDate).Format("2006-01-02")
		formattedApprovers[i] = approver.UserID + " " + date
	}

	return formattedAuditors, formattedApprovers, nil
}

// ParseWithNicknames 使用用户昵称解析审批信息
// 功能：解析approval_info字段，使用用户昵称替换用户ID
// 参数：approvalInfo - JSON字符串格式的审批信息，userNicknames - 用户ID到昵称的映射
// 返回值：auditors - 审核人列表，approvers - 批准人列表
func (p *ApprovalParser) ParseWithNicknames(approvalInfo string, userNicknames map[string]string) ([]string, []string) {
	auditors, approvers, _ := p.ParseApprovalInfo(approvalInfo)
	
	// 使用用户昵称替换用户ID
	for i, auditor := range auditors {
		parts := splitUserAndDate(auditor)
		if len(parts) == 2 {
			userID := parts[0]
			date := parts[1]
			if nickname, exists := userNicknames[userID]; exists && nickname != "" {
				auditors[i] = nickname + " " + date
			}
		}
	}
	
	for i, approver := range approvers {
		parts := splitUserAndDate(approver)
		if len(parts) == 2 {
			userID := parts[0]
			date := parts[1]
			if nickname, exists := userNicknames[userID]; exists && nickname != "" {
				approvers[i] = nickname + " " + date
			}
		}
	}
	
	return auditors, approvers
}

// FormatApprovalStrings 格式化审批信息为字符串列表
// 功能：将审批信息格式化为userNickname yyyy-mm-dd的字符串列表
// 参数：approvalInfo - JSON字符串格式的审批信息，userNicknames - 用户ID到昵称的映射
// 返回值：格式化后的字符串列表，格式为userNickname yyyy-mm-dd
func (p *ApprovalParser) FormatApprovalStrings(approvalInfo string, userNicknames map[string]string) ([]string, []string, error) {
	auditors, approvers, err := p.ParseApprovalInfo(approvalInfo)
	if err != nil {
		return nil, nil, err
	}

	// 格式化审核人
	formattedAuditors := make([]string, len(auditors))
	for i, auditor := range auditors {
		// 解析原始格式 "userID yyyy-mm-dd"
		parts := splitUserAndDate(auditor)
		if len(parts) == 2 {
			nickname := userNicknames[parts[0]]
			if nickname == "" {
				nickname = parts[0] // 如果找不到昵称，使用用户ID
			}
			formattedAuditors[i] = nickname + " " + parts[1]
		} else {
			formattedAuditors[i] = auditor
		}
	}

	// 格式化批准人
	formattedApprovers := make([]string, len(approvers))
	for i, approver := range approvers {
		// 解析原始格式 "userID yyyy-mm-dd"
		parts := splitUserAndDate(approver)
		if len(parts) == 2 {
			nickname := userNicknames[parts[0]]
			if nickname == "" {
				nickname = parts[0] // 如果找不到昵称，使用用户ID
			}
			formattedApprovers[i] = nickname + " " + parts[1]
		} else {
			formattedApprovers[i] = approver
		}
	}

	return formattedAuditors, formattedApprovers, nil
}

// splitUserAndDate 分割用户ID和日期
// 功能：将"userID yyyy-mm-dd"格式的字符串分割为[userID, yyyy-mm-dd]
// 参数：str - 要分割的字符串
// 返回值：分割后的字符串切片
func splitUserAndDate(str string) []string {
	// 找到最后一个空格的位置
	for i := len(str) - 1; i >= 0; i-- {
		if str[i] == ' ' {
			return []string{str[:i], str[i+1:]}
		}
	}
	return []string{str}
}