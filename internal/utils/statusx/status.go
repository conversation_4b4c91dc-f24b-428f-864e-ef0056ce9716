package statusx

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var TokenError = status.Error(codes.Unauthenticated, "token错误或已过期")
var NotFound = status.Error(codes.NotFound, "资源不存在")
var Internal = status.Error(codes.Internal, "系统错误")
var InvalidArgument = status.Error(codes.InvalidArgument, "参数错误")

// 资源已存在
var AlreadyExists = status.Error(codes.AlreadyExists, "资源已存在")

func NewRpcNotFoundError(msg string) error {
	return status.Error(codes.NotFound, msg)
}

func NewRpcInvalidArgumentError(msg string) error {
	return status.Error(codes.InvalidArgument, msg)
}

func NewRpcUnauthenticatedError(msg string) error {
	return status.Error(codes.Unauthenticated, msg)
}

func NewRpcPermissionDeniedError(msg string) error {
	return status.Error(codes.PermissionDenied, msg)
}

func NewRpcError(code codes.Code, msg string) error {
	return status.Error(code, msg)
}

func NewRpcErrorWithCode(code codes.Code) error {
	return status.Error(code, "")
}
func NewRpcInternalError(msg string) error {
	return status.Error(codes.Internal, msg)
}

func IsRpcError(err error) bool {
	return status.Code(err) != codes.OK
}

func IsRpcUnauthenticated(err error) bool {
	return status.Code(err) == codes.Unauthenticated
}

func IsRpcNotFound(err error) bool {
	return status.Code(err) == codes.NotFound
}

func IsRpcInternal(err error) bool {
	return status.Code(err) == codes.Internal
}

func IsRpcInvalidArgument(err error) bool {
	return status.Code(err) == codes.InvalidArgument
}

func IsRpcPermissionDenied(err error) bool {
	return status.Code(err) == codes.PermissionDenied
}

func GetRpcErrorMsg(err error) string {
	return status.Convert(err).Message()
}

func IsRpcAlreadyExists(err error) bool {
	return status.Code(err) == codes.AlreadyExists
}
