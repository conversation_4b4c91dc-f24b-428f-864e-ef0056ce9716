package utils

import (
	"context"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
)

func TestStructDeepCopy(t *testing.T) {
	convey.Convey("DeepCopy", t, func() {
		now := time.Now()
		var to, form = struct {
			Name string
			Age  int
			List []string
			Info struct {
				Id  string
				D2I int64
				I2D time.Time
			}
		}{}, struct {
			Name string
			Age  int
			List []string
			Info struct {
				Id  string
				D2I time.Time
				I2D int64
			}
		}{
			Name: "bar",
			Age:  20,
			List: []string{"d"},
			Info: struct {
				Id  string
				D2I time.Time
				I2D int64
			}{Id: "456", D2I: now, I2D: now.UnixMilli()},
		}
		err := StructDeepCopy(context.Background(), &to, &form)
		convey.So(err, convey.ShouldBeNil)
		convey.So(to.Name, convey.ShouldEqual, form.Name)
		convey.So(to.Age, convey.ShouldEqual, form.Age)
		convey.So(len(to.List), convey.ShouldEqual, len(form.List))
		for i, s := range form.List {
			convey.So(to.List[i], convey.ShouldEqual, s)
		}
		convey.So(to.Info.Id, convey.ShouldEqual, form.Info.Id)
		convey.So(to.Info.Id, convey.ShouldEqual, form.Info.Id)
		convey.So(to.Info.D2I, convey.ShouldEqual, now.UnixMilli())
		convey.So(to.Info.I2D.UnixMilli(), convey.ShouldEqual, now.UnixMilli())
	})
}

func TestStructCopy(t *testing.T) {
	convey.Convey("DeepCopy", t, func() {
		var to, form = struct {
			Name string
			Age  int
		}{}, struct {
			Name string
			Age  int
		}{
			Name: "bar",
			Age:  20,
		}
		err := StructDeepCopy(context.Background(), &to, &form)
		convey.So(err, convey.ShouldBeNil)
		convey.So(to.Name, convey.ShouldEqual, form.Name)
		convey.So(to.Age, convey.ShouldEqual, form.Age)
	})
}

func TestArrayCopy(t *testing.T) {
	convey.Convey("ArrayCopy", t, func() {
		var to = struct {
			Name string
			Age  int
		}{}

		var form = []struct {
			Name string
			Age  int
		}{
			{
				Name: "bar",
				Age:  20,
			},
			{
				Name: "bar2",
				Age:  21,
			},
			{
				Name: "bar3",
				Age:  22,
			},
			{
				Name: "bar4",
				Age:  23,
			},
		}
		rt, err := ArrayCopy(context.Background(), to, form)
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(rt), convey.ShouldEqual, len(form))
		for i, s := range form {
			convey.So(rt[i].Name, convey.ShouldEqual, s.Name)
			convey.So(rt[i].Age, convey.ShouldEqual, s.Age)
		}
	})
}
