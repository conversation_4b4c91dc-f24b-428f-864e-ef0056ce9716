package document_library

import (
	"net/http"

	"nebula/internal/logic/document_library"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ImportDocumentLibraryHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ImportDocumentLibraryReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := document_library.NewImportDocumentLibraryLogic(r.Context(), svcCtx)
		resp, err := l.ImportDocumentLibrary(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
