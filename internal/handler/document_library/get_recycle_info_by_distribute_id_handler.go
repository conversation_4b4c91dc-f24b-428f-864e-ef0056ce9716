package document_library

import (
	"nebula/internal/respx"
	"net/http"

	"nebula/internal/logic/document_library"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// GetRecycleInfoByDistributeIdHandler 根据发放清单ID查询回收信息处理器
func GetRecycleInfoByDistributeIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetRecycleInfoByDistributeIdReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := document_library.NewGetRecycleInfoByDistributeIdLogic(r.Context(), svcCtx)
		resp, err := l.GetRecycleInfoByDistributeId(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
