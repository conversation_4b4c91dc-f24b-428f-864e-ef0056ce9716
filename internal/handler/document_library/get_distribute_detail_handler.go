package document_library

import (
	"nebula/internal/respx"
	"net/http"

	"nebula/internal/logic/document_library"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetDistributeDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetDistributeDetailReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := document_library.NewGetDistributeDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetDistributeDetail(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
