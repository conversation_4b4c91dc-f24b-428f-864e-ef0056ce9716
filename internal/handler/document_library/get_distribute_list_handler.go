package document_library

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/document_library"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GetDistributeListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetDistributeListReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := document_library.NewGetDistributeListLogic(r.Context(), svcCtx)
		resp, err := l.GetDistributeList(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
