package document_library

import (
	"net/http"

	"nebula/internal/logic/document_library"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetLoanRecordsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetLoanRecordsReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := document_library.NewGetLoanRecordsLogic(r.Context(), svcCtx)
		resp, err := l.GetLoanRecords(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
