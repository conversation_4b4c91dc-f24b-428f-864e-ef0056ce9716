package internaldocument

import (
	"net/http"

	"nebula/internal/logic/internaldocument"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetInternalDeprecatedDocumentsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetInternalDeprecatedDocumentsReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := internaldocument.NewGetInternalDeprecatedDocumentsLogic(r.Context(), svcCtx)
		resp, err := l.GetInternalDeprecatedDocuments(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
