package internaldocument

import (
	"net/http"

	"nebula/internal/logic/internaldocument"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetInternalDocumentHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetInternalDocumentReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := internaldocument.NewGetInternalDocumentLogic(r.Context(), svcCtx)
		resp, err := l.GetInternalDocument(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
