package signature

import (
	"net/http"

	"nebula/internal/logic/signature"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetSignatureTaskStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetSignatureTaskStatusReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := signature.NewGetSignatureTaskStatusLogic(r.Context(), svcCtx)
		resp, err := l.GetSignatureTaskStatus(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
