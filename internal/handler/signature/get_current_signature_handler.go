package signature

import (
	"net/http"

	"nebula/internal/logic/signature"
	"nebula/internal/respx"
	"nebula/internal/svc"
)

func GetCurrentSignatureHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := signature.NewGetCurrentSignatureLogic(r.Context(), svcCtx)
		resp, err := l.GetCurrentSignature()
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
