package file_management_books

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/file_management_books"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GiveBackBookHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GiveBackBookReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := file_management_books.NewGiveBackBookLogic(r.Context(), svcCtx)
		resp, err := l.GiveBackBook(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
