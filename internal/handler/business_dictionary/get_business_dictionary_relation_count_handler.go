package business_dictionary

import (
	"net/http"

	"nebula/internal/logic/business_dictionary"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetBusinessDictionaryRelationCountHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetBusinessDictionaryRelationCountReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := business_dictionary.NewGetBusinessDictionaryRelationCountLogic(r.Context(), svcCtx)
		resp, err := l.GetBusinessDictionaryRelationCount(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
