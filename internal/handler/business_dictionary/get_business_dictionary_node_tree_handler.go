package business_dictionary

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/business_dictionary"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GetBusinessDictionaryNodeTreeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetBusinessDictionaryNodeTreeReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := business_dictionary.NewGetBusinessDictionaryNodeTreeLogic(r.Context(), svcCtx)
		resp, err := l.GetBusinessDictionaryNodeTree(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
