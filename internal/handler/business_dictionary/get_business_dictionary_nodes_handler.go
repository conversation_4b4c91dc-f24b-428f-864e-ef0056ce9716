package business_dictionary

import (
	"net/http"

	"nebula/internal/logic/business_dictionary"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetBusinessDictionaryNodesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetBusinessDictionaryNodesReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := business_dictionary.NewGetBusinessDictionaryNodesLogic(r.Context(), svcCtx)
		resp, err := l.GetBusinessDictionaryNodes(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
