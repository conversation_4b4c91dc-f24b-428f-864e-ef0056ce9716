package business_dictionary

import (
	"net/http"

	"nebula/internal/logic/business_dictionary"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func MoveBusinessDictionaryNodeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MoveBusinessDictionaryNodeReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := business_dictionary.NewMoveBusinessDictionaryNodeLogic(r.Context(), svcCtx)
		resp, err := l.MoveBusinessDictionaryNode(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
