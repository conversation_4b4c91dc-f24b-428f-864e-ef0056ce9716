package business_dictionary

import (
	"net/http"

	"nebula/internal/logic/business_dictionary"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func CreateBusinessDictionaryNodeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateBusinessDictionaryNodeReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := business_dictionary.NewCreateBusinessDictionaryNodeLogic(r.Context(), svcCtx)
		resp, err := l.CreateBusinessDictionaryNode(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
