package externaldocument

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/externaldocument"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GetExternalDocumentsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetExternalDocumentsReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewGetExternalDocumentsLogic(r.Context(), svcCtx)
		resp, err := l.GetExternalDocuments(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
