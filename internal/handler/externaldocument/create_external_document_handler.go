package externaldocument

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/externaldocument"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func CreateExternalDocumentHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateExternalDocumentReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewCreateExternalDocumentLogic(r.Context(), svcCtx)
		resp, err := l.CreateExternalDocument(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
