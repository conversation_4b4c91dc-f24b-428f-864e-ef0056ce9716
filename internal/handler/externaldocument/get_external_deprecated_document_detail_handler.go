package externaldocument

import (
	"net/http"

	"nebula/internal/logic/externaldocument"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetExternalDeprecatedDocumentDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetExternalDeprecatedDocumentDetailReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewGetExternalDeprecatedDocumentDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetExternalDeprecatedDocumentDetail(&req)
		respx.WriteResp(r.<PERSON>text(), w, resp, err)
	}
}
