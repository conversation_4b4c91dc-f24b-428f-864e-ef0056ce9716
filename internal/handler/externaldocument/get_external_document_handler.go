package externaldocument

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/externaldocument"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GetExternalDocumentHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetExternalDocumentReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewGetExternalDocumentLogic(r.Context(), svcCtx)
		resp, err := l.GetExternalDocument(&req)
		respx.WriteResp(r.Context(), w, resp, err)
	}
}
