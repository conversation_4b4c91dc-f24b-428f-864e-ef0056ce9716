package externaldocument

import (
	"net/http"

	"nebula/internal/logic/externaldocument"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetExternalDocumentChangeRecordsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetExternalDocumentChangeRecordsReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewGetExternalDocumentChangeRecordsLogic(r.Context(), svcCtx)
		resp, err := l.GetExternalDocumentChangeRecords(&req)
		respx.WriteResp(r.<PERSON>text(), w, resp, err)
	}
}
