package externaldocument

import (
	"nebula/internal/respx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"nebula/internal/logic/externaldocument"
	"nebula/internal/svc"
	"nebula/internal/types"
)

func GetExternalDocumentByNumberAndNameHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetExternalDocumentByNumberAndNameReq
		if err := httpx.Parse(r, &req); err != nil {
			respx.WriteResp(r.Context(), w, nil, respx.ParamError)
			return
		}
		l := externaldocument.NewGetExternalDocumentByNumberAndNameLogic(r.Context(), svcCtx)
		resp, err := l.GetExternalDocumentByNumberAndName(&req)
		respx.WriteResp(r.<PERSON><PERSON>(), w, resp, err)
	}
}
