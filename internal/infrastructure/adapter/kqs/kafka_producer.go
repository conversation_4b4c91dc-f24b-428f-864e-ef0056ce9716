package kqs

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"

	"github.com/segmentio/kafka-go"
)

// KafkaConfig 定义Kafka相关配置
type KafkaConfig struct {
	Brokers []string `yaml:"brokers"`
}

// KafkaProducer 单一职责：只负责一个topic的生产
// 线程安全，适合高并发场景
type KafkaProducer struct {
	writer *kafka.Writer
	topic  string
}

// NewKafkaProducer 创建单一topic的Producer实例
func NewKafkaProducer(brokers []string, topic string) *KafkaProducer {
	return &KafkaProducer{
		topic: topic,
		writer: &kafka.Writer{
			Addr:     kafka.TCP(brokers...),
			Topic:    topic,
			Balancer: &kafka.LeastBytes{},
		},
	}
}

// SendMessage 发送消息到构造时指定的topic
func (p *KafkaProducer) SendMessage(ctx context.Context, key string, value any) error {
	if value == nil {
		value = ""
	}
	v, err := tooBytes(value)
	if err != nil {
		return err
	}
	msg := kafka.Message{
		Key:   []byte(key),
		Value: v,
	}
	err = p.writer.WriteMessages(ctx, msg)
	if err != nil {
		log.Printf("Kafka消息发送失败，topic=%s, err=%v", p.topic, err)
	}
	return err
}

// Close 关闭writer资源
func (p *KafkaProducer) Close() error {
	return p.writer.Close()
}

// ToBytes 通用类型转 []byte 工具函数
// 支持 string、[]byte、int、int64、float64、bool、json.RawMessage、struct、map、slice 等常见类型
// 复杂类型自动使用 json.Marshal 序列化，未知类型返回错误
func tooBytes(val interface{}) ([]byte, error) {
	switch v := val.(type) {
	case nil:
		return nil, nil
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	case json.RawMessage:
		return []byte(v), nil
	case int:
		return []byte(strconv.Itoa(v)), nil
	case int64:
		return []byte(strconv.FormatInt(v, 10)), nil
	case float64:
		return []byte(strconv.FormatFloat(v, 'f', -1, 64)), nil
	case bool:
		if v {
			return []byte("true"), nil
		}
		return []byte("false"), nil
	default:
		// 复杂类型（struct、map、slice等）用json序列化
		bs, err := json.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("ToBytes: marshal error: %w", err)
		}
		return bs, nil
	}
}
