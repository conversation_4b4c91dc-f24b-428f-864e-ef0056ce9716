package kqs

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
)

const (
	// 1导出中，2可下载，3已下载，4导出失败
	DataExportStatusProgress   = 1
	DataExportStatusComplete   = 2
	DataExportStatusDownloaded = 3
	DataExportStatusFailed     = 4
)

type DataExportModelInfo struct {
	TaskID     string `json:"task_id"`
	FileID     string `json:"file_id"`
	FileName   string `json:"file_name"`
	Status     uint   `json:"status"`
	ModuleName string `json:"module_name"`
	UserID     string `json:"user_id"`
}

// DataExportProducerInterface 数据导出生产者接口
//
//go:generate mockgen -source=data_export_producer.go -destination=mock_data_export_producer.go -package=kqs
type DataExportProducer interface {
	SendMessage(ctx context.Context, value DataExportModelInfo) error
	Close() error
}

// DataExportProducer 数据导出专用Kafka生产者
// 单一职责：只负责一个topic的生产，线程安全
// 用于数据导出相关业务
type DataExportProducerImpl struct {
	KafkaProducer *KafkaProducer
}

// NewDataExportProducer 创建数据导出专用Producer实例
func NewDataExportProducer(brokers []string, topic string) *DataExportProducerImpl {
	return &DataExportProducerImpl{
		KafkaProducer: NewKafkaProducer(brokers, topic),
	}
}

// SendMessage 发送消息到构造时指定的topic
func (p *DataExportProducerImpl) SendMessage(ctx context.Context, value DataExportModelInfo) error {
	err := p.KafkaProducer.SendMessage(ctx, value.TaskID, value)
	if err != nil {
		logc.Errorf(ctx, "数据导出消息发送失败，taskId=%s, err=%v", value.TaskID, err)
	}
	return err
}

// Close 关闭writer资源
func (p *DataExportProducerImpl) Close() error {
	return p.KafkaProducer.Close()
}
