// Code generated by MockGen. DO NOT EDIT.
// Source: data_export_producer.go

// Package kqs is a generated GoMock package.
package kqs

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockDataExportProducer is a mock of DataExportProducer interface.
type MockDataExportProducer struct {
	ctrl     *gomock.Controller
	recorder *MockDataExportProducerMockRecorder
}

// MockDataExportProducerMockRecorder is the mock recorder for MockDataExportProducer.
type MockDataExportProducerMockRecorder struct {
	mock *MockDataExportProducer
}

// NewMockDataExportProducer creates a new mock instance.
func NewMockDataExportProducer(ctrl *gomock.Controller) *MockDataExportProducer {
	mock := &MockDataExportProducer{ctrl: ctrl}
	mock.recorder = &MockDataExportProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataExportProducer) EXPECT() *MockDataExportProducerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockDataExportProducer) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockDataExportProducerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockDataExportProducer)(nil).Close))
}

// SendMessage mocks base method.
func (m *MockDataExportProducer) SendMessage(ctx context.Context, value DataExportModelInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockDataExportProducerMockRecorder) SendMessage(ctx, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockDataExportProducer)(nil).SendMessage), ctx, value)
}
