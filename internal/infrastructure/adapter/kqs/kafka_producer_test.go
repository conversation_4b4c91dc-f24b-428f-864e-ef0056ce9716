package kqs

import (
	"context"
	"testing"
)

func TestProducer_SendMessage(t *testing.T) {
	brokers := []string{"192.168.110.74:9092"}
	topic := "test002"
	producer := NewKafkaProducer(brokers, topic)
	defer producer.Close()

	t.Run("send string", func(t *testing.T) {
		err := producer.SendMessage(context.Background(), "key1", "hello kafka-go")
		if err != nil {
			t.Logf("SendMessage error: %v (如未启动Kafka服务可忽略)", err)
		}
	})

	t.Run("send struct", func(t *testing.T) {
		type Demo struct {
			Name string
			Age  int
		}
		err := producer.SendMessage(context.Background(), "key2", Demo{Name: "张三", Age: 18})
		if err != nil {
			t.Logf("SendMessage error: %v (如未启动Kafka服务可忽略)", err)
		}
	})

	t.Run("send nil", func(t *testing.T) {
		err := producer.SendMessage(context.Background(), "key3", nil)
		if err != nil {
			t.Logf("SendMessage error: %v (如未启动Kafka服务可忽略)", err)
		}
	})

	t.Run("close twice", func(t *testing.T) {
		_ = producer.Close()
		_ = producer.Close()
	})
}
