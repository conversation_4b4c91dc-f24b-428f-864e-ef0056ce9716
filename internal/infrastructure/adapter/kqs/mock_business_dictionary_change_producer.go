// Code generated by MockGen. DO NOT EDIT.
// Source: business_dictionary_change_producer.go

// Package kqs is a generated GoMock package.
package kqs

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockBusinessDictionaryChangeProducerInterface is a mock of BusinessDictionaryChangeProducerInterface interface.
type MockBusinessDictionaryChangeProducerInterface struct {
	ctrl     *gomock.Controller
	recorder *MockBusinessDictionaryChangeProducerInterfaceMockRecorder
}

// MockBusinessDictionaryChangeProducerInterfaceMockRecorder is the mock recorder for MockBusinessDictionaryChangeProducerInterface.
type MockBusinessDictionaryChangeProducerInterfaceMockRecorder struct {
	mock *MockBusinessDictionaryChangeProducerInterface
}

// NewMockBusinessDictionaryChangeProducerInterface creates a new mock instance.
func NewMockBusinessDictionaryChangeProducerInterface(ctrl *gomock.Controller) *MockBusinessDictionaryChangeProducerInterface {
	mock := &MockBusinessDictionaryChangeProducerInterface{ctrl: ctrl}
	mock.recorder = &MockBusinessDictionaryChangeProducerInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBusinessDictionaryChangeProducerInterface) EXPECT() *MockBusinessDictionaryChangeProducerInterfaceMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockBusinessDictionaryChangeProducerInterface) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockBusinessDictionaryChangeProducerInterfaceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockBusinessDictionaryChangeProducerInterface)(nil).Close))
}

// SendMessage mocks base method.
func (m *MockBusinessDictionaryChangeProducerInterface) SendMessage(ctx context.Context, values []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, values)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockBusinessDictionaryChangeProducerInterfaceMockRecorder) SendMessage(ctx, values interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockBusinessDictionaryChangeProducerInterface)(nil).SendMessage), ctx, values)
}
