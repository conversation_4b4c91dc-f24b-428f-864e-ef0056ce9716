package kqs

import (
	"context"
	"log"

	"github.com/segmentio/kafka-go"
)

// MessageHandler defines the interface for handling Kafka messages.
type MessageHandler interface {
	Handle(ctx context.Context, message []byte) error
}

// KafkaConsumer is a generic Kafka consumer adapter.
type KafkaConsumer struct {
	readerConfig kafka.ReaderConfig
	handler      MessageHandler
}

// NewKafkaConsumer creates a new generic KafkaConsumer.
func NewKafkaConsumer(readerConfig kafka.ReaderConfig, handler MessageHandler) *KafkaConsumer {
	return &KafkaConsumer{
		readerConfig: readerConfig,
		handler:      handler,
	}
}

// Start starts the Kafka consumer.
func (c *KafkaConsumer) Start(ctx context.Context) {
	log.Printf("[KafkaConsumer] 启动监听Topic: %s ...", c.readerConfig.Topic)

	r := kafka.NewReader(c.readerConfig)
	defer r.Close()

	for {
		m, err := r.ReadMessage(ctx)
		if err != nil {
			log.Printf("读取Kafka消息失败: %v", err)
			break
		}

		err = c.handler.Handle(ctx, m.Value)
		if err != nil {
			log.Printf("处理Kafka消息失败: %v", err)
		}
	}
}

func headersToMap(headers []kafka.Header) map[string]string {
	m := make(map[string]string)
	for _, h := range headers {
		m[h.Key] = string(h.Value)
	}
	return m
}
