package mapper

const (
	TableNameDisposalRecordPermission = "disposal_record_permissions"
)

// DisposalRecordPermission 对应 disposal_record_permissions 表
type DisposalRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联DisposalRecordFile的ID'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
}

func (DisposalRecordPermission) TableName() string {
	return TableNameDisposalRecordPermission
}
