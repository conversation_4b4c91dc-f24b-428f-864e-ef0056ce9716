package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	BusinessDictionaryTableName = "business_dictionary"
)

type BusinessDictionaryClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewBusinessDictionaryClient(db *NebulaDB) *BusinessDictionaryClient {
	return &BusinessDictionaryClient{
		DB: db.db,
	}
}

type GetBusinessDictionarysParams struct {
	Search   string
	Page     int
	PageSize int
	NoPage   bool
	TenantID string
}

// 分页查询
func (c *BusinessDictionaryClient) GetBusinessDictionarys(ctx context.Context, params GetBusinessDictionarysParams) (dictionaries []BusinessDictionary, total int64, err error) {
	query := c.DB.WithContext(ctx).Model(&BusinessDictionary{}).Where("module_name LIKE ? OR field_name LIKE ?", "%"+params.Search+"%", "%"+params.Search+"%").Where("tenant_id = ?", params.TenantID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if total == 0 {
		return nil, 0, nil
	}

	if !params.NoPage {
		if params.Page <= 0 || params.PageSize <= 0 {
			params.Page = 1
			params.PageSize = 10
		}
		query = query.Order("created_at DESC").Offset((params.Page - 1) * params.PageSize).Limit(params.PageSize)
	}

	if err := query.Find(&dictionaries).Error; err != nil {
		return nil, 0, err
	}

	return dictionaries, total, nil
}

// 更新更新时间
func (c *BusinessDictionaryClient) UpdateBusinessDictionaryUpdatedAt(ctx context.Context, tx *NebulaTX, id string, updatedAt time.Time, updatedBy string) error {
	return tx.GetTX().Model(&BusinessDictionary{}).Where("id = ?", id).Updates(map[string]interface{}{
		"updated_at": updatedAt,
		"updated_by": updatedBy,
	}).Error
}

type BusinessDictionary struct {
	ID         string    `gorm:"primaryKey;column:id" json:"id"`
	ModuleName string    `gorm:"column:module_name" json:"moduleName"`
	FieldName  string    `gorm:"column:field_name" json:"fieldName"`
	Status     bool      `gorm:"column:status" json:"status"`
	IsDeleted  bool      `gorm:"column:is_deleted" json:"isDeleted"`
	CreatedAt  time.Time `gorm:"column:created_at" json:"createdAt"`
	CreatedBy  string    `gorm:"column:created_by" json:"createdBy"`
	UpdatedAt  time.Time `gorm:"column:updated_at" json:"updatedAt"`
	UpdatedBy  string    `gorm:"column:updated_by" json:"updatedBy"`
	TenantID   string    `gorm:"column:tenant_id" json:"tenantId"`
}

func (BusinessDictionary) TableName() string {
	return BusinessDictionaryTableName
}
