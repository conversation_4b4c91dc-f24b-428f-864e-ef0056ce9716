package mapper

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type BusinessDictionaryNodeRelationClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewBusinessDictionaryNodeRelationClient(db *NebulaDB) *BusinessDictionaryNodeRelationClient {
	return &BusinessDictionaryNodeRelationClient{DB: db.db}
}

func (c *BusinessDictionaryNodeRelationClient) GetBusinessDictionaryNodeRelationByNodeIDs(ctx context.Context, nodeIDs []string) ([]BusinessDictionaryNodeRelation, error) {
	var relations []BusinessDictionaryNodeRelation
	if err := c.DB.WithContext(ctx).Model(&BusinessDictionaryNodeRelation{}).Where("node_id IN (?)", nodeIDs).Find(&relations).Error; err != nil {
		return []BusinessDictionaryNodeRelation{}, err
	}
	return relations, nil
}

func (c *BusinessDictionaryNodeRelationClient) GetBusinessDictionaryNodeRelationByNodeID(ctx context.Context, nodeID string) (BusinessDictionaryNodeRelation, error) {
	var relation BusinessDictionaryNodeRelation
	if err := c.DB.WithContext(ctx).Model(&BusinessDictionaryNodeRelation{}).Where("node_id = ?", nodeID).First(&relation).Error; err != nil {
		return BusinessDictionaryNodeRelation{}, err
	}
	return relation, nil
}

func (c *BusinessDictionaryNodeRelationClient) GetBusinessDictionaryNodeRelationByNames(ctx context.Context, names []string, dictionaryID string) ([]BusinessDictionaryNodeRelation, error) {
	var relations []BusinessDictionaryNodeRelation
	if err := c.DB.WithContext(ctx).Model(&BusinessDictionaryNodeRelation{}).Where("names in (?) and dictionary_id = ?", names, dictionaryID).Find(&relations).Error; err != nil {
		return []BusinessDictionaryNodeRelation{}, err
	}
	return relations, nil
}

func (c *BusinessDictionaryNodeRelationClient) BatchUpsertRelations(ctx context.Context, tx *NebulaTX, relations []BusinessDictionaryNodeRelation) error {
	if len(relations) == 0 {
		return nil
	}
	return c.DB.WithContext(ctx).Clauses(
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "node_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"dictionary_id", "codes", "names"}),
		},
	).Create(&relations).Error
}

func (c *BusinessDictionaryNodeRelationClient) DeleteBusinessDictionaryNodeRelation(ctx context.Context, tx *NebulaTX, nodeIDs []string) error {
	return tx.GetTX().WithContext(ctx).Where("node_id IN (?)", nodeIDs).Delete(&BusinessDictionaryNodeRelation{}).Error
}

type BusinessDictionaryNodeRelation struct {
	NodeID       string `gorm:"column:node_id;type:varchar(64);primaryKey" json:"node_id"`  // 节点ID
	DictionaryID string `gorm:"column:dictionary_id;type:varchar(64)" json:"dictionary_id"` // 字典ID
	Codes        string `gorm:"column:codes;type:varchar(500);not null" json:"codes"`       // 拼接的代码
	Names        string `gorm:"column:names;type:varchar(500);not null" json:"names"`       // 拼接的名称
}

// TableName 指定表名
func (BusinessDictionaryNodeRelation) TableName() string {
	return "business_dictionary_node_relation"
}
