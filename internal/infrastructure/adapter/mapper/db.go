package mapper

import (
	"fmt"
	"nebula/internal/config"
	"nebula/internal/utils/logs"
	"time"

	dameng "github.com/godoes/gorm-dameng"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type NebulaDB struct {
	db *gorm.DB
}

// NewNebulaDB 初始化saas_db连接
func NewNebulaDB(c config.Config) (result *NebulaDB) {
	db := openDb(c.NebulaDB, logs.NewGormLogger(c.Mode))
	return &NebulaDB{
		db: db,
	}
}

func (db *NebulaDB) GetDB() *gorm.DB {
	return db.db
}

type DocvaultDB struct {
	db *gorm.DB
}

// NewdocvaultDB 初始化saas_db连接
func NewDocvaultDB(c config.Config) (result *DocvaultDB) {
	db := openDb(c.DocvaultDB, logs.NewGormLogger(c.Mode))
	return &DocvaultDB{
		db: db,
	}
}

func (db *DocvaultDB) GetDB() *gorm.DB {
	return db.db
}

type PhoenixDB struct {
	db *gorm.DB
}

// NewPhoenixDB 初始化saas_db连接
func NewPhoenixDB(c config.Config) (result *PhoenixDB) {
	db := openDb(c.PhoenixDB, logs.NewGormLogger(c.Mode))
	return &PhoenixDB{
		db: db,
	}
}

func (db *PhoenixDB) GetDB() *gorm.DB {
	return db.db
}

func openDb(dbConf config.DBConf, logger *logs.GormLogger) *gorm.DB {
	var dialect gorm.Dialector
	if dbConf.Type == "dm" {
		dsn := fmt.Sprintf("dm://%s:%s@%s:%s?schema=%s",
			dbConf.User, dbConf.Password,
			dbConf.Host, dbConf.Port,
			dbConf.Schema)
		dialect = dameng.Open(dsn)
	} else {
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8&parseTime=True&loc=Local&sql_mode=ANSI_QUOTES",
			dbConf.User, dbConf.Password,
			dbConf.Host, dbConf.Port,
			dbConf.Schema)
		dialect = mysql.New(mysql.Config{
			DSN:                       dsn,
			DisableDatetimePrecision:  true,
			DontSupportRenameIndex:    true,
			DontSupportRenameColumn:   true,
			SkipInitializeWithVersion: false,
		})
	}

	db, err := gorm.Open(dialect, &gorm.Config{
		Logger: logger, //Logger: gorm.Default.LogMode(gorm.Info),
	})
	if err != nil {
		panic("init db fail:" + err.Error())
	}
	// 维护连接池
	sqlDB, err := db.DB()
	if err != nil {
		panic("init db fail:" + err.Error())
	}

	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(dbConf.MaxIdleConns)

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(dbConf.MaxOpenConns)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	sqlDB.SetConnMaxLifetime(time.Duration(dbConf.ConnMaxLifetime) * time.Minute)

	return db
}
