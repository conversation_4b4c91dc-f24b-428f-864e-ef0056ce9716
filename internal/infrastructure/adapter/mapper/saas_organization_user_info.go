package mapper

import (
	"gorm.io/gorm"
)

const TableNameSaasOrganizationUserInfo = "saas_organization_user_info"

type SaasOrganizationUserInfoClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewSaasOrganizationUserInfoClient(db *PhoenixDB) *SaasOrganizationUserInfoClient {
	return &SaasOrganizationUserInfoClient{
		DB: db.GetDB(),
	}
}

// SaasOrganizationUserInfo 组织用户信息记录表
type SaasOrganizationUserInfo struct {
	Id             string `gorm:"column:id;primary_key;NOT NULL;comment:'Snowflake ID  全局唯一ID'"`
	CreatedAt      string `gorm:"column:created_at;comment:'Created Time  创建时间'"`
	UpdatedAt      string `gorm:"column:updated_at;comment:'Updated Time  更新时间'"`
	Sort           uint32 `gorm:"column:sort;default:1;NOT NULL;comment:'Sort number  排序编号'"`
	DeletedAt      string `gorm:"column:deleted_at;comment:'Deleted Time  删除时间（软删除标识）'"`
	Extra          string `gorm:"column:extra;comment:'Extra information  额外信息(json对象格式存储，用于存储的额外展示信息)'"`
	OrganizationId string `gorm:"column:organization_id;NOT NULL;comment:'Organization ID  组织架构 ID'"`
	UserId         string `gorm:"column:user_id;NOT NULL;comment:'User ID  用户 ID'"`
	IsLeader       int8   `gorm:"column:is_leader;default:0;NOT NULL;comment:'是否领导(0否1是)'"`
	IsAdmin        int8   `gorm:"column:is_admin;default:0;comment:'是否管理员(0否1是)'"`
}

// TableName 表名
func (s *SaasOrganizationUserInfo) TableName() string {
	return TableNameSaasOrganizationUserInfo
}
