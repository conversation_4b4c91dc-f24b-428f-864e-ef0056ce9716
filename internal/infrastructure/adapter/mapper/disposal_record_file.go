package mapper

import (
	"context"

	"gorm.io/gorm"
)

const (
	TableNameDisposalRecordFile = "disposal_record_files"
)

// DisposalRecordFile 对应 disposal_record_files 表
type DisposalRecordFile struct {
	ID                     string `gorm:"type:varchar(64);primary_key"`
	RecordID               string `gorm:"type:varchar(64);index;comment:'关联DisposalRecord的ID'"`
	DistributeRecordFileID string `gorm:"type:varchar(64);comment:'清单文件id'"`
}

func (DisposalRecordFile) TableName() string {
	return TableNameDisposalRecordFile
}

// DisposalRecordFileClient 是 disposal_record_files 表的数据访问客户端
type DisposalRecordFileClient struct {
	db *gorm.DB
}

// NewDisposalRecordFileClient 创建一个新的 DisposalRecordFileClient 实例
func NewDisposalRecordFileClient(db *DocvaultDB) *DisposalRecordFileClient {
	return &DisposalRecordFileClient{
		db: db.GetDB(),
	}
}

// FindByDistributeRecordFileIDs 根据发放记录文件ID列表查询处置记录文件关联
// 功能：查询指定发放记录文件ID对应的处置记录文件关联
// 参数：ctx - 上下文，distributeRecordFileIDs - 发放记录文件ID列表
// 返回值：处置记录文件关联列表，错误信息
func (c *DisposalRecordFileClient) FindByDistributeRecordFileIDs(ctx context.Context, distributeRecordFileIDs []string) ([]DisposalRecordFile, error) {
	var disposalRecordFiles []DisposalRecordFile
	err := c.db.WithContext(ctx).Where("distribute_record_file_id IN ?", distributeRecordFileIDs).Find(&disposalRecordFiles).Error
	return disposalRecordFiles, err
}
