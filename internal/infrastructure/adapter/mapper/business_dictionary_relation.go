package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	BusinessDictionaryRelationTableName = "business_dictionary_relation"
)

type BusinessDictionaryRelationClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewBusinessDictionaryRelationClient(db *NebulaDB) *BusinessDictionaryRelationClient {
	return &BusinessDictionaryRelationClient{
		DB: db.db,
	}
}

// 根据节点id批量查询
func (c *BusinessDictionaryRelationClient) GetBusinessDictionaryRelationsByNodeID(ctx context.Context, nodeIDs []string) ([]BusinessDictionaryRelation, error) {
	var relations []BusinessDictionaryRelation
	if err := c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("dictionary_node_id IN (?)", nodeIDs).Order("created_at DESC").Find(&relations).Error; err != nil {
		return nil, err
	}
	return relations, nil
}

// 创建关系
func (c *BusinessDictionaryRelationClient) CreateBusinessDictionaryRelation(ctx context.Context, relation BusinessDictionaryRelation) error {
	return c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Create(&relation).Error
}

// 批量创建关系
func (c *BusinessDictionaryRelationClient) TxBatchCreateBusinessDictionaryRelation(ctx context.Context, relation []BusinessDictionaryRelation, tx *NebulaTX) error {
	return tx.GetTX().WithContext(ctx).Model(&BusinessDictionaryRelation{}).Create(&relation).Error
}

func (c *BusinessDictionaryRelationClient) BatchCreateBusinessDictionaryRelation(ctx context.Context, relation []BusinessDictionaryRelation) error {
	return c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Create(&relation).Error
}

// 根据业务 id 更新记录
func (c *BusinessDictionaryRelationClient) UpdateBusinessDictionaryRelationByBusinessID(ctx context.Context, businessID, businessType, nodeID string) error {
	return c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("business_id = ? and business_type = ?", businessID, businessType).Updates(map[string]interface{}{
		"dictionary_node_id": nodeID,
	}).Error
}

func (c *BusinessDictionaryRelationClient) TxUpdateBusinessDictionaryRelationByBusinessID(ctx context.Context, businessID, businessType, nodeID string, tx *NebulaTX) error {
	return tx.GetTX().WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("business_id = ? and business_type = ?", businessID, businessType).Updates(map[string]interface{}{
		"dictionary_node_id": nodeID,
	}).Error
}

func (c *BusinessDictionaryRelationClient) DeleteBusinessDictionaryRelationByBusinessID(ctx context.Context, businessID string) error {
	return c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("business_id = ?", businessID).Delete(&BusinessDictionaryNodeRelation{}).Error
}

func (c *BusinessDictionaryRelationClient) DeleteBusinessDictionaryRelationByBusinessIDAndType(ctx context.Context, businessID, businessType string) error {
	return c.DB.WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("business_id = ? and business_type = ?", businessID, businessType).Delete(&BusinessDictionaryNodeRelation{}).Error
}

func (c *BusinessDictionaryRelationClient) TxDeleteBusinessDictionaryRelationByBusinessIDAndType(ctx context.Context, businessID, businessType string, tx *NebulaTX) error {
	return tx.GetTX().WithContext(ctx).Model(&BusinessDictionaryRelation{}).Where("business_id = ? and business_type = ?", businessID, businessType).Delete(&BusinessDictionaryNodeRelation{}).Error
}

type BusinessDictionaryRelation struct {
	ID               string    `gorm:"primaryKey;column:id" json:"id"`
	DictionaryNodeID string    `gorm:"column:dictionary_node_id" json:"dictionaryNodeId"`
	BusinessID       string    `gorm:"column:business_id" json:"businessId"`
	BusinessType     string    `gorm:"column:business_type" json:"businessType"`
	CreatedAt        time.Time `gorm:"column:created_at" json:"createdAt"`
	CreatedBy        string    `gorm:"column:created_by" json:"createdBy"`
}

func (BusinessDictionaryRelation) TableName() string {
	return BusinessDictionaryRelationTableName
}
