package mapper

import (
	"context"

	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordPermission = "recycle_record_permissions"
)

// RecycleRecordPermission 对应 recycle_record_permissions 表
type RecycleRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联RecycleRecordFile的ID'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
}

func (RecycleRecordPermission) TableName() string {
	return TableNameRecycleRecordPermission
}

// RecycleRecordPermissionClient 是 recycle_record_permissions 表的数据访问客户端
type RecycleRecordPermissionClient struct {
	db *gorm.DB
}

// NewRecycleRecordPermissionClient 创建一个新的 RecycleRecordPermissionClient 实例
func NewRecycleRecordPermissionClient(db *DocvaultDB) *RecycleRecordPermissionClient {
	return &RecycleRecordPermissionClient{
		db: db.GetDB(),
	}
}

// FindByFileRecordIDs 根据文件记录ID列表查询回收记录权限
// 功能：查询指定文件记录ID对应的回收记录权限
// 参数：ctx - 上下文，fileRecordIDs - 文件记录ID列表
// 返回值：回收记录权限列表，错误信息
func (c *RecycleRecordPermissionClient) FindByFileRecordIDs(ctx context.Context, fileRecordIDs []string) ([]RecycleRecordPermission, error) {
	var permissions []RecycleRecordPermission
	err := c.db.WithContext(ctx).Where("file_record_id IN ?", fileRecordIDs).Find(&permissions).Error
	return permissions, err
}

// FindInternalElectronicPermissionsByFileRecordIDs 根据文件记录ID列表查询内发电子文件回收权限记录
// 功能: 查询指定文件记录ID列表对应的内发电子文件回收权限记录（只包含查询权限和查询/下载权限）
// 参数:
//   - ctx: 上下文
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 内发电子文件回收权限记录列表
//   - err: 错误信息
func (c *RecycleRecordPermissionClient) FindInternalElectronicPermissionsByFileRecordIDs(ctx context.Context, fileRecordIDs []string) ([]RecycleRecordPermission, error) {
	var permissions []RecycleRecordPermission
	if len(fileRecordIDs) == 0 {
		return permissions, nil
	}

	// 查询条件：
	// 1. file_record_id IN (fileRecordIDs)
	// 2. file_form = 1 (电子文件)
	// 3. file_permission IN (1, 2) (查询权限或查询/下载权限)
	err := c.db.WithContext(ctx).
		Where("file_record_id IN ?", fileRecordIDs).
		Where("file_form = ?", 1).                    // 电子文件
		Where("file_permission IN ?", []int32{1, 2}). // 查询权限(1)或查询/下载权限(2)
		Find(&permissions).Error

	return permissions, err
}
