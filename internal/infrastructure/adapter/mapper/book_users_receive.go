package mapper

import (
	"context"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"time"
)

const BookUsersReceiveTableName = "book_users_receive"

type BookUsersReceiveClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewBookUsersReceiveClient(db *DocvaultDB) *BookUsersReceiveClient {
	return &BookUsersReceiveClient{
		db: db.db,
	}
}

// BookUsersReceive
type BookUsersReceive struct {
	ID                   string         `gorm:"column:id;primary_key;NOT NULL;comment:'主键id'"`
	BookID               string         `gorm:"column:book_id;NOT NULL;comment:'书籍id'"`
	UserID               string         `gorm:"column:user_id;NOT NULL;comment:'领用人'"`
	Reason               string         `gorm:"column:reason;NOT NULL;comment:'领用原因'"`
	ReceiveStatus        int8           `gorm:"column:receive_status;NOT NULL;comment:'领用状态 1待提交 | 2待审批 | 3已审批 | 4已驳回 | 5已撤销'"`
	GiveBackStatus       int8           `gorm:"column:give_back_status;NOT NULL;comment:'归还状态 0无状态 | 1待提交 | 2待审批 | 3已审批 | 4已驳回 | 5已撤销'"`
	ReceiveDate          int64          `gorm:"column:receive_date;NOT NULL;comment:'领用时间'"`
	GiveBackDate         int64          `gorm:"column:give_back_date;NOT NULL;comment:'归还时间'"`
	UpdateAt             time.Time      `gorm:"column:update_at;default:(now ());NOT NULL;comment:'更新时间'"`
	ReceiveApprovalInfo  datatypes.JSON `gorm:"column:receive_approval_info;NOT NULL;comment:'领用审批信息'"`
	GiveBackApprovalInfo datatypes.JSON `gorm:"column:give_back_approval_info;NOT NULL;comment:'归还审批信息'"`
	WorkflowID           string         `gorm:"column:workflow_id;NOT NULL;comment:'工作流id'"`
}

// TableName 表名
func (b *BookUsersReceive) TableName() string {
	return BookUsersReceiveTableName
}

// GetByBookIDAndUserID 根据 bookID 和 userID 查询信息，查询待审批和已审批未归还的信息
func (b *BookUsersReceiveClient) GetByBookIDAndUserID(ctx context.Context, bookID, userID string) (BookUsersReceive, error) {
	var bookUsersReceive BookUsersReceive
	err := b.db.WithContext(ctx).
		Where("receive_status = 2").
		Or("receive_status = 3 and give_back_status != 3").
		Where("book_id = ? and user_id = ?", bookID, userID).Find(&bookUsersReceive).Error
	return bookUsersReceive, err
}
