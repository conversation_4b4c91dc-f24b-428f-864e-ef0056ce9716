package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

// User 用户实体
// 功能: 对应saas_user表的数据结构
type User struct {
	ID              string         `gorm:"column:id;primaryKey" json:"id"`                    // Snowflake ID | 全局唯一ID
	CreatedAt       *time.Time     `gorm:"column:created_at" json:"created_at"`               // Created Time | 创建时间
	UpdatedAt       *time.Time     `gorm:"column:updated_at" json:"updated_at"`               // Updated Time | 更新时间
	Status          bool           `gorm:"column:status;default:1" json:"status"`             // status true normal false ban | 状态  正常/禁用
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index" json:"deleted_at"`         // Deleted Time | 删除时间（软删除标识）
	Username        string         `gorm:"column:username;uniqueIndex" json:"username"`       // User's login name | 登录名
	Password        string         `gorm:"column:password" json:"-"`                          // Password | 密码Hash (不返回给前端)
	Nickname        string         `gorm:"column:nickname;default:''" json:"nickname"`        // Nickname | 昵称
	Mobile          *string        `gorm:"column:mobile" json:"mobile"`                       // Mobile number | 手机号
	Email           *string        `gorm:"column:email" json:"email"`                         // Email | 邮箱号
	Gender          string         `gorm:"column:gender;default:'未设置'" json:"gender"`         // gender | 性别
	Post            string         `gorm:"column:post;default:''" json:"post"`                // post | 职务
	IsSuperuser     bool           `gorm:"column:is_superuser;default:0" json:"is_superuser"` // Is Superuser | 是否超级管理员
	DefaultTenantID *string        `gorm:"column:default_tenant_id" json:"default_tenant_id"` // Default tenant id | 默认租户ID，用于快速登录
	AvatarID        string         `gorm:"column:avatar_id;default:''" json:"avatar_id"`      // Avatar FIle ID | 头像文件ID
	DeviceNo        string         `gorm:"column:device_no;default:''" json:"device_no"`      // Device No | 设备号
	Kind            string         `gorm:"column:kind;default:''" json:"kind"`                // kind | 类型，attendance：列席用户
	IMEI            *string        `gorm:"column:imei;uniqueIndex" json:"imei"`               // IMEI
}

// TableName 指定表名
// 功能: 返回用户表的表名
// 返回值:
//   - string: 表名
func (User) TableName() string {
	return "saas_user"
}

// UserClient 用户映射器客户端
// 功能: 提供用户相关的数据库操作方法
type UserClient struct {
	DB *PhoenixDB
}

// NewUserClient 创建用户客户端
// 功能: 初始化用户映射器客户端实例
// 参数:
//   - db: 数据库连接实例
//
// 返回值:
//   - *UserClient: 用户客户端实例
func NewUserClient(db *PhoenixDB) *UserClient {
	return &UserClient{
		DB: db,
	}
}

// BatchGetUserNicknames 批量根据用户ID查询用户昵称
// 功能: 根据用户ID列表批量查询用户昵称
// 参数:
//   - ctx: 上下文
//   - userIDs: 用户ID列表
//
// 返回值:
//   - map[string]string: 用户ID为key，用户昵称为value的映射
//   - error: 错误信息
func (c *UserClient) BatchGetUserNicknames(ctx context.Context, userIDs []string) (map[string]string, error) {
	if len(userIDs) == 0 {
		return make(map[string]string), nil
	}

	var users []struct {
		ID       string `gorm:"column:id"`
		Nickname string `gorm:"column:nickname"`
	}

	if err := c.DB.db.WithContext(ctx).
		Table("saas_user").
		Select("id, nickname").
		Where("id IN ?", userIDs).
		Find(&users).Error; err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string]string)
	for _, user := range users {
		result[user.ID] = user.Nickname
	}

	return result, nil
}

// GetUserIDsByNickname 根据昵称模糊查询用户ID列表
// 功能: 根据昵称进行模糊查询，获取匹配的用户ID列表
// 参数:
//   - ctx: 上下文
//   - nickname: 用户昵称（支持模糊匹配）
//
// 返回值:
//   - []string: 匹配的用户ID列表
//   - error: 错误信息
func (c *UserClient) GetUserIDsByNickname(ctx context.Context, nickname string) ([]string, error) {
	if nickname == "" {
		return []string{}, nil
	}

	var userIDs []string
	if err := c.DB.db.WithContext(ctx).
		Table("saas_user").
		Select("id").
		Where("nickname LIKE ?", "%"+nickname+"%").
		Find(&userIDs).Error; err != nil {
		return nil, err
	}

	return userIDs, nil
}
