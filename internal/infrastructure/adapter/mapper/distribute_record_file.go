package mapper

import (
	"context"

	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordFile = "distribute_record_files"
)

// DistributeRecordFile 对应 distribute_record_files 表
type DistributeRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联DistributeRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileName string `gorm:"type:varchar(255);comment:'文件名'"`
	Number   string `gorm:"type:varchar(255);comment:'文件编号'"`
	Version  string `gorm:"type:varchar(255);comment:'版本'"`
}

func (DistributeRecordFile) TableName() string {
	return TableNameDistributeRecordFile
}

// DistributeRecordFileClient 是 distribute_record_files 表的数据访问客户端
type DistributeRecordFileClient struct {
	db *gorm.DB
}

// NewDistributeRecordFileClient 创建一个新的 DistributeRecordFileClient 实例
func NewDistributeRecordFileClient(db *DocvaultDB) *DistributeRecordFileClient {
	return &DistributeRecordFileClient{
		db: db.GetDB(),
	}
}

// FindByFileIDAndApproved 根据文件ID查询审批完成的发放记录文件
// 功能: 查询指定文件ID对应的所有发放记录文件，只返回状态为待审批或已审批的记录
// 参数:
//   - fileID: 文件ID
//
// 返回值:
//   - files: 发放记录文件列表
//   - err: 错误信息
func (c *DistributeRecordFileClient) FindApprovedByFileID(ctx context.Context, fileID string) ([]DistributeRecordFile, error) {
	var files []DistributeRecordFile
	// 关联查询 distribute_records 表 3(已审批) 的记录
	err := c.db.WithContext(ctx).Table("distribute_record_files").
		Select("distribute_record_files.*").
		Joins("INNER JOIN distribute_records ON distribute_record_files.record_id = distribute_records.id").
		Where("distribute_record_files.file_id = ? AND distribute_records.status = ?", fileID, 3).
		Find(&files).Error
	return files, err
}

func (c *DistributeRecordFileClient) FindApprovByFileID(ctx context.Context, fileID string) ([]DistributeRecordFile, error) {
	var files []DistributeRecordFile
	// 关联查询 distribute_records 表 3(已审批) 的记录
	err := c.db.WithContext(ctx).Table("distribute_record_files").
		Select("distribute_record_files.*").
		Joins("INNER JOIN distribute_records ON distribute_record_files.record_id = distribute_records.id").
		Where("distribute_record_files.file_id = ? AND distribute_records.status IN (2, 3)", fileID).
		Find(&files).Error
	return files, err
}

// FindByID 根据ID查询发放记录文件
// 功能: 查询指定ID对应的发放记录文件
// 参数:
//   - ctx: 上下文
//   - id: 发放记录文件ID
//
// 返回值:
//   - file: 发放记录文件
//   - err: 错误信息
func (c *DistributeRecordFileClient) FindByID(ctx context.Context, id string) (DistributeRecordFile, error) {
	var file DistributeRecordFile
	err := c.db.WithContext(ctx).Where("id = ?", id).First(&file).Error
	return file, err
}

// FindByIDs 根据IDs查询发放记录文件
// 功能: 根据ID列表查询发放记录文件
// 参数:
//   - ctx: 上下文
//   - ids: 发放记录文件ID列表
//
// 返回值:
//   - files: 发放记录文件列表
//   - err: 错误信息
func (c *DistributeRecordFileClient) FindByIDs(ctx context.Context, ids []string) ([]DistributeRecordFile, error) {
	var files []DistributeRecordFile
	err := c.db.WithContext(ctx).Where("id in ?", ids).Find(&files).Error
	return files, err
}
