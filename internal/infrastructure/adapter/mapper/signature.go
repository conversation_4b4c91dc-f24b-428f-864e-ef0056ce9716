package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

type SignatureClient struct {
	DB *NebulaDB `name:"saas_db"`
}

func NewSignatureClient(db *NebulaDB) *SignatureClient {
	return &SignatureClient{
		DB: db,
	}
}

// InvalidateOldSignature 作废用户旧签名（status=0，updated_by/updated_at）
func (c *SignatureClient) UpdateStatusToInvalidByUserId(ctx context.Context, tx *gorm.DB, userId string, updatedBy string) error {
	return c.DB.db.WithContext(ctx).Model(&Signature{}).
		Where("user_id = ? and status = 1", userId).
		Updates(map[string]interface{}{
			"status":     false,
			"updated_by": updatedBy,
			"updated_at": time.Now(),
		}).Error
}

// Create 插入新签名（status=1）
func (c *SignatureClient) CreateWithCtx(ctx context.Context, tx *gorm.DB, sig Signature) error {
	return tx.WithContext(ctx).Create(&sig).Error
}

// GetLatestActiveSignature 查询当前用户生效签名（status=1，按created_at倒序取1条）
func (c *SignatureClient) GetLatestActiveSignature(ctx context.Context, userId string) (Signature, error) {
	var sig Signature
	err := c.DB.db.WithContext(ctx).
		Model(&Signature{}).
		Where("user_id = ? AND status = 1", userId).
		Order("created_at DESC").
		Find(&sig).Error
	if err != nil {
		return Signature{}, err
	}
	return sig, nil
}

// Page 查询用户历史签名（status=0，按updated_at倒序，分页）
type PageSignatureReq struct {
	UserID   string `json:"user_id"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	NoPage   bool   `json:"no_page"`
	Status   bool   `json:"status"`
	Search   string `json:"search"`
}

func (c *SignatureClient) Page(ctx context.Context, req PageSignatureReq) ([]Signature, int64, error) {
	var list []Signature
	var total int64
	db := c.DB.db.WithContext(ctx).
		Model(&Signature{}).
		Where("user_id = ? AND status = 0", req.UserID).Count(&total).Order("updated_at DESC,created_at DESC")

	if !req.NoPage {
		db = db.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize)
	}
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

type Signature struct {
	ID                 string    `gorm:"column:id;type:varchar(64);primaryKey"`
	UserID             string    `gorm:"column:user_id;type:varchar(64);not null"`
	ApproverID         string    `gorm:"column:approver_id;type:varchar(64);not null"`
	AuthLetterFileID   string    `gorm:"column:auth_letter_file_id;type:varchar(64);not null"`
	AuthLetterFileName string    `gorm:"column:auth_letter_file_name;type:varchar(255);not null"`
	SignatureBase64    string    `gorm:"column:signature_base64;type:text;not null"`
	Status             bool      `gorm:"column:status;type:tinyint(1);not null"`
	CreatedAt          time.Time `gorm:"column:created_at;type:timestamp;not null"`
	UpdatedAt          time.Time `gorm:"column:updated_at;type:timestamp;not null"`
	CreatedBy          string    `gorm:"column:created_by;type:varchar(64);not null"`
	UpdatedBy          string    `gorm:"column:updated_by;type:varchar(64);not null"`
}

func (s Signature) TableName() string {
	return "signature"
}
