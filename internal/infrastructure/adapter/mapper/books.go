package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const BooksTableName = "books"

type BooksClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewBooksClient(db *DocvaultDB) *BooksClient {
	return &BooksClient{
		db: db.db,
	}
}

// Books 书籍表
type Books struct {
	Id                 string    `gorm:"column:id;primary_key;NOT NULL;comment:'主键id'"`
	Status             int8      `gorm:"column:status;NOT NULL;comment:'状态（0未启用  1启用）'"`
	IsDelete           int8      `gorm:"column:is_delete;NOT NULL;comment:'是否删除（0未删除  1已删除）'"`
	Sort               int       `gorm:"column:sort;NOT NULL;comment:'序号'"`
	Number             string    `gorm:"column:number;NOT NULL;comment:'书籍编号'"`
	Name               string    `gorm:"column:name;NOT NULL;comment:'书籍名称'"`
	Author             string    `gorm:"column:author;NOT NULL;comment:'作者/编者'"`
	Publisher          string    `gorm:"column:publisher;NOT NULL;comment:'出版社'"`
	BookType           string    `gorm:"column:book_type;NOT NULL;comment:'书籍类型'"`
	RegisterCount      int32     `gorm:"column:register_count;NOT NULL;comment:'总在册数'"`
	ReceiveCount       int32     `gorm:"column:receive_count;NOT NULL;comment:'领用数'"`
	BorrowCount        int32     `gorm:"column:borrow_count;NOT NULL;comment:'借用数'"`
	OnBorrow           bool      `gorm:"column:on_borrow;NOT NULL;comment:'借用状态(false未借用  true借用中)'"`
	SurplusCount       int32     `gorm:"column:surplus_count;NOT NULL;comment:'剩余数'"`
	HiddenSurplusCount int32     `gorm:"column:hidden_surplus_count;NOT NULL;comment:'剩余数'"`
	CreatedTime        time.Time `gorm:"column:created_time;default:CURRENT_TIMESTAMP;NOT NULL;comment:'创建时间'"`
	UpdatedTime        time.Time `gorm:"column:updated_time;default:CURRENT_TIMESTAMP;NOT NULL;comment:'更新时间'"`
	CreatedBy          string    `gorm:"column:created_by;NOT NULL;comment:'创建人'"`
	UpdatedBy          string    `gorm:"column:updated_by;NOT NULL;comment:'修改人'"`
	DictionaryNodeID   string    `gorm:"column:dictionary_node_id;NOT NULL;comment:'类型id'"`
	FileID             string    `gorm:"column:file_id;NOT NULL;comment:'书籍文件id'"`
	OrganizationId     string    `gorm:"column:organization_id;NOT NULL;comment:'所属组织'"`
	Remark             string    `gorm:"column:remark;NOT NULL;comment:'备注'"`
}

// TableName 表名
func (b *Books) TableName() string {
	return BooksTableName
}

// GetHiddenSurplusCountById 通过ID查询隐式剩余数
func (b *BooksClient) GetHiddenSurplusCountByID(ctx context.Context, id string) (Books, error) {
	var book Books
	err := b.db.WithContext(ctx).Where("id = ?", id).Find(&book).Error
	return book, err
}
