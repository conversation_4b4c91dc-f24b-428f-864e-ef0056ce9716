package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	ViewNameAllDocuments = "all_documents_view"
)

// AllDocumentsViewClient 文档统一视图客户端
// 功能：提供对文档统一视图的查询操作，支持跨内部库和外部库的统一查询
type AllDocumentsViewClient struct {
	db *gorm.DB
}

// NewAllDocumentsViewClient 创建文档统一视图客户端
// 功能：初始化文档统一视图客户端实例
// 参数：db - 数据库连接实例
// 返回值：文档统一视图客户端实例
func NewAllDocumentsViewClient(db *DocvaultDB) *AllDocumentsViewClient {
	return &AllDocumentsViewClient{db: db.GetDB()}
}

// AllDocumentsView 文档统一视图模型
// 功能：统一内部文档库和外部文档库的数据结构
// 字段说明：
//   - DocumentID: 文档ID
//   - FileID: 文件ID
//   - DocumentName: 文档名称
//   - DocumentNo: 文档编号
//   - DocumentCategoryID: 文档类别ID
//   - DocumentVersionNo: 文档版本号
//   - DocumentModule: 文档模块类型 (2-内部文档库, 3-外部文档库)
//   - DocumentStatus: 文档状态(1-即将作废, 2-即将实施, 3-有效, 4-拟修订, -1-作废)
//   - OrganizationID: 归属组织ID
//   - TenantID: 租户ID
//   - CreatedBy: 创建人
//   - UpdatedBy: 更新人
//   - CreatedAt: 创建时间
//   - UpdatedAt: 更新时间
type AllDocumentsView struct {
	DocumentID         string    `gorm:"column:document_id"`          // 文档ID
	FileID             string    `gorm:"column:file_id"`              // 文件ID
	DocumentName       string    `gorm:"column:document_name"`        // 文档名称
	DocumentNo         string    `gorm:"column:document_no"`          // 文档编号
	DocumentCategoryID string    `gorm:"column:document_category_id"` // 文档类别ID
	DocumentVersionNo  string    `gorm:"column:document_version_no"`  // 文档版本号
	DocumentModule     int       `gorm:"column:document_module"`      // 文档模块类型 (2-内部文档库, 3-外部文档库)
	DocumentStatus     int8      `gorm:"column:document_status"`      // 文档状态(1-即将作废, 2-即将实施, 3-有效, 4-拟修订, -1-作废)
	OrganizationID     string    `gorm:"column:organization_id"`      // 归属组织ID
	TenantID           string    `gorm:"column:tenant_id"`            // 租户ID
	CreatedBy          string    `gorm:"column:created_by"`           // 创建人
	UpdatedBy          string    `gorm:"column:update_by"`            // 更新人
	CreatedAt          time.Time `gorm:"column:created_at"`           // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at"`           // 更新时间
}

// TableName 指定GORM模型对应的视图名
// 功能：告诉GORM使用all_documents_view视图
// 返回值：视图名称
func (AllDocumentsView) TableName() string {
	return ViewNameAllDocuments
}

// AllDocumentsQueryReq 文档查询请求参数
// 功能：定义查询文档统一视图的请求参数
type AllDocumentsQueryReq struct {
	OrganizationID      string   // 组织ID（必需）
	DocumentStatus      []int8   // 文档状态列表（可选）
	DocumentModule      []int    // 文档模块列表（可选，2-内部文档库, 3-外部文档库）
	DocumentCategoryID  string   // 文档类别ID（可选）
	DocumentCategoryIDs []string // 文档类别ID列表（可选）
	DocumentName        string   // 文档名称（模糊搜索，可选）
	DocumentNo          string   // 文档编号（模糊搜索，可选）
	Page                int      // 页码（可选，默认1）
	PageSize            int      // 每页大小（可选，默认10）
	NoPage              bool     // 是否不分页（可选，默认false）
}

// GetAllowBorrowDocuments 查询可借阅文档列表
// 功能：根据查询条件获取可借阅的文档列表，支持分页和多种筛选条件
// 参数：ctx - 上下文，req - 查询请求参数
// 返回值：文档列表，总数，错误信息
// 注意事项：
//   - 只查询有效状态（status=3）的文档
//   - 支持按组织、类别、名称、编号等条件筛选
//   - 支持分页查询
func (c *AllDocumentsViewClient) GetAllowBorrowDocuments(ctx context.Context, req AllDocumentsQueryReq) ([]AllDocumentsView, int64, error) {
	var documents []AllDocumentsView
	var total int64

	// 构建基础查询
	query := c.db.WithContext(ctx).Table(ViewNameAllDocuments).Where("organization_id = ?", req.OrganizationID)

	// 设置文档状态筛选条件
	if len(req.DocumentStatus) > 0 {
		query = query.Where("document_status IN ?", req.DocumentStatus)
	}

	// 设置文档模块筛选条件
	if len(req.DocumentModule) > 0 {
		query = query.Where("document_module IN ?", req.DocumentModule)
	}

	// 设置文档类别筛选条件
	if req.DocumentCategoryID != "" {
		query = query.Where("document_category_id = ?", req.DocumentCategoryID)
	}

	if len(req.DocumentCategoryIDs) > 0 {
		query = query.Where("document_category_id IN ?", req.DocumentCategoryIDs)
	}

	// 设置文档名称模糊搜索条件
	if req.DocumentName != "" {
		query = query.Where("document_name LIKE ?", "%"+req.DocumentName+"%")
	}

	// 设置文档编号模糊搜索条件（需要根据实际表结构调整）
	if req.DocumentNo != "" {
		// 注意：视图中没有统一的document_no字段，这里需要根据实际需求调整
		// 可能需要在视图中添加document_no字段或者使用其他字段
		query = query.Where("document_no LIKE ?", "%"+req.DocumentNo+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 如果不分页，直接查询所有数据
	if req.NoPage {
		if err := query.Find(&documents).Error; err != nil {
			return nil, 0, err
		}
		return documents, total, nil
	}

	// 分页查询
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&documents).Error; err != nil {
		return nil, 0, err
	}

	return documents, total, nil
}

// GetDocumentByID 根据文档ID查询文档详情
// 功能：根据文档ID获取文档的详细信息
// 参数：ctx - 上下文，documentID - 文档ID
// 返回值：文档信息，错误信息
func (c *AllDocumentsViewClient) GetDocumentByID(ctx context.Context, documentID string) (*AllDocumentsView, error) {
	var document AllDocumentsView
	if err := c.db.WithContext(ctx).Table(ViewNameAllDocuments).Where("document_id = ?", documentID).First(&document).Error; err != nil {
		return nil, err
	}
	return &document, nil
}

// GetDocumentsByIDs 根据文档ID列表批量查询文档
// 功能：根据文档ID列表批量获取文档信息
// 参数：ctx - 上下文，documentIDs - 文档ID列表
// 返回值：文档列表，错误信息
func (c *AllDocumentsViewClient) GetDocumentsByIDs(ctx context.Context, documentIDs []string) ([]AllDocumentsView, error) {
	var documents []AllDocumentsView
	if len(documentIDs) == 0 {
		return documents, nil
	}

	if err := c.db.WithContext(ctx).Table(ViewNameAllDocuments).Where("document_id IN ?", documentIDs).Find(&documents).Error; err != nil {
		return nil, err
	}
	return documents, nil
}

// CountDocumentsByStatus 统计各状态文档数量
// 功能：按文档状态和模块类型统计文档数量
// 参数：ctx - 上下文，organizationID - 组织ID
// 返回值：统计结果，错误信息
func (c *AllDocumentsViewClient) CountDocumentsByStatus(ctx context.Context, organizationID string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	if err := c.db.WithContext(ctx).Table(ViewNameAllDocuments).
		Select("document_status, document_module, COUNT(*) as doc_count").
		Where("organization_id = ?", organizationID).
		Group("document_status, document_module").
		Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}
