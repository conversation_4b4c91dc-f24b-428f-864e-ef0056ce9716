package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

type NebulaTXGenerator struct {
	db *gorm.DB `name:"saas_db"`
}

func NewNebulaTXGenerator(db *NebulaDB) *NebulaTXGenerator {
	return &NebulaTXGenerator{
		db: db.db,
	}
}

func (p *NebulaTXGenerator) CreateTX(ctx context.Context) *NebulaTX {
	return NewNebulaTX(p.db.Begin(), ctx)
}

type NebulaTX struct {
	ctx context.Context
	tx  *gorm.DB
}

func NewNebulaTX(tx *gorm.DB, ctx context.Context) *NebulaTX {
	return &NebulaTX{
		tx: tx,
	}
}

func (p *NebulaTX) GetTX() *gorm.DB {
	return p.tx
}

func (p *NebulaTX) Commit() error {
	return p.tx.Commit().Error
}

func (p *NebulaTX) AutoCommit(err *error) {
	if r := recover(); r != nil {
		p.Rollback()
		return
	}
	if *err != nil {
		err2 := p.Rollback()
		if err2 != nil {
			logc.Error(p.ctx, err2)
		}
		return
	}

	err2 := p.Commit()
	if err2 != nil {
		logc.Error(p.ctx, err2)
	}
}

func (p *NebulaTX) Rollback() error {
	return p.tx.Rollback().Error
}
