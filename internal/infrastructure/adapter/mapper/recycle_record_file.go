package mapper

import (
	"context"

	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordFile = "recycle_record_files"
)

// RecycleRecordFile 对应 recycle_record_files 表
type RecycleRecordFile struct {
	ID                     string `gorm:"type:varchar(64);primary_key"`
	RecordID               string `gorm:"type:varchar(64);index;comment:'关联RecycleRecord的ID'"`
	DistributeRecordFileID string `gorm:"type:varchar(64);comment:'清单文件id'"`
}

func (RecycleRecordFile) TableName() string {
	return TableNameRecycleRecordFile
}

// RecycleRecordFileClient 是 recycle_record_files 表的数据访问客户端
type RecycleRecordFileClient struct {
	db *gorm.DB
}

// NewRecycleRecordFileClient 创建一个新的 RecycleRecordFileClient 实例
func NewRecycleRecordFileClient(db *DocvaultDB) *RecycleRecordFileClient {
	return &RecycleRecordFileClient{
		db: db.GetDB(),
	}
}

// FindByDistributeRecordFileIDs 根据发放记录文件ID列表查询回收记录文件关联
// 功能：查询指定发放记录文件ID对应的回收记录文件关联
// 参数：ctx - 上下文，distributeRecordFileIDs - 发放记录文件ID列表
// 返回值：回收记录文件关联列表，错误信息
func (c *RecycleRecordFileClient) FindByDistributeRecordFileIDs(ctx context.Context, distributeRecordFileIDs []string) ([]RecycleRecordFile, error) {
	var recycleRecordFiles []RecycleRecordFile
	err := c.db.WithContext(ctx).Where("distribute_record_file_id IN ?", distributeRecordFileIDs).Find(&recycleRecordFiles).Error
	return recycleRecordFiles, err
}
