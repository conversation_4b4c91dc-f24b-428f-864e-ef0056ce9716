package mapper

import (
	"gorm.io/gorm"
)

const TableNameSaasOrganization = "saas_organization"

type SaasOrganizationClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewSaasOrganizationClient(db *PhoenixDB) *SaasOrganizationClient {
	return &SaasOrganizationClient{
		DB: db.GetDB(),
	}
}

// SaasOrganization 组织信息表
type SaasOrganization struct {
	Id        string `gorm:"column:id;primary_key;NOT NULL;comment:'Snowflake ID  全局唯一ID'"`
	CreatedAt string `gorm:"column:created_at;comment:'Created Time  创建时间'"`
	UpdatedAt string `gorm:"column:updated_at;comment:'Updated Time  更新时间'"`
	Status    int8   `gorm:"column:status;default:1;comment:'status true normal false ban  状态  正常/禁用'"`
	Sort      uint32 `gorm:"column:sort;default:1;NOT NULL;comment:'Sort number  排序编号'"`
	DeletedAt string `gorm:"column:deleted_at;comment:'Deleted Time  删除时间（软删除标识）'"`
	Name      string `gorm:"column:name;NOT NULL;comment:'Organization name  部门名称'"`
	Ancestors string `gorm:"column:ancestors;NOT NULL;comment:'Parents'"`
	Code      string `gorm:"column:code;NOT NULL;comment:'Code  组织架构节点编码（可作为扩展其他用户体系，比如钉钉，企业微信等）'"`
	NodeType  uint32 `gorm:"column:node_type;NOT NULL;comment:'Node type  组织架构类型 0 集团 1 公司 2 部门'"`
	Leader    string `gorm:"column:leader;NOT NULL;comment:'Organization leader  领导'"`
	Phone     string `gorm:"column:phone;NOT NULL;comment:'Leader'"`
	Email     string `gorm:"column:email;NOT NULL;comment:'Leader'"`
	Remark    string `gorm:"column:remark;NOT NULL;comment:'Remark  备注'"`
	TenantId  string `gorm:"column:tenant_id;NOT NULL;comment:'Tenant ID'"`
	ParentId  string `gorm:"column:parent_id;comment:'Parent organization ID  父级部门ID'"`
}

// TableName 表名
func (s *SaasOrganization) TableName() string {
	return TableNameSaasOrganization
}
