package mapper

import (
	"context"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"time"
)

const BookUsersBorrowTableName = "book_users_borrow"

type BookUsersBorrowClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewBookUsersBorrowClient(db *DocvaultDB) *BookUsersBorrowClient {
	return &BookUsersBorrowClient{
		db: db.db,
	}
}

// BookUsersBorrow
type BookUsersBorrow struct {
	ID                   string         `gorm:"column:id;primary_key;NOT NULL;comment:'主键id'"`
	BookID               string         `gorm:"column:book_id;NOT NULL;comment:'书籍id'"`
	UserID               string         `gorm:"column:user_id;NOT NULL;comment:'借用人'"`
	Reason               string         `gorm:"column:reason;NOT NULL;comment:'借用原因'"`
	BorrowStatus         int8           `gorm:"column:borrow_status;NOT NULL;comment:'借用状态 1待提交 | 2待审批 | 3已审批 | 4已驳回 | 5已撤销'"`
	GiveBackStatus       int8           `gorm:"column:give_back_status;NOT NULL;comment:'归还状态 0无状态 | 1待提交 | 2待审批 | 3已审批 | 4已驳回 | 5已撤销'"`
	BorrowDate           int64          `gorm:"column:borrow_date;NOT NULL;comment:'借用时间'"`
	GiveBackDate         int64          `gorm:"column:give_back_date;NOT NULL;comment:'归还时间'"`
	UpdateAt             time.Time      `gorm:"column:update_at;NOT NULL ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	BorrowApprovalInfo   datatypes.JSON `gorm:"column:borrow_approval_info;NOT NULL;comment:'借用审批信息'"`
	GiveBackApprovalInfo datatypes.JSON `gorm:"column:give_back_approval_info;NOT NULL;comment:'归还审批信息'"`
	WorkflowID           string         `gorm:"column:workflow_id;NOT NULL;comment:'流程id'"`
}

// TableName 表名
func (b *BookUsersBorrow) TableName() string {
	return BookUsersBorrowTableName
}

// GetByBookIDAndUserID 根据 bookID 和 userID 查询信息，查询待审批和已审批未归还的信息
func (b *BookUsersBorrowClient) GetByBookIDAndUserID(ctx context.Context, bookID, userID string) (BookUsersBorrow, error) {
	var bookUsersBorrow BookUsersBorrow
	err := b.db.WithContext(ctx).
		Where("borrow_status = 2").
		Or("borrow_status = 3 and give_back_status != 3").
		Where("book_id = ? and user_id = ?", bookID, userID).Find(&bookUsersBorrow).Error
	return bookUsersBorrow, err
}
