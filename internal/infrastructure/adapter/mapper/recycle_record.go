package mapper

import (
	"context"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecord = "recycle_records"
)

// RecycleRecord 对应 recycle_records 表
// 注意: 一条回收记录可以对应多条发放记录，通过 recycle_record_files 表关联
type RecycleRecord struct {
	ID            string         `gorm:"type:varchar(64);primary_key"`
	RecycleBy     string         `gorm:"type:varchar(64);comment:'回收人,用户id'"`
	RecycleDate   time.Time      `gorm:"column:recycle_date;comment:'回收日期'"`
	RecycleReason string         `gorm:"type:text;comment:'回收原因'"`
	OtherReason   string         `gorm:"type:text;comment:'其他原因'"`
	WorkflowID    string         `gorm:"type:varchar(64);comment:'流程ID'"`
	ApprovalInfo  datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`
	CreatedAt     time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt     time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy     string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy     string         `gorm:"type:varchar(64);column:updated_by"`
}

func (RecycleRecord) TableName() string {
	return TableNameRecycleRecord
}

// RecycleRecordClient 是 recycle_records 表的数据访问客户端
type RecycleRecordClient struct {
	db *gorm.DB
}

// NewRecycleRecordClient 创建一个新的 RecycleRecordClient 实例
func NewRecycleRecordClient(db *DocvaultDB) *RecycleRecordClient {
	return &RecycleRecordClient{
		db: db.GetDB(),
	}
}

// FindByIDs 根据ID列表查询回收记录
// 功能：查询指定ID列表对应的回收记录
// 参数：ctx - 上下文，ids - 回收记录ID列表
// 返回值：回收记录列表，错误信息
func (c *RecycleRecordClient) FindByIDs(ctx context.Context, ids []string) ([]RecycleRecord, error) {
	var recycleRecords []RecycleRecord
	err := c.db.WithContext(ctx).Where("id IN ?", ids).Find(&recycleRecords).Error
	return recycleRecords, err
}
