package rpcinterceptor

import (
	"context"
	"nebula/internal/utils"
	"strconv"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	SessionUserIdKey         = "x-session-user-id"
	SessionTenantIdKey       = "x-session-tenant-id"
	SessionDeviceKindKey     = "x-session-device-kind"
	SessionIsVirtualUserKey  = "x-session-is-virtual-user"
	SessionTraceIdKey        = "x-trace-id"
	SessionIsAdminKey        = "x-session-is-admin"
	SessionOrganizationIdKey = "x-session-organization-id"
)

func SessionUnaryClientInterceptor(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		md = make(metadata.MD)
	}

	userInfo := utils.GetCurrentLoginUser(ctx)
	md.Set(SessionUserIdKey, userInfo.UserId)
	md.Set(SessionTenantIdKey, userInfo.TenantId)
	md.Set(SessionDeviceKindKey, strconv.Itoa(userInfo.DeviceKind))
	md.Set(SessionIsVirtualUserKey, strconv.FormatBool(userInfo.IsVirtualUser))
	md.Set(SessionIsAdminKey, strconv.FormatBool(userInfo.IsAdmin))
	md.Set(SessionTraceIdKey, utils.GetContextTraceId(ctx))
	md.Set(SessionOrganizationIdKey, utils.GetContextOrganizationID(ctx))
	ctx = metadata.NewOutgoingContext(ctx, md)

	return invoker(ctx, method, req, reply, cc, opts...)
}
