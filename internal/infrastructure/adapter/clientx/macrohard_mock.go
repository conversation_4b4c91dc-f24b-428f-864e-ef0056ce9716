// Code generated by MockGen. DO NOT EDIT.
// Source: macrohard.go

// Package clientx is a generated GoMock package.
package clientx

import (
	context "context"
	entity "nebula/internal/infrastructure/adapter/clientx/entity"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockMacrohardClient is a mock of MacrohardClient interface.
type MockMacrohardClient struct {
	ctrl     *gomock.Controller
	recorder *MockMacrohardClientMockRecorder
}

// MockMacrohardClientMockRecorder is the mock recorder for MockMacrohardClient.
type MockMacrohardClientMockRecorder struct {
	mock *MockMacrohardClient
}

// NewMockMacrohardClient creates a new mock instance.
func NewMockMacrohardClient(ctrl *gomock.Controller) *MockMacrohardClient {
	mock := &MockMacrohardClient{ctrl: ctrl}
	mock.recorder = &MockMacrohardClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMacrohardClient) EXPECT() *MockMacrohardClientMockRecorder {
	return m.recorder
}

// DocxContentFilling mocks base method.
func (m *MockMacrohardClient) DocxContentFilling(ctx context.Context, req *entity.DocxContentFillingRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DocxContentFilling", ctx, req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocxContentFilling indicates an expected call of DocxContentFilling.
func (mr *MockMacrohardClientMockRecorder) DocxContentFilling(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocxContentFilling", reflect.TypeOf((*MockMacrohardClient)(nil).DocxContentFilling), ctx, req)
}

// DocxConvertToPdf mocks base method.
func (m *MockMacrohardClient) DocxConvertToPdf(ctx context.Context, docxFileID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DocxConvertToPdf", ctx, docxFileID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocxConvertToPdf indicates an expected call of DocxConvertToPdf.
func (mr *MockMacrohardClientMockRecorder) DocxConvertToPdf(ctx, docxFileID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocxConvertToPdf", reflect.TypeOf((*MockMacrohardClient)(nil).DocxConvertToPdf), ctx, docxFileID)
}
