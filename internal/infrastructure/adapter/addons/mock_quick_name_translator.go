// Code generated by MockGen. DO NOT EDIT.
// Source: quick_name_translator.go

// Package addons is a generated GoMock package.
package addons

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockQuickNameTranslator is a mock of QuickNameTranslator interface.
type MockQuickNameTranslator struct {
	ctrl     *gomock.Controller
	recorder *MockQuickNameTranslatorMockRecorder
}

// MockQuickNameTranslatorMockRecorder is the mock recorder for MockQuickNameTranslator.
type MockQuickNameTranslatorMockRecorder struct {
	mock *MockQuickNameTranslator
}

// NewMockQuickNameTranslator creates a new mock instance.
func NewMockQuickNameTranslator(ctrl *gomock.Controller) *MockQuickNameTranslator {
	mock := &MockQuickNameTranslator{ctrl: ctrl}
	mock.recorder = &MockQuickNameTranslatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuickNameTranslator) EXPECT() *MockQuickNameTranslatorMockRecorder {
	return m.recorder
}

// TranslateFileName mocks base method.
func (m *MockQuickNameTranslator) TranslateFileName(ctx context.Context, fileID string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateFileName", ctx, fileID)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateFileName indicates an expected call of TranslateFileName.
func (mr *MockQuickNameTranslatorMockRecorder) TranslateFileName(ctx, fileID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateFileName", reflect.TypeOf((*MockQuickNameTranslator)(nil).TranslateFileName), ctx, fileID)
}

// TranslateOrganizationName mocks base method.
func (m *MockQuickNameTranslator) TranslateOrganizationName(ctx context.Context, organizationID string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateOrganizationName", ctx, organizationID)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateOrganizationName indicates an expected call of TranslateOrganizationName.
func (mr *MockQuickNameTranslatorMockRecorder) TranslateOrganizationName(ctx, organizationID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateOrganizationName", reflect.TypeOf((*MockQuickNameTranslator)(nil).TranslateOrganizationName), ctx, organizationID)
}

// TranslateUserNickname mocks base method.
func (m *MockQuickNameTranslator) TranslateUserNickname(ctx context.Context, userID string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateUserNickname", ctx, userID)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateUserNickname indicates an expected call of TranslateUserNickname.
func (mr *MockQuickNameTranslatorMockRecorder) TranslateUserNickname(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateUserNickname", reflect.TypeOf((*MockQuickNameTranslator)(nil).TranslateUserNickname), ctx, userID)
}
