package addons

import (
	"context"
	"nebula/internal/config"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

//go:generate mockgen -source=redis_addons.go -destination=mock_redis_addons.go -package=addons

// RedisAddons 定义RedisAddons操作接口
type RedisAddons interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value string) error
	SetEx(ctx context.Context, key string, value string, seconds int) error
	Del(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	GetRawClient() *redis.Redis
}

// RedisAddonsImpl Redis客户端实现
type RedisAddonsImpl struct {
	client *redis.Redis
}

// NewRedisAddonsImpl 创建新的Redis客户端
func NewRedisAddonsImpl(c config.Config) RedisAddons {
	conf := redis.RedisConf{
		Host: c.Redis.Host,
		Type: c.Redis.Type,
		Pass: c.Redis.Pass,
		Tls:  false,
	}

	rds := redis.MustNewRedis(conf)
	if rds == nil {
		panic("redis init failed")
	}
	return &RedisAddonsImpl{
		client: rds,
	}
}

// Get 获取键值
func (r *RedisAddonsImpl) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(key)
}

// Set 设置键值
func (r *RedisAddonsImpl) Set(ctx context.Context, key string, value string) error {
	return r.client.Set(key, value)
}

// SetEx 设置键值带过期时间
func (r *RedisAddonsImpl) SetEx(ctx context.Context, key string, value string, seconds int) error {
	return r.client.Setex(key, value, seconds)
}

// Del 删除键
func (r *RedisAddonsImpl) Del(ctx context.Context, key string) error {
	_, err := r.client.Del(key)
	return err
}

// Exists 检查键是否存在
func (r *RedisAddonsImpl) Exists(ctx context.Context, key string) (bool, error) {
	return r.client.Exists(key)
}

// GetRawClient 获取原始的go-zero redis客户端
func (r *RedisAddonsImpl) GetRawClient() *redis.Redis {
	return r.client
}
