package referenceimpl

import (
	"context"
	"encoding/json"
	"nebula/internal/config"
	"nebula/internal/domain/aggregate"
	"nebula/internal/domain/entity"
	"nebula/internal/domain/value"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/clientx"
	centity "nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"
	"time"

	"gorm.io/gorm"
)

const (
	SignatureTaskPrefix = "signature_task:"
	SignatureTaskTTL    = 24 * time.Hour
)

type SignatureTaskInfo struct {
	TaskId          string `json:"taskId"`
	SignatureBase64 string `json:"signatureBase64,omitempty"` // 签名图片Base64字符串
}

type signatureReferenceImpl struct {
	rdsAddons       addons.RedisAddons
	macrohardClient clientx.MacrohardClient
	config          config.Config
	db              *mapper.NebulaDB
}

// NewSignatureService creates a new SignatureService implementation.
func NewSignatureReferenceImpl(rdsAddons addons.RedisAddons, macrohardClient clientx.MacrohardClient, config config.Config, db *mapper.NebulaDB) aggregate.SignatureReference {
	return &signatureReferenceImpl{
		rdsAddons:       rdsAddons,
		macrohardClient: macrohardClient,
		config:          config,
		db:              db,
	}
}

func (s *signatureReferenceImpl) CreateSignatureTask(ctx context.Context, taskId string) (err error) {
	task := &SignatureTaskInfo{
		TaskId:          taskId,
		SignatureBase64: "",
	}
	taskJson, _ := json.Marshal(task)
	err = s.rdsAddons.SetEx(ctx, SignatureTaskPrefix+taskId, string(taskJson), int(SignatureTaskTTL.Seconds()))
	if err != nil {
		return err
	}
	return nil
}

func (s *signatureReferenceImpl) UpdateSignatureTask(ctx context.Context, taskId string, signatureBase64 string) (err error) {
	task := &SignatureTaskInfo{
		TaskId:          taskId,
		SignatureBase64: signatureBase64,
	}
	taskJson, _ := json.Marshal(task)
	err = s.rdsAddons.SetEx(ctx, SignatureTaskPrefix+taskId, string(taskJson), int(SignatureTaskTTL.Seconds()))
	if err != nil {
		return err
	}
	return nil
}

func (s *signatureReferenceImpl) GetSignatureTask(ctx context.Context, taskId string) (signatureTask value.SignatureTask, err error) {
	taskJson, err := s.rdsAddons.Get(ctx, SignatureTaskPrefix+taskId)
	if err != nil {
		return value.SignatureTask{}, err
	}
	task := &SignatureTaskInfo{}
	err = json.Unmarshal([]byte(taskJson), task)
	if err != nil {
		return value.SignatureTask{}, err
	}
	return value.SignatureTask{
		TaskId:          task.TaskId,
		SignatureBase64: task.SignatureBase64,
	}, nil
}

func (s *signatureReferenceImpl) GenerateAuthLetterFile(ctx context.Context, authLetterInfo value.AuthLetterInfo) (fileId string, err error) {
	fileID := s.config.Business.SignatureAuthLetterFileID
	ps, err := utils.ArrayCopy(ctx, centity.DocxContentFillingFieldParam{}, authLetterInfo.AuthLetterParams)
	if err != nil {
		return "", err
	}
	fileId, err = s.macrohardClient.DocxContentFilling(ctx, &centity.DocxContentFillingRequest{
		FileID:      fileID,
		NewFileName: authLetterInfo.AuthLetterFileName + ".docx",
		Params:      ps,
	})
	if err != nil {
		return "", err
	}

	// 转换为 PDF
	pdfFileId, err := s.macrohardClient.DocxConvertToPdf(ctx, fileId)
	if err != nil {
		return "", err
	}

	return pdfFileId, nil
}

func (s *signatureReferenceImpl) SaveSignature(ctx context.Context, entity entity.Signature) (err error) {
	s.db.GetDB().Transaction(func(tx *gorm.DB) error {
		if err := mapper.NewSignatureClient(s.db).UpdateStatusToInvalidByUserId(ctx, tx, entity.UserID, entity.UserID); err != nil {
			return err
		}
		if err := mapper.NewSignatureClient(s.db).CreateWithCtx(ctx, tx, mapper.Signature{
			ID:                 entity.ID,
			UserID:             entity.UserID,
			Status:             entity.Status,
			CreatedAt:          time.UnixMilli(entity.CreatedAt),
			UpdatedAt:          time.UnixMilli(entity.UpdatedAt),
			CreatedBy:          entity.CreatedBy,
			UpdatedBy:          entity.UpdatedBy,
			SignatureBase64:    entity.SignatureBase64,
			ApproverID:         entity.LastApproverID,
			AuthLetterFileID:   entity.AuthLetterFileID,
			AuthLetterFileName: entity.AuthLetterFileName,
		}); err != nil {
			return err
		}

		return nil
	})
	return nil
}
