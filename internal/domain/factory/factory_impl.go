package factory

import (
	"nebula/internal/config"
	"nebula/internal/domain/aggregate"
)

type Impl struct {
	c                     config.Config
	basicAbilityReference aggregate.BasicAbilityReference
	userDemoReference     aggregate.UserReferenceDemo
	signatureReference    aggregate.SignatureReference
}

func NewImpl(c config.Config, basicAbilityReference aggregate.BasicAbilityReference, userReferenceDemo aggregate.UserReferenceDemo, signatureReference aggregate.SignatureReference) Factory {
	return &Impl{c: c, basicAbilityReference: basicAbilityReference, userDemoReference: userReferenceDemo, signatureReference: signatureReference}
}

func (i *Impl) NewUserDemo() aggregate.UserServiceDemo {
	return aggregate.NewUserDemo(i.userDemoReference)
}

func (i *Impl) SignatureService() aggregate.SignatureService {
	return aggregate.NewSignature(i.basicAbilityReference, i.signatureReference)
}
