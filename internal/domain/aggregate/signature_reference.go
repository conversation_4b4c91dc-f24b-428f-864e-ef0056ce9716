package aggregate

import (
	"context"
	"nebula/internal/domain/entity"
	"nebula/internal/domain/value"
)

type SignatureReference interface {
	CreateSignatureTask(ctx context.Context, taskId string) (err error)

	GetSignatureTask(ctx context.Context, taskId string) (signatureTask value.SignatureTask, err error)

	UpdateSignatureTask(ctx context.Context, taskId string, signatureBase64 string) (err error)

	SaveSignature(ctx context.Context, entity entity.Signature) (err error)
	// 生成授权书
	GenerateAuthLetterFile(ctx context.Context, authLetterInfo value.AuthLetterInfo) (fileId string, err error)
}
