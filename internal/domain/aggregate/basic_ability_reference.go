package aggregate

import (
	"context"
	"nebula/internal/domain/entity"
	"nebula/internal/domain/value"
)

type BasicAbilityReference interface {
	// 生成ID
	GenerateID() string

	// 翻译名称
	TranslateUserNickname(ctx context.Context, userId string) string

	// 翻译组织名称
	TranslateOrganizationName(ctx context.Context, organizationId string) string

	// 获取文件信息
	GetFileInfo(ctx context.Context, fileId string) (entity.File, error)

	// 获取审批流信息
	GetWorkflowInfo(ctx context.Context, workflowId string) (workflowInfo value.WorkflowInfo, err error)
}
