package aggregate

import (
	"context"
)

// SignatureService defines the interface for signature-related business logic.
type SignatureService interface {
	// 创建签名任务
	CreateSignatureTask(ctx context.Context) (taskId string, err error)
	// 上传签名
	UploadSignature(ctx context.Context, taskId string, signatureBase64 string) (err error)

	// 审批完成
	ApprovalCompleted(ctx context.Context, msg ApprovalResultMsg) (err error)
}

type ApprovalResultMsg struct {
	TenantID       string
	OrganizationID string
	WorkflowID     string
	SponsorID      string
	FormContent    string
	CompletedAt    int64
	CreatedAt      int64
	BusinessID     string
	BusinessCode   string
}
