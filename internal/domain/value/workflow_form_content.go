package value

type WorkflowFormContent struct {
	BusinessId string      `json:"businessId"`
	Version    string      `json:"version"`
	Data       interface{} `json:"data"`
}

type SignatureWorkflowFormContent struct {
	WorkflowFormContent
	Data struct {
		Nickname        string `json:"nickname"`
		SignatureBase64 string `json:"signatureBase64"`
		Reason          string `json:"reason"`
	} `json:"data"`
}
