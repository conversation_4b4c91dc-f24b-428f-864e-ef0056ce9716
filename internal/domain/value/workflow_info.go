package value

type WorkflowInfo struct {
	FlowName                string             `json:"flowName"`
	FlowID                  string             `json:"flowId"`
	FlowStatus              string             `json:"flowStatus"`
	FlowCreatedUserNickname string             `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64              `json:"flowCreatedTime"`
	FormContent             string             `json:"formContent"`
	Nodes                   []WorkflowInfoNode `json:"nodes"`
}

type WorkflowInfoNode struct {
	NodeName    string                     `json:"nodeName"`
	NodeID      string                     `json:"nodeId"`
	Status      string                     `json:"status"`
	SigningKind string                     `json:"signingKind"`
	Approvers   []WorkflowInfoNodeApprover `json:"approvers"`
}

type WorkflowInfoNodeApprover struct {
	TaskID           string `json:"taskId"`
	ApproverID       string `json:"approverId"`
	ApproverNickname string `json:"approverNickname"`
	Status           string `json:"status"`
	UpdatedAt        int64  `json:"updatedAt"`
	Comment          string `json:"comment"`
}
