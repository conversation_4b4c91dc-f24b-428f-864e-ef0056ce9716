package filters

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
)

// FilterFunc 过滤器函数类型
// 功能：定义过滤器函数的签名
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
type FilterFunc func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error)

// InternalDocumentFilterFunc 内部文档过滤器函数类型
// 功能：定义内部文档过滤器函数的签名
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
type InternalDocumentFilterFunc func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error)

// ExternalDocumentFilterFunc 外部文档过滤器函数类型
// 功能：定义外部文档过滤器函数的签名
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
type ExternalDocumentFilterFunc func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error)

// FilterPipeline 过滤器管道
// 功能：使用函数式编程组织多个过滤器，替代传统的责任链模式
type FilterPipeline struct {
	filters                  []FilterFunc
	internalDocumentFilters  []InternalDocumentFilterFunc
	externalDocumentFilters  []ExternalDocumentFilterFunc
}

// NewFilterPipeline 创建过滤器管道
// 功能：创建过滤器管道实例
// 返回值：过滤器管道实例
func NewFilterPipeline() *FilterPipeline {
	return &FilterPipeline{
		filters:                 make([]FilterFunc, 0),
		internalDocumentFilters: make([]InternalDocumentFilterFunc, 0),
		externalDocumentFilters: make([]ExternalDocumentFilterFunc, 0),
	}
}

// Add 添加过滤器函数
// 功能：向管道中添加一个过滤器函数
// 参数：filter - 过滤器函数
// 返回值：过滤器管道实例（支持链式调用）
func (p *FilterPipeline) Add(filter FilterFunc) *FilterPipeline {
	p.filters = append(p.filters, filter)
	return p
}

// AddInternalDocument 添加内部文档过滤器函数
// 功能：向管道中添加一个内部文档过滤器函数
// 参数：filter - 内部文档过滤器函数
// 返回值：过滤器管道实例（支持链式调用）
func (p *FilterPipeline) AddInternalDocument(filter InternalDocumentFilterFunc) *FilterPipeline {
	p.internalDocumentFilters = append(p.internalDocumentFilters, filter)
	return p
}

// AddExternalDocument 添加外部文档过滤器函数
// 功能：向管道中添加一个外部文档过滤器函数
// 参数：filter - 外部文档过滤器函数
// 返回值：过滤器管道实例（支持链式调用）
func (p *FilterPipeline) AddExternalDocument(filter ExternalDocumentFilterFunc) *FilterPipeline {
	p.externalDocumentFilters = append(p.externalDocumentFilters, filter)
	return p
}

// Execute 执行所有过滤器
// 功能：按顺序执行所有过滤器函数，如果任何一个过滤器返回空结果，则停止执行
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (p *FilterPipeline) Execute(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
	// 实现步骤：
	// 1. 遍历所有过滤器函数
	// 2. 依次执行每个过滤器函数
	// 3. 如果任何过滤器返回错误或空结果，立即返回
	// 4. 所有过滤器都成功执行后返回成功

	for _, filter := range p.filters {
		isEmpty, err := filter(ctx, req, pageReq)
		if err != nil {
			return false, err
		}
		if isEmpty {
			return true, nil
		}
	}

	return false, nil
}

// ExecuteInternalDocument 执行所有内部文档过滤器
// 功能：按顺序执行所有内部文档过滤器函数，如果任何一个过滤器返回空结果，则停止执行
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (p *FilterPipeline) ExecuteInternalDocument(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
	for _, filter := range p.internalDocumentFilters {
		isEmpty, err := filter(ctx, req, pageReq)
		if err != nil {
			return false, err
		}
		if isEmpty {
			return true, nil
		}
	}

	return false, nil
}

// ExecuteExternalDocument 执行所有外部文档过滤器
// 功能：按顺序执行所有外部文档过滤器函数，如果任何一个过滤器返回空结果，则停止执行
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (p *FilterPipeline) ExecuteExternalDocument(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
	for _, filter := range p.externalDocumentFilters {
		isEmpty, err := filter(ctx, req, pageReq)
		if err != nil {
			return false, err
		}
		if isEmpty {
			return true, nil
		}
	}

	return false, nil
}

// Size 获取过滤器数量
// 功能：返回管道中过滤器的数量
// 返回值：过滤器数量
func (p *FilterPipeline) Size() int {
	return len(p.filters)
}

// Clear 清空过滤器管道
// 功能：清空管道中的所有过滤器
func (p *FilterPipeline) Clear() {
	p.filters = p.filters[:0]
	p.internalDocumentFilters = p.internalDocumentFilters[:0]
	p.externalDocumentFilters = p.externalDocumentFilters[:0]
}

// WithApprovalStatusFilter 审批状态过滤器
// 功能：创建审批状态过滤器函数
// 返回值：过滤器函数
func WithApprovalStatusFilter() FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含审批状态参数
		// 2. 如果包含，则设置到查询参数中
		// 3. 返回继续执行标志

		if req.Status != 0 {
			pageReq.ApprovalStatus = req.Status
		}
		return false, nil
	}
}

// WithDocumentModuleFilter 文档模块过滤器
// 功能：创建文档模块过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentModuleFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档模块参数
		// 2. 设置到查询参数中，由数据库层面通过all_documents_view进行过滤
		// 3. 返回继续执行标志

		if req.DocumentModuleType != 0 {
			pageReq.DocumentModuleType = req.DocumentModuleType
		}
		return false, nil
	}
}

// WithDocumentCategoryFilter 文档类别过滤器
// 功能：创建文档类别过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentCategoryFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档类别参数
		// 2. 设置类别ID参数，由数据库层面通过all_documents_view进行过滤

		if len(req.DocumentCategoryID) == 0 {
			return false, nil
		}

		// 直接设置类别ID参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentCategoryIDs = req.DocumentCategoryID

		return false, nil
	}
}

// WithApplicantFilter 申请人过滤器
// 功能：创建申请人过滤器函数，通过用户昵称模糊匹配查找所有匹配的用户ID
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithApplicantFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含申请人参数
		// 2. 通过昵称模糊匹配查找所有匹配的用户ID
		// 3. 设置所有用户ID到查询参数中
		// 4. 如果查询不到用户，返回空结果

		if req.Applicant == "" {
			return false, nil
		}

		// 通过昵称模糊匹配查找所有用户ID
		userClient := mapper.NewUserClient(svcCtx.PhoenixDB)
		userIDs, err := userClient.GetUserIDsByNickname(ctx, req.Applicant)
		if err != nil {
			return false, err
		}

		if len(userIDs) == 0 {
			return true, nil // 没有找到用户，返回空结果
		}

		// 使用所有匹配的用户ID进行查询
		pageReq.CreatedByIDs = userIDs

		return false, nil
	}
}

// WithDocumentNoFilter 文档编号过滤器
// 功能：创建文档编号过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentNoFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档编号参数
		// 2. 设置文档编号参数，由数据库层面通过all_documents_view进行过滤

		if req.DocumentNo == "" {
			return false, nil
		}

		// 直接设置文档编号参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentNo = req.DocumentNo

		return false, nil
	}
}

// WithDocumentNameFilter 文档名称过滤器
// 功能：创建文档名称过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentNameFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档名称参数
		// 2. 设置文档名称参数，由数据库层面通过all_documents_view进行过滤

		if req.DocumentName == "" {
			return false, nil
		}

		// 直接设置文档名称参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentName = req.DocumentName

		return false, nil
	}
}

// WithOrganizationFilter 组织过滤器
// 功能：创建组织过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithOrganizationFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetDeprecateApplicationsReq, pageReq *mapper.DeprecationRecordPageReq) (bool, error) {
		// 设置组织ID和租户ID
		pageReq.OrganizationID = utils.GetContextOrganizationID(ctx)
		pageReq.TenantID = utils.GetContextTenantID(ctx)
		return false, nil
	}
}

// ==================== 内部文档过滤器函数 ====================

// WithOrganizationFilterForInternalDoc 组织过滤器
// 功能：创建内部文档组织过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithOrganizationFilterForInternalDoc(svcCtx *svc.ServiceContext) InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		// 设置组织ID
		pageReq.OrganizationID = utils.GetContextOrganizationID(ctx)
		return false, nil
	}
}

// WithDocumentNoFilterForInternalDoc 文档编号过滤器
// 功能：创建内部文档编号过滤器函数
// 返回值：过滤器函数
func WithDocumentNoFilterForInternalDoc() InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if req.DocumentNo != "" {
			pageReq.No = req.DocumentNo
		}
		return false, nil
	}
}

// WithDocumentNameFilterForInternalDoc 文档名称过滤器
// 功能：创建内部文档名称过滤器函数
// 返回值：过滤器函数
func WithDocumentNameFilterForInternalDoc() InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if req.DocumentName != "" {
			pageReq.Name = req.DocumentName
		}
		return false, nil
	}
}

// WithOriginalDocumentNoFilterForInternalDoc 原文档编号过滤器
// 功能：创建内部文档原编号过滤器函数
// 返回值：过滤器函数
func WithOriginalDocumentNoFilterForInternalDoc() InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if req.OriginalDocumentNo != "" {
			pageReq.OriginalNo = req.OriginalDocumentNo
		}
		return false, nil
	}
}

// WithDocumentCategoryFilterForInternalDoc 文档类别过滤器
// 功能：创建内部文档类别过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentCategoryFilterForInternalDoc(svcCtx *svc.ServiceContext) InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if len(req.DocumentCategoryID) > 0 {
			pageReq.DocCategoryIds = req.DocumentCategoryID
		}
		return false, nil
	}
}

// WithDraftingDepartmentFilterForInternalDoc 编制部门过滤器
// 功能：创建内部文档编制部门过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDraftingDepartmentFilterForInternalDoc(svcCtx *svc.ServiceContext) InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if len(req.DraftingDepartmentID) > 0 {
			pageReq.DepartmentIds = req.DraftingDepartmentID
		}
		return false, nil
	}
}

// WithStatusFilterForInternalDoc 状态过滤器
// 功能：创建内部文档状态过滤器函数
// 返回值：过滤器函数
func WithStatusFilterForInternalDoc() InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if req.Status != 0 {
			pageReq.Status = req.Status
		}
		return false, nil
	}
}

// WithAttachmentFilterForInternalDoc 附件过滤器
// 功能：创建内部文档附件过滤器函数
// 返回值：过滤器函数
func WithAttachmentFilterForInternalDoc() InternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq, pageReq *mapper.InternalDocumentLibraryPageReq) (bool, error) {
		if req.HasAttachment != 0 {
			pageReq.HasAttachment = req.HasAttachment
		}
		return false, nil
	}
}

// ==================== 外部文档过滤器函数 ====================

// WithOrganizationFilterForExternalDoc 组织过滤器
// 功能：创建外部文档组织过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithOrganizationFilterForExternalDoc(svcCtx *svc.ServiceContext) ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		// 设置组织ID
		pageReq.OrgID = utils.GetContextOrganizationID(ctx)
		return false, nil
	}
}

// WithDocumentNoFilterForExternalDoc 文档编号过滤器
// 功能：创建外部文档编号过滤器函数
// 返回值：过滤器函数
func WithDocumentNoFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.Number != "" {
			pageReq.Number = req.Number
		}
		return false, nil
	}
}

// WithDocumentNameFilterForExternalDoc 文档名称过滤器
// 功能：创建外部文档名称过滤器函数
// 返回值：过滤器函数
func WithDocumentNameFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.Name != "" {
			pageReq.Name = req.Name
		}
		return false, nil
	}
}

// WithOriginalDocumentNoFilterForExternalDoc 原文档编号过滤器
// 功能：创建外部文档原编号过滤器函数
// 返回值：过滤器函数
func WithOriginalDocumentNoFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.OriginalNumber != "" {
			pageReq.OriginalNumber = req.OriginalNumber
		}
		return false, nil
	}
}

// WithDocumentTypeFilterForExternalDoc 文档类型过滤器
// 功能：创建外部文档类型过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentTypeFilterForExternalDoc(svcCtx *svc.ServiceContext) ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if len(req.TypeDictionaryNodeIds) > 0 {
			pageReq.TypeDictionaryNodeIds = req.TypeDictionaryNodeIds
		}
		return false, nil
	}
}

// WithPublishDepartmentFilterForExternalDoc 发文部门过滤器
// 功能：创建外部文档发文部门过滤器函数
// 返回值：过滤器函数
func WithPublishDepartmentFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.PublishDepartment != "" {
			pageReq.PublishDepartment = req.PublishDepartment
		}
		return false, nil
	}
}

// WithStatusFilterForExternalDoc 状态过滤器
// 功能：创建外部文档状态过滤器函数
// 返回值：过滤器函数
func WithStatusFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.Status != 0 {
			pageReq.Status = int(req.Status)
		}
		return false, nil
	}
}

// WithAttachmentFilterForExternalDoc 附件过滤器
// 功能：创建外部文档附件过滤器函数
// 返回值：过滤器函数
func WithAttachmentFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		if req.BeAttachedFile != 0 {
			if req.BeAttachedFile == 1 {
				pageReq.BeAttachedFile = "1"
			} else {
				pageReq.BeAttachedFile = "2"
			}
		}
		return false, nil
	}
}

// WithOrgTypeFilterForExternalDoc 机构类型过滤器
// 功能：创建外部文档机构类型过滤器函数
// 返回值：过滤器函数
func WithOrgTypeFilterForExternalDoc() ExternalDocumentFilterFunc {
	return func(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq, pageReq *mapper.ExternalDocumentPage) (bool, error) {
		// 根据机构类型可以进行特殊处理，这里暂时不做特殊处理
		// 如果后续需要根据机构类型进行过滤，可以在这里实现
		return false, nil
	}
}