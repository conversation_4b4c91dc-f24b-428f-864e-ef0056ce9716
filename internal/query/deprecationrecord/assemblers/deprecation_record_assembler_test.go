package assemblers

import (
	"context"
	"testing"

	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/mapper"

	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

// TestDeprecationRecordAssembler_TranslateCommaDelimitedIDs 测试翻译英文逗号分割的ID字符串
func TestDeprecationRecordAssembler_TranslateCommaDelimitedIDs(t *testing.T) {
	Convey("测试翻译英文逗号分割的ID字符串", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 创建Mock翻译器
		mockTranslator := addons.NewMockQuickNameTranslator(ctrl)
		assembler := NewDeprecationRecordAssembler(mockTranslator)
		ctx := context.Background()

		Convey("测试翻译用户ID字符串", func() {
			// 设置Mock期望
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user002").Return("李四")
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user003").Return("王五")

			// 测试翻译
			result := assembler.translateUserIDs(ctx, "user001,user002,user003")
			So(result, ShouldEqual, "张三、李四、王五")
		})

		Convey("测试翻译组织ID字符串", func() {
			// 设置Mock期望
			mockTranslator.EXPECT().TranslateOrganizationName(ctx, "org001").Return("技术部")
			mockTranslator.EXPECT().TranslateOrganizationName(ctx, "org002").Return("质量部")

			// 测试翻译
			result := assembler.translateOrganizationIDs(ctx, "org001,org002")
			So(result, ShouldEqual, "技术部、质量部")
		})

		Convey("测试空字符串", func() {
			result := assembler.translateUserIDs(ctx, "")
			So(result, ShouldEqual, "")
		})

		Convey("测试包含空格的ID字符串", func() {
			// 设置Mock期望
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user002").Return("李四")

			// 测试翻译（包含空格）
			result := assembler.translateUserIDs(ctx, " user001 , user002 ")
			So(result, ShouldEqual, "张三、李四")
		})

		Convey("测试翻译失败时使用原ID", func() {
			// 设置Mock期望：第一个翻译成功，第二个翻译失败（返回空字符串）
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user002").Return("")

			// 测试翻译
			result := assembler.translateUserIDs(ctx, "user001,user002")
			So(result, ShouldEqual, "张三、user002")
		})

		Convey("测试单个ID", func() {
			// 设置Mock期望
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")

			// 测试翻译
			result := assembler.translateUserIDs(ctx, "user001")
			So(result, ShouldEqual, "张三")
		})
	})
}

// TestDeprecationRecordAssembler_AssembleInternalDeprecatedVersionDetail 测试组装内部作废版本详情
func TestDeprecationRecordAssembler_AssembleInternalDeprecatedVersionDetail(t *testing.T) {
	Convey("测试组装内部作废版本详情", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 创建Mock翻译器
		mockTranslator := addons.NewMockQuickNameTranslator(ctrl)
		assembler := NewDeprecationRecordAssembler(mockTranslator)
		ctx := context.Background()

		// 设置Mock期望
		mockTranslator.EXPECT().TranslateOrganizationName(ctx, "dept001").Return("技术部")
		mockTranslator.EXPECT().TranslateOrganizationName(ctx, "dept002").Return("质量部")
		mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")
		mockTranslator.EXPECT().TranslateUserNickname(ctx, "user002").Return("李四")

		// 创建测试数据
		version := mapper.InternalDocumentLibrary{
			ID:                    "doc001",
			No:                    "DOC-001",
			Version:               "1.0",
			OriginalNo:            "ORIG-001",
			OriginalVersionNo:     "1.0",
			Name:                  "测试文档",
			EnglishName:           "Test Document",
			DepartmentIDs:         "dept001,dept002", // 英文逗号分割的部门ID
			AuthorIDs:             "user001,user002", // 英文逗号分割的用户ID
			ReplacementDocName:    "替代文档",
			ReplacementDocVersion: "2.0",
			Remark:                "测试备注",
		}

		queryResult := &InternalDocumentQueryResult{
			CategoryNames: map[string]string{
				"cat001": "文档类别1",
			},
			DocumentToDeprecationRecordMap: map[string]string{
				"doc001": "record001",
			},
		}

		// 执行测试
		result := assembler.assembleInternalDeprecatedVersionDetail(ctx, version, queryResult)

		// 验证结果
		So(result.ID, ShouldEqual, "doc001")
		So(result.DocumentNo, ShouldEqual, "DOC-001")
		So(result.DocumentVersionNo, ShouldEqual, "1.0")
		So(result.DocumentName, ShouldEqual, "测试文档")
		So(result.DocumentEnglishName, ShouldEqual, "Test Document")
		So(result.DraftingDepartmentName, ShouldEqual, "技术部、质量部") // 翻译后用中文顿号连接
		So(result.Drafter, ShouldEqual, "张三、李四")                  // 翻译后用中文顿号连接
		So(result.ReplacedDocumentName, ShouldEqual, "替代文档")
		So(result.ReplacedVersionNo, ShouldEqual, "2.0")
		So(result.Remark, ShouldEqual, "测试备注")
		So(result.ApprovalID, ShouldEqual, "record001")
	})
}

// TestDeprecationRecordAssembler_TranslateCommaDelimitedIDs_EdgeCases 测试边界情况
func TestDeprecationRecordAssembler_TranslateCommaDelimitedIDs_EdgeCases(t *testing.T) {
	Convey("测试翻译功能的边界情况", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockTranslator := addons.NewMockQuickNameTranslator(ctrl)
		assembler := NewDeprecationRecordAssembler(mockTranslator)
		ctx := context.Background()

		Convey("测试只有逗号的字符串", func() {
			result := assembler.translateUserIDs(ctx, ",,,")
			So(result, ShouldEqual, "")
		})

		Convey("测试包含空ID的字符串", func() {
			// 设置Mock期望
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001").Return("张三")

			result := assembler.translateUserIDs(ctx, "user001,,")
			So(result, ShouldEqual, "张三")
		})

		Convey("测试中文逗号（不应该被分割）", func() {
			// 设置Mock期望：整个字符串作为一个ID
			mockTranslator.EXPECT().TranslateUserNickname(ctx, "user001，user002").Return("特殊用户")

			result := assembler.translateUserIDs(ctx, "user001，user002")
			So(result, ShouldEqual, "特殊用户")
		})
	})
}
