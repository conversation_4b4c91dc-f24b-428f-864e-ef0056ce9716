package filters

import (
	"context"
	"strconv"
	"strings"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/query/shared"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
)

// FilterFunc 过滤器函数类型
// 功能：定义过滤器函数的签名
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
type FilterFunc func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error)

// FilterPipeline 过滤器管道
// 功能：使用函数式编程组织多个过滤器，替代传统的责任链模式
type FilterPipeline struct {
	filters []FilterFunc
}

// NewFilterPipeline 创建过滤器管道
// 功能：创建过滤器管道实例
// 返回值：过滤器管道实例
func NewFilterPipeline() *FilterPipeline {
	return &FilterPipeline{
		filters: make([]FilterFunc, 0),
	}
}

// Add 添加过滤器函数
// 功能：向管道中添加一个过滤器函数
// 参数：filter - 过滤器函数
// 返回值：过滤器管道实例（支持链式调用）
func (p *FilterPipeline) Add(filter FilterFunc) *FilterPipeline {
	// 实现步骤：
	// 1. 将过滤器函数添加到过滤器列表中
	// 2. 返回自身以支持链式调用

	p.filters = append(p.filters, filter)
	return p
}

// Execute 执行所有过滤器
// 功能：按顺序执行所有过滤器函数，如果任何一个过滤器返回空结果，则停止执行
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (p *FilterPipeline) Execute(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 遍历所有过滤器函数
	// 2. 依次执行每个过滤器函数
	// 3. 如果任何过滤器返回错误或空结果，立即返回
	// 4. 所有过滤器都成功执行后返回成功

	for _, filter := range p.filters {
		isEmpty, err := filter(ctx, req, pageReq)
		if err != nil {
			return false, err
		}
		if isEmpty {
			return true, nil
		}
	}

	return false, nil
}

// Size 获取过滤器数量
// 功能：返回管道中过滤器的数量
// 返回值：过滤器数量
func (p *FilterPipeline) Size() int {
	return len(p.filters)
}

// Clear 清空过滤器管道
// 功能：清空管道中的所有过滤器
func (p *FilterPipeline) Clear() {
	p.filters = p.filters[:0]
}

// mergeDocumentIDs 合并文档ID列表并去重
// 功能：将新的文档ID列表合并到现有的文档ID列表中，并进行去重处理
// 参数：existing - 现有的文档ID列表，newIDs - 新的文档ID列表
// 返回值：合并去重后的文档ID列表
func mergeDocumentIDs(existing []string, newIDs []string) []string {
	if existing == nil {
		return newIDs
	}
	// 合并两个切片
	combined := append(existing, newIDs...)
	// 使用工具函数去重
	return utils.SliceDuplicate(combined)
}

// WithApprovalStatusFilter 审批状态过滤器
// 功能：创建审批状态过滤器函数
// 返回值：过滤器函数
func WithApprovalStatusFilter() FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含审批状态参数
		// 2. 如果包含，则设置到查询参数中
		// 3. 返回继续执行标志

		if req.ApprovalStatus != "" {
			// 将字符串转换为int指针
			status, err := strconv.Atoi(req.ApprovalStatus)
			if err != nil {
				return false, err
			}
			pageReq.ApprovalStatus = &status
		}
		return false, nil
	}
}

// WithDocumentModuleFilter 文档模块过滤器
// 功能：创建文档模块过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentModuleFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档模块参数
		// 2. 设置到查询参数中，由数据库层面通过all_documents_view进行过滤
		// 3. 返回继续执行标志

		if req.DocumentModuleType != 0 {
			pageReq.ModuleType = &req.DocumentModuleType
		}
		return false, nil
	}
}

// WithDocumentCategoryFilter 文档类别过滤器
// 功能：创建文档类别过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentCategoryFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档类别参数
		// 2. 设置类别ID参数，由数据库层面通过all_documents_view进行过滤

		if req.DocumentCategoryID == "" {
			return false, nil
		}

		// 直接设置类别ID参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentCategoryIDs = strings.Split(req.DocumentCategoryID, ",")

		return false, nil
	}
}

// WithUserNicknameFilter 用户昵称过滤器
// 功能：创建用户昵称过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithUserNicknameFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含用户昵称参数
		// 2. 如果包含，则查询用户ID
		// 3. 设置用户ID到查询参数中
		// 4. 如果查询不到用户，返回空结果

		if req.UserNickname == "" {
			return false, nil
		}

		userClient := mapper.NewUserClient(svcCtx.PhoenixDB)
		userIDs, err := userClient.GetUserIDsByNickname(ctx, req.UserNickname)
		if err != nil {
			return false, err
		}

		if len(userIDs) == 0 {
			return true, nil // 没有找到用户，返回空结果
		}

		// 取第一个匹配的用户ID
		pageReq.UserIDs = userIDs
		return false, nil
	}
}

// WithDocumentNoFilter 文档编号过滤器
// 功能：创建文档编号过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentNoFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档编号参数
		// 2. 设置文档编号参数，由数据库层面通过all_documents_view进行过滤

		if req.DocumentNo == "" {
			return false, nil
		}

		// 直接设置文档编号参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentNo = req.DocumentNo

		return false, nil
	}
}

// WithDocumentNameFilter 文档名称过滤器
// 功能：创建文档名称过滤器函数，使用文档连接优化策略
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithDocumentNameFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 实现步骤：
		// 1. 检查请求中是否包含文档名称参数
		// 2. 设置文档名称参数，由数据库层面通过all_documents_view进行过滤

		if req.DocumentName == "" {
			return false, nil
		}

		// 直接设置文档名称参数，由数据库层面通过all_documents_view进行过滤
		pageReq.DocumentName = req.DocumentName

		return false, nil
	}
}

// WithOrganizationFilter 组织过滤器
// 功能：创建组织过滤器函数
// 参数：svcCtx - 服务上下文
// 返回值：过滤器函数
func WithOrganizationFilter(svcCtx *svc.ServiceContext) FilterFunc {
	return func(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
		// 查询用户权限范围内的组织id
		fileManagerInfo, err := shared.NewOrganizationAuth(svcCtx).GetUserFileManagerInfo(ctx)
		if err != nil {
			return false, err
		}

		// 不是文件管理员，直接查询该用户创建的数据
		if !fileManagerInfo.IsFileManager {
			pageReq.UserID = utils.GetContextUserID(ctx)
			return false, nil
		}

		// 多个组织，查询所有组织下的数据
		pageReq.OrgID = utils.GetContextOrganizationID(ctx)
		return false, nil
	}
}
