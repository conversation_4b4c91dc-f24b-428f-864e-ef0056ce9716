package services

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
)

// DocumentQueryService 文档查询服务
// 功能：统一处理文档ID查询逻辑，消除重复代码
type DocumentQueryService struct {
	svcCtx *svc.ServiceContext
}

// NewDocumentQueryService 创建文档查询服务
// 功能：创建文档查询服务实例
// 参数：svcCtx - 服务上下文
// 返回值：文档查询服务实例
func NewDocumentQueryService(svcCtx *svc.ServiceContext) *DocumentQueryService {
	return &DocumentQueryService{
		svcCtx: svcCtx,
	}
}

// QueryDocumentIDsByNo 根据文档编号查询文档ID列表
// 功能：根据文档模块类型查询对应文档库中的文档ID
// 参数：ctx - 上下文，documentNo - 文档编号，moduleType - 文档模块类型
// 返回值：文档ID列表，错误信息
func (s *DocumentQueryService) QueryDocumentIDsByNo(ctx context.Context, documentNo, moduleType string) ([]string, error) {
	// 实现步骤：
	// 1. 根据模块类型决定查询策略
	// 2. 执行相应的查询操作
	// 3. 合并查询结果

	return s.queryDocumentIDs(ctx, documentNo, moduleType, s.queryByNo)
}

// QueryDocumentIDsByName 根据文档名称查询文档ID列表
// 功能：根据文档模块类型查询对应文档库中的文档ID
// 参数：ctx - 上下文，documentName - 文档名称，moduleType - 文档模块类型
// 返回值：文档ID列表，错误信息
func (s *DocumentQueryService) QueryDocumentIDsByName(ctx context.Context, documentName, moduleType string) ([]string, error) {
	// 实现步骤：
	// 1. 根据模块类型决定查询策略
	// 2. 执行相应的查询操作
	// 3. 合并查询结果

	return s.queryDocumentIDs(ctx, documentName, moduleType, s.queryByName)
}

// queryDocumentIDs 通用文档ID查询方法
// 功能：根据查询函数和模块类型执行文档ID查询
// 参数：ctx - 上下文，queryValue - 查询值，moduleType - 模块类型，queryFunc - 查询函数
// 返回值：文档ID列表，错误信息
func (s *DocumentQueryService) queryDocumentIDs(ctx context.Context, queryValue, moduleType string, queryFunc func(context.Context, string, string) ([]string, error)) ([]string, error) {
	// 实现步骤：
	// 1. 检查查询值是否为空
	// 2. 根据模块类型执行查询
	// 3. 返回查询结果

	if queryValue == "" {
		return []string{}, nil
	}

	var documentIDs []string

	// 根据文档模块类型决定查询哪个文档库
	if moduleType != "" {
		switch moduleType {
		case "2": // 内部文档
			ids, err := queryFunc(ctx, queryValue, "internal")
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		case "3": // 外部文档
			ids, err := queryFunc(ctx, queryValue, "external")
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		}
	} else {
		// 如果没有指定文档模块，则同时查询内部和外部文档
		internalIDs, err := queryFunc(ctx, queryValue, "internal")
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, internalIDs...)

		externalIDs, err := queryFunc(ctx, queryValue, "external")
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, externalIDs...)
	}

	return documentIDs, nil
}

// queryByNo 根据编号查询文档ID
// 功能：根据文档类型和编号查询文档ID
// 参数：ctx - 上下文，queryValue - 查询值，docType - 文档类型
// 返回值：文档ID列表，错误信息
func (s *DocumentQueryService) queryByNo(ctx context.Context, queryValue, docType string) ([]string, error) {
	// 实现步骤：
	// 1. 根据文档类型创建相应的客户端
	// 2. 执行查询操作
	// 3. 返回查询结果

	switch docType {
	case "internal":
		internalClient := mapper.NewInternalDocumentLibraryClient(s.svcCtx.DocvaultDB)
		return internalClient.GetDocumentIDsByNo(ctx, queryValue)
	case "external":
		externalClient := mapper.NewExternalDocumentLibraryClient(s.svcCtx.DocvaultDB)
		return externalClient.GetDocumentIDsByNo(ctx, queryValue)
	default:
		return []string{}, nil
	}
}

// queryByName 根据名称查询文档ID
// 功能：根据文档类型和名称查询文档ID
// 参数：ctx - 上下文，queryValue - 查询值，docType - 文档类型
// 返回值：文档ID列表，错误信息
func (s *DocumentQueryService) queryByName(ctx context.Context, queryValue, docType string) ([]string, error) {
	// 实现步骤：
	// 1. 根据文档类型创建相应的客户端
	// 2. 执行查询操作
	// 3. 返回查询结果

	switch docType {
	case "internal":
		internalClient := mapper.NewInternalDocumentLibraryClient(s.svcCtx.DocvaultDB)
		return internalClient.GetDocumentIDsByName(ctx, queryValue)
	case "external":
		externalClient := mapper.NewExternalDocumentLibraryClient(s.svcCtx.DocvaultDB)
		return externalClient.GetDocumentIDsByName(ctx, queryValue)
	default:
		return []string{}, nil
	}
}

// IntersectDocumentIDs 计算两个文档ID列表的交集
// 功能：计算两个文档ID列表的交集，用于多条件查询时的结果合并
// 参数：list1 - 第一个文档ID列表，list2 - 第二个文档ID列表
// 返回值：交集文档ID列表
func (s *DocumentQueryService) IntersectDocumentIDs(list1, list2 []string) []string {
	// 实现步骤：
	// 1. 创建第一个列表的映射
	// 2. 遍历第二个列表，查找交集
	// 3. 返回交集结果

	if len(list1) == 0 || len(list2) == 0 {
		return []string{}
	}

	documentIDSet := make(map[string]bool)
	for _, id := range list1 {
		documentIDSet[id] = true
	}

	var intersectionIDs []string
	for _, id := range list2 {
		if documentIDSet[id] {
			intersectionIDs = append(intersectionIDs, id)
		}
	}

	return intersectionIDs
}