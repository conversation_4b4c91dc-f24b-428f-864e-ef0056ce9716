package borrowrecord

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/query/borrowrecord/assemblers"
	"nebula/internal/query/borrowrecord/filters"
	"nebula/internal/query/borrowrecord/services"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// BorrowRecordQueryService 借阅记录查询服务
// 功能：对外提供借阅记录的统一查询服务，封装内部的查询组件
type BorrowRecordQueryService struct {
	documentQueryService   *services.DocumentQueryService
	concurrentQueryService *services.ConcurrentQueryService
	loanRecordAssembler    *assemblers.LoanRecordAssembler
	filterPipeline         *filters.FilterPipeline
	svcCtx                 *svc.ServiceContext
}

// NewBorrowRecordQueryService 创建借阅记录查询服务
// 功能：创建借阅记录查询服务实例，初始化所有查询组件
// 参数：svcCtx - 服务上下文
// 返回值：借阅记录查询服务实例
func NewBorrowRecordQueryService(svcCtx *svc.ServiceContext) *BorrowRecordQueryService {
	return &BorrowRecordQueryService{
		documentQueryService:   services.NewDocumentQueryService(svcCtx),
		concurrentQueryService: services.NewConcurrentQueryService(svcCtx),
		loanRecordAssembler:    assemblers.NewLoanRecordAssembler(),
		filterPipeline:         filters.NewFilterPipeline(),
		svcCtx:                 svcCtx,
	}
}

// GetLoanRecords 获取借阅记录
// 功能：统一的借阅记录查询入口，整合所有查询逻辑
// 参数：ctx - 上下文，req - 查询请求
// 返回值：借阅记录响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (brqs *BorrowRecordQueryService) GetLoanRecords(ctx context.Context, req *types.GetLoanRecordsReq) (*types.GetLoanRecordsResp, error) {
	// 实现步骤：
	// 1. 构建分页查询请求
	// 2. 设置过滤器链
	// 3. 应用过滤器
	// 4. 执行数据库查询
	// 5. 并发查询关联数据
	// 6. 组装响应数据

	// 1. 构建分页查询请求
	pageReq := &mapper.PageBorrowRecordReq{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	}

	// 2. 设置过滤器管道
	brqs.setupFilterPipeline()

	// 3. 应用过滤器
	isEmpty, err := brqs.filterPipeline.Execute(ctx, req, pageReq)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &types.GetLoanRecordsResp{
			Data:  []types.LoanRecord{},
			Total: 0,
		}, nil
	}

	// 4. 执行数据库查询
	borrowerClient := mapper.NewBorrowRecordClient(brqs.svcCtx.DocvaultDB)
	borrowerRecords, total, err := borrowerClient.GetBorrowRecordStatistics(ctx, *pageReq)
	if err != nil {
		return nil, err
	}

	if len(borrowerRecords) == 0 {
		return &types.GetLoanRecordsResp{
			Data:  []types.LoanRecord{},
			Total: int64(total),
		}, nil
	}

	// 5. 并发查询关联数据
	queryResult, err := brqs.concurrentQueryService.QueryConcurrently(ctx, borrowerRecords)
	if err != nil {
		return nil, err
	}

	// 6. 组装最终响应数据
	assemblerQueryResult := &assemblers.QueryResult{
		UserNicknames:  queryResult.UserNicknames,
		DocumentCounts: queryResult.DocumentCounts,
	}

	return brqs.loanRecordAssembler.AssembleWithPagination(borrowerRecords, assemblerQueryResult, int(total)), nil
}

// setupFilterPipeline 设置过滤器管道
// 功能：配置过滤器管道，使用文档连接优化策略，添加所有需要的过滤器函数
func (brqs *BorrowRecordQueryService) setupFilterPipeline() {
	// 实现步骤：
	// 1. 清空现有过滤器
	// 2. 按顺序添加过滤器函数

	// 1. 清空现有过滤器
	brqs.filterPipeline.Clear()

	// 2. 按顺序添加过滤器函数
	// 注意：过滤器的添加顺序会影响执行效率，应该将过滤性更强的过滤器放在前面
	// 所有过滤器现在都使用文档连接优化策略，直接在数据库层面进行过滤
	brqs.filterPipeline.
		Add(filters.WithApprovalStatusFilter()).
		Add(filters.WithDocumentModuleFilter(brqs.svcCtx)).
		Add(filters.WithUserNicknameFilter(brqs.svcCtx)).
		Add(filters.WithDocumentCategoryFilter(brqs.svcCtx)).
		Add(filters.WithDocumentNoFilter(brqs.svcCtx)).
		Add(filters.WithDocumentNameFilter(brqs.svcCtx)).
		Add(filters.WithOrganizationFilter(brqs.svcCtx))
}

// QueryDocumentIDsByNo 根据文档编号查询文档ID
// 功能：对外提供文档编号查询接口
// 参数：ctx - 上下文，documentNo - 文档编号，moduleType - 模块类型
// 返回值：文档ID列表，错误信息
func (brqs *BorrowRecordQueryService) QueryDocumentIDsByNo(ctx context.Context, documentNo, moduleType string) ([]string, error) {
	return brqs.documentQueryService.QueryDocumentIDsByNo(ctx, documentNo, moduleType)
}

// QueryDocumentIDsByName 根据文档名称查询文档ID
// 功能：对外提供文档名称查询接口
// 参数：ctx - 上下文，documentName - 文档名称，moduleType - 模块类型
// 返回值：文档ID列表，错误信息
func (brqs *BorrowRecordQueryService) QueryDocumentIDsByName(ctx context.Context, documentName, moduleType string) ([]string, error) {
	return brqs.documentQueryService.QueryDocumentIDsByName(ctx, documentName, moduleType)
}

// QueryConcurrently 并发查询
// 功能：对外提供并发查询接口
// 参数：ctx - 上下文，borrowerRecords - 借阅记录列表
// 返回值：查询结果，错误信息
func (brqs *BorrowRecordQueryService) QueryConcurrently(ctx context.Context, borrowerRecords []mapper.BorrowRecordView) (*services.QueryResult, error) {
	return brqs.concurrentQueryService.QueryConcurrently(ctx, borrowerRecords)
}
