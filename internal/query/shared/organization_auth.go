package shared

import (
	"context"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

const (
	// 角色代码常量
	GroupFileManagerRoleCode      = "JTWJGLY"  // 集团文件管理员角色代码
	SubsidiaryFileManagerRoleCode = "ZGSWJGLY" // 子公司文件管理员角色代码
)

// FileManagerType 文件管理员类型
type FileManagerType string

const (
	// 文件管理员类型常量
	GroupFileManager      FileManagerType = "GROUP"      // 集团文件管理员
	SubsidiaryFileManager FileManagerType = "SUBSIDIARY" // 子公司文件管理员
	NotFileManager        FileManagerType = "NONE"       // 非文件管理员
)

// FileManagerInfo 文件管理员信息
type FileManagerInfo struct {
	IsFileManager bool            `json:"is_file_manager"` // 是否为文件管理员
	ManagerType   FileManagerType `json:"manager_type"`    // 文件管理员类型
	Description   string          `json:"description"`     // 管理员类型描述
}

// OrganizationAuth 组织权限查询器
// 功能：根据用户角色和组织层级，获取用户文件管理员信息
type OrganizationAuth struct {
	svcCtx *svc.ServiceContext
}

// NewOrganizationAuth 创建组织权限查询器实例
// 功能：初始化组织权限查询器
// 参数：
//   - svcCtx: 服务上下文
//
// 返回值：
//   - *OrganizationAuth: 组织权限查询器实例
func NewOrganizationAuth(svcCtx *svc.ServiceContext) *OrganizationAuth {
	return &OrganizationAuth{svcCtx: svcCtx}
}

// GetUserFileManagerInfo 获取用户文件管理员信息
// 功能：根据用户角色和组织层级，返回用户是否为文件管理员以及管理员类型
// 实现逻辑：
//  1. 获取当前登录用户信息
//  2. 获取用户所属组织信息
//  3. 根据组织类型（集团/子公司）和用户角色权限确定文件管理员类型
//
// 参数：
//   - ctx: 上下文
//
// 返回值：
//   - *FileManagerInfo: 文件管理员信息
//   - error: 错误信息
func (o *OrganizationAuth) GetUserFileManagerInfo(ctx context.Context) (*FileManagerInfo, error) {
	// 1. 获取当前登录用户信息
	userLoginInfo := utils.GetCurrentLoginUser(ctx)

	// 2. 获取用户所属组织信息
	organization, err := o.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, userLoginInfo.OrganizationId)
	if err != nil {
		return nil, err
	}

	// 3. 根据组织类型确定文件管理员权限
	if o.isGroupOrganization(organization.ParentId) {
		// 处理集团组织权限
		return o.checkGroupFileManagerAuth(ctx, userLoginInfo.UserId)
	} else {
		// 处理子公司组织权限
		return o.checkSubsidiaryFileManagerAuth(ctx, userLoginInfo.UserId)
	}
}

// isGroupOrganization 判断是否为集团组织
// 功能：通过父组织ID判断当前组织是否为集团（顶级组织）
// 参数：
//   - parentId: 父组织ID
//
// 返回值：
//   - bool: true表示集团组织，false表示子公司
func (o *OrganizationAuth) isGroupOrganization(parentId string) bool {
	return parentId == ""
}

// checkGroupFileManagerAuth 检查集团文件管理员权限
// 功能：检查用户是否为集团文件管理员并返回相应信息
// 参数：
//   - ctx: 上下文
//   - userId: 用户ID
//
// 返回值：
//   - *FileManagerInfo: 文件管理员信息
//   - error: 错误信息
func (o *OrganizationAuth) checkGroupFileManagerAuth(ctx context.Context, userId string) (*FileManagerInfo, error) {
	// 检查是否为集团文件管理员
	isGroupFileManager, err := o.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userId, GroupFileManagerRoleCode)
	if err != nil {
		return nil, err
	}

	if isGroupFileManager {
		// 集团文件管理员
		return &FileManagerInfo{
			IsFileManager: true,
			ManagerType:   GroupFileManager,
			Description:   "集团文件管理员",
		}, nil
	}

	// 普通集团用户
	return &FileManagerInfo{
		IsFileManager: false,
		ManagerType:   NotFileManager,
		Description:   "普通用户",
	}, nil
}

// checkSubsidiaryFileManagerAuth 检查子公司文件管理员权限
// 功能：检查用户是否为子公司文件管理员并返回相应信息
// 参数：
//   - ctx: 上下文
//   - userId: 用户ID
//
// 返回值：
//   - *FileManagerInfo: 文件管理员信息
//   - error: 错误信息
func (o *OrganizationAuth) checkSubsidiaryFileManagerAuth(ctx context.Context, userId string) (*FileManagerInfo, error) {
	// 检查是否为子公司文件管理员
	isSubsidiaryFileManager, err := o.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userId, SubsidiaryFileManagerRoleCode)
	if err != nil {
		return nil, err
	}

	if isSubsidiaryFileManager {
		// 子公司文件管理员
		return &FileManagerInfo{
			IsFileManager: true,
			ManagerType:   SubsidiaryFileManager,
			Description:   "子公司文件管理员",
		}, nil
	}

	// 普通子公司用户
	return &FileManagerInfo{
		IsFileManager: false,
		ManagerType:   NotFileManager,
		Description:   "普通用户",
	}, nil
}
