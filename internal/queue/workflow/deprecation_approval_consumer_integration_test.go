package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/clientx"
	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestDeprecationApprovalConsumer_IntegrationRejected 测试审批驳回事件的集成测试
func TestDeprecationApprovalConsumer_IntegrationRejected(t *testing.T) {
	convey.Convey("测试作废审批驳回事件集成测试", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForDeprecationApproval()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据 - 驳回测试需要从审批中状态开始
		testData, err := createTestDataForDeprecationApproval(svcCtx, 2)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建消费者实例
		consumer := NewDeprecationApprovalConsumer(svcCtx)

		// 构造审批驳回消息
		msg := WorkflowEventMessage{
			TenantID:       "test_tenant_001",
			OrganizationID: "test_org_001",
			WorkflowID:     testData.WorkflowID,
			SponsorID:      "test_deprecate_user_001",
			FormContent: fmt.Sprintf(`{
				"deprecateId": "%s"
			}`, testData.DeprecationRecordID),
			CompletedAt:  time.Now().UnixMilli(),
			CreatedAt:    time.Now().UnixMilli(),
			BusinessID:   "FILE_REPEAL_002",
			BusinessCode: "FILE_REPEAL",
			EventType:    consts.WorkflowEventRejected,
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msg)
		convey.So(err, convey.ShouldBeNil)

		// 执行消息处理
		err = consumer.Handle(context.Background(), msgBytes)
		convey.So(err, convey.ShouldBeNil)

		// 验证数据库状态更新
		verifyDeprecationRecordStatus(svcCtx, testData.DeprecationRecordID, 4) // 已驳回

		// 验证文档状态 - 审批驳回后文档状态应该
		verifyDocumentsStatusByDeprecationRecord(svcCtx, testData.DeprecationRecordID, int8(2))
	})
}

// TestDeprecationApprovalConsumer_IntegrationCanceled 测试审批撤销事件的集成测试
func TestDeprecationApprovalConsumer_IntegrationCanceled(t *testing.T) {
	convey.Convey("测试作废审批撤销事件集成测试", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForDeprecationApproval()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据 - 撤销测试需要从审批中状态开始
		testData, err := createTestDataForDeprecationApproval(svcCtx, 2)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建消费者实例
		consumer := NewDeprecationApprovalConsumer(svcCtx)

		// 构造审批撤销消息
		msg := WorkflowEventMessage{
			TenantID:       "test_tenant_001",
			OrganizationID: "test_org_001",
			WorkflowID:     testData.WorkflowID,
			SponsorID:      "test_deprecate_user_001",
			FormContent: fmt.Sprintf(`{
				"deprecateId": "%s"
			}`, testData.DeprecationRecordID),
			CompletedAt:  time.Now().UnixMilli(),
			CreatedAt:    time.Now().UnixMilli(),
			BusinessID:   "FILE_REPEAL_003",
			BusinessCode: "FILE_REPEAL",
			EventType:    consts.WorkflowEventCanceled,
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msg)
		convey.So(err, convey.ShouldBeNil)

		// 执行消息处理
		err = consumer.Handle(context.Background(), msgBytes)
		convey.So(err, convey.ShouldBeNil)

		// 验证数据库状态更新
		verifyDeprecationRecordStatus(svcCtx, testData.DeprecationRecordID, 1) // 待提交

		// 验证文档状态 - 审批撤销后文档状态应该2
		verifyDocumentsStatusByDeprecationRecord(svcCtx, testData.DeprecationRecordID, int8(2))
	})
}

// TestDeprecationApprovalConsumer_IntegrationStartSuccess 测试审批启动成功事件的集成测试
func TestDeprecationApprovalConsumer_IntegrationStartSuccess(t *testing.T) {
	convey.Convey("测试作废审批启动成功事件集成测试", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForDeprecationApproval()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据 - 启动成功测试需要从待提交状态开始
		testData, err := createTestDataForDeprecationApproval(svcCtx, -1)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建消费者实例
		consumer := NewDeprecationApprovalConsumer(svcCtx)

		// 构造审批启动成功消息
		msg := WorkflowEventMessage{
			TenantID:       "test_tenant_001",
			OrganizationID: "test_org_001",
			WorkflowID:     testData.WorkflowID,
			SponsorID:      "test_deprecate_user_001",
			FormContent: fmt.Sprintf(`{
				"deprecateId": "%s"
			}`, testData.DeprecationRecordID),
			CompletedAt:  time.Now().UnixMilli(),
			CreatedAt:    time.Now().UnixMilli(),
			BusinessID:   "FILE_REPEAL_004",
			BusinessCode: "FILE_REPEAL",
			EventType:    consts.WorkflowEventStartSuccess,
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msg)
		convey.So(err, convey.ShouldBeNil)

		// 执行消息处理
		err = consumer.Handle(context.Background(), msgBytes)
		convey.So(err, convey.ShouldBeNil)

		// 验证启动成功后的状态更新
		verifyDeprecationRecordStatus(svcCtx, testData.DeprecationRecordID, 2) // 审批中
		verifyDeprecationRecordWorkflowID(svcCtx, testData.DeprecationRecordID, testData.WorkflowID)

		// 验证文档状态 - 审批启动成功后文档状态应该(1-即将作废)
		verifyDocumentsStatusByDeprecationRecord(svcCtx, testData.DeprecationRecordID, int8(1))
	})
}

// TestDeprecationApprovalConsumer_WithMockPhoenix 使用 MockPhoenixClient 的测试示例
func TestDeprecationApprovalConsumer_WithMockPhoenix(t *testing.T) {
	convey.Convey("使用 MockPhoenixClient 测试作废审批通过事件", t, func() {
		// 设置带有 Mock Phoenix 客户端的测试环境
		svcCtx, mockPhoenixClient, cleanup, err := setupTestEnvironmentWithMockPhoenix()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据 - 通过测试需要从审批中状态开始
		testData, err := createTestDataForDeprecationApproval(svcCtx, 2)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 设置 Mock Phoenix 客户端的期望行为
		// 模拟 GetWorkflow 返回成功的审批流信息
		mockWorkflowData := entity.GetWorkflowRespData{
			FlowName:   "测试审批流",
			FlowID:     testData.WorkflowID,
			FlowStatus: "approved",
			Nodes: []entity.GetWorkflowRespDataNode{
				{
					NodeName: "审批节点",
					NodeID:   "node-1",
					Status:   "approved",
					Approvers: []entity.GetWorkflowRespDataNodeApprover{
						{
							ApproverID:       "test_deprecate_approver_001",
							ApproverNickname: "作废审批测试批准人1",
							Status:           "approved",
						},
					},
				},
			},
		}
		mockPhoenixClient.EXPECT().
			GetWorkflow(gomock.Any(), testData.WorkflowID).
			Return(mockWorkflowData, nil).
			Times(1)

		// 创建消费者实例
		consumer := NewDeprecationApprovalConsumer(svcCtx)

		// 构造审批通过消息
		msg := WorkflowEventMessage{
			TenantID:       "test_tenant_001",
			OrganizationID: "test_org_001",
			WorkflowID:     testData.WorkflowID,
			SponsorID:      "test_deprecate_user_001",
			FormContent: fmt.Sprintf(`{
				"deprecateId": "%s"
			}`, testData.DeprecationRecordID),
			CompletedAt:  time.Now().UnixMilli(),
			CreatedAt:    time.Now().UnixMilli(),
			BusinessID:   "FILE_REPEAL_MOCK_001",
			BusinessCode: "FILE_REPEAL",
			EventType:    consts.WorkflowEventPassed,
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msg)
		convey.So(err, convey.ShouldBeNil)

		// 执行消息处理
		err = consumer.Handle(context.Background(), msgBytes)
		convey.So(err, convey.ShouldBeNil)

		// 验证数据库状态更新
		verifyDeprecationRecordStatus(svcCtx, testData.DeprecationRecordID, 3) // 已通过
		verifyDeprecationRecordApprovalInfo(svcCtx, testData.DeprecationRecordID)

		// 验证文档状态 - 审批通过后文档状态应该变1
		verifyDocumentsStatusByDeprecationRecord(svcCtx, testData.DeprecationRecordID, int8(1))
	})
}

// setupTestEnvironmentForDeprecationApproval 设置作废审批消费者测试环境
// 功能：初始化测试所需的服务上下文，包括数据库连接和gRPC连接
// 返回值：
//   - *svc.ServiceContext: 服务上下文
//   - func(): 清理函数
//   - error: 错误信息
func setupTestEnvironmentForDeprecationApproval() (*svc.ServiceContext, func(), error) {
	// 从配置文件加载配置
	configManager, err := gzconfigcenter.NewConfigManager[config.Config](gzconfigcenter.EtcdConfig{}, "../../../etc/nebula.yaml")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load config: %v", err)
	}

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(configManager)

	// 使用真实的 Docvault gRPC 连接
	docvaultRpcClient := zrpc.MustNewClient(zrpc.RpcClientConf{
		Endpoints: []string{"127.0.0.1:11013"},
		Timeout:   5000,
	})
	svcCtx.DocvaultRpcConn = docvaultRpcClient.Conn()

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForDeprecationApproval(svcCtx)
		// 关闭 gRPC 连接
		svcCtx.DocvaultRpcConn.Close()
	}

	return svcCtx, cleanup, nil
}

// setupTestEnvironmentWithMockPhoenix 设置带有 Mock Phoenix 客户端的测试环境
// 功能：初始化测试所需的服务上下文，使用 MockPhoenixClient 替代真实的 Phoenix 客户端
// 返回值：
//   - *svc.ServiceContext: 服务上下文
//   - *clientx.MockPhoenixClient: Mock Phoenix 客户端
//   - func(): 清理函数
//   - error: 错误信息
func setupTestEnvironmentWithMockPhoenix() (*svc.ServiceContext, *clientx.MockPhoenixClient, func(), error) {
	// 从配置文件加载配置
	configManager, err := gzconfigcenter.NewConfigManager[config.Config](gzconfigcenter.EtcdConfig{}, "../../../etc/nebula.yaml")
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to load config: %v", err)
	}

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(configManager)

	// 使用真实的 Docvault gRPC 连接
	docvaultRpcClient := zrpc.MustNewClient(zrpc.RpcClientConf{
		Endpoints: []string{"127.0.0.1:11013"},
		Timeout:   5000,
	})
	svcCtx.DocvaultRpcConn = docvaultRpcClient.Conn()

	// 创建 Mock Phoenix 客户端
	ctrl := gomock.NewController(nil) // 在实际测试中，这里应该传入 *testing.T
	mockPhoenixClient := clientx.NewMockPhoenixClient(ctrl)
	svcCtx.PhoenixClient = mockPhoenixClient

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForDeprecationApproval(svcCtx)
		// 关闭 gRPC 连接
		svcCtx.DocvaultRpcConn.Close()
		// 完成 Mock 控制器
		ctrl.Finish()
	}

	return svcCtx, mockPhoenixClient, cleanup, nil
}

// TestDataForDeprecationApproval 作废审批消费者测试数据结构
type TestDataForDeprecationApproval struct {
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
	DeprecationRecords              []mapper.DeprecationRecord
	DeprecationDocumentRelations    []mapper.DeprecationDocumentRelation
	DeprecationRecordID             string
	WorkflowID                      string
}

// createTestDataForDeprecationApproval 创建作废审批消费者测试数据
// 功能：创建测试所需的用户、内部文档、业务字典、作废记录等数据
// 参数：
//   - svcCtx: 服务上下文
//   - approvalStatus: 作废记录的初始审批状态
//
// 返回值：
//   - *TestDataForDeprecationApproval: 测试数据结构
//   - error: 错误信息
func createTestDataForDeprecationApproval(svcCtx *svc.ServiceContext, approvalStatus int32) (*TestDataForDeprecationApproval, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForDeprecationApproval(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_deprecate_user_001",
			Username:  "test_deprecate_user_001",
			Nickname:  "作废审批测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_deprecate_auditor_001",
			Username:  "test_deprecate_auditor_001",
			Nickname:  "作废审批测试审核人1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_deprecate_approver_001",
			Username:  "test_deprecate_approver_001",
			Nickname:  "作废审批测试批准人1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_deprecate_internal_doc_001",
			No:             "DEPRECATE-INT-001",
			Name:           "作废审批测试内部文档1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_deprecate_file_001",
			DocCategoryID:  "test_deprecate_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_deprecate_user_001",
			CreatedBy:      "test_deprecate_user_001",
			UpdatedBy:      "test_deprecate_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour),
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_deprecate_category_001",
			Names:  "作废审批内部文档类别",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	// 4. 创建作废记录
	deprecationRecordID := "test_deprecate_record_001"
	deprecationRecords := []mapper.DeprecationRecord{
		{
			ID:             deprecationRecordID,
			DeprecateAt:    now.Add(30 * 24 * time.Hour),
			Reason:         1,              // 版本过期
			ApprovalStatus: approvalStatus, // 使用传入的审批状态
			WorkflowID:     "",
			ApprovalInfo:   nil,
			CreatedBy:      "test_deprecate_user_001",
			CreatedAt:      now,
			UpdatedAt:      now,
		},
	}

	// 插入作废记录数据
	for _, record := range deprecationRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废记录失败: %w", err)
		}
	}

	// 5. 创建作废文档关系
	deprecationDocRelations := []mapper.DeprecationDocumentRelation{
		{
			DeprecationRecordID: deprecationRecordID,
			DocumentID:          "test_deprecate_internal_doc_001",
		},
	}

	// 插入作废文档关系数据
	for _, relation := range deprecationDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试作废文档关系失败: %w", err)
		}
	}

	return &TestDataForDeprecationApproval{
		Users:                           users,
		InternalDocuments:               internalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
		DeprecationRecords:              deprecationRecords,
		DeprecationDocumentRelations:    deprecationDocRelations,
		DeprecationRecordID:             deprecationRecordID,
		WorkflowID:                      "test_workflow_001",
	}, nil
}

// cleanupTestDataForDeprecationApproval 清理作废审批消费者测试数据
// 功能：删除测试过程中创建的所有数据，确保测试环境清洁
// 参数：
//   - svcCtx: 服务上下文
func cleanupTestDataForDeprecationApproval(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除作废文档关系记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_document_relations WHERE document_id LIKE '%test_deprecate_%'")

	// 删除作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_records WHERE id LIKE '%test_deprecate_%' OR created_by LIKE '%test_deprecate_%'")

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_deprecate_category_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_%").Delete(&mapper.User{})
}

// verifyDeprecationRecordStatus 验证作废记录状态
// 功能：直接查询数据库验证作废记录的审批状态是否正确更新
// 参数：
//   - svcCtx: 服务上下文
//   - deprecationID: 作废记录ID
//   - expectedStatus: 期望的审批状态
func verifyDeprecationRecordStatus(svcCtx *svc.ServiceContext, deprecationID string, expectedStatus int32) {
	ctx := context.Background()
	deprecationClient := mapper.NewDeprecationRecordClient(svcCtx.DocvaultDB)

	record, err := deprecationClient.GetByID(ctx, deprecationID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(record, convey.ShouldNotBeNil)
	convey.So(record.ApprovalStatus, convey.ShouldEqual, expectedStatus)
}

// verifyDeprecationRecordApprovalInfo 验证作废记录审批信息
// 功能：验证作废记录的审批信息是否正确保存
// 参数：
//   - svcCtx: 服务上下文
//   - deprecationID: 作废记录ID
func verifyDeprecationRecordApprovalInfo(svcCtx *svc.ServiceContext, deprecationID string) {
	ctx := context.Background()
	deprecationClient := mapper.NewDeprecationRecordClient(svcCtx.DocvaultDB)

	record, err := deprecationClient.GetByID(ctx, deprecationID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(record, convey.ShouldNotBeNil)

	// 验证审批信息不为空
	if record.ApprovalInfo != nil {
		approvalInfoBytes, err := record.ApprovalInfo.MarshalJSON()
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(approvalInfoBytes), convey.ShouldBeGreaterThan, 2) // 不只是 "{}"
	}
}

// verifyDeprecationRecordWorkflowID 验证作废记录工作流ID
// 功能：验证作废记录的工作流ID是否正确保存
// 参数：
//   - svcCtx: 服务上下文
//   - deprecationID: 作废记录ID
//   - expectedWorkflowID: 期望的工作流ID
func verifyDeprecationRecordWorkflowID(svcCtx *svc.ServiceContext, deprecationID string, expectedWorkflowID string) {
	ctx := context.Background()
	deprecationClient := mapper.NewDeprecationRecordClient(svcCtx.DocvaultDB)

	record, err := deprecationClient.GetByID(ctx, deprecationID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(record, convey.ShouldNotBeNil)
	convey.So(record.WorkflowID, convey.ShouldEqual, expectedWorkflowID)
}

// verifyDocumentStatus 验证文档状态
// 功能：直接查询内部文档库表验证文档状态是否正确更新
// 参数：
//   - svcCtx: 服务上下文
//   - documentID: 文档ID
//   - expectedStatus: 期望的文档状态
func verifyDocumentStatus(svcCtx *svc.ServiceContext, documentID string, expectedStatus int8) {
	ctx := context.Background()
	internalDocClient := mapper.NewInternalDocumentLibraryClient(svcCtx.DocvaultDB)

	document, err := internalDocClient.GetByID(ctx, documentID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(document, convey.ShouldNotBeNil)
	convey.So(document.Status, convey.ShouldEqual, expectedStatus)
}

// verifyDocumentsStatusByDeprecationRecord 验证作废记录关联的所有文档状态
// 功能：根据作废记录ID查询所有关联文档，并验证它们的状态
// 参数：
//   - svcCtx: 服务上下文
//   - deprecationRecordID: 作废记录ID
//   - expectedStatus: 期望的文档状态
func verifyDocumentsStatusByDeprecationRecord(svcCtx *svc.ServiceContext, deprecationRecordID string, expectedStatus int8) {
	ctx := context.Background()
	deprecationDocRelationClient := mapper.NewDeprecationDocumentRelationClient(svcCtx.DocvaultDB)

	// 查询作废记录关联的所有文档
	relations, err := deprecationDocRelationClient.GetByDeprecationRecordID(ctx, deprecationRecordID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(len(relations), convey.ShouldBeGreaterThan, 0)

	// 验证每个关联文档的状态
	for _, relation := range relations {
		verifyDocumentStatus(svcCtx, relation.DocumentID, expectedStatus)
	}
}
