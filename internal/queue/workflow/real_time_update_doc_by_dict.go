package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

type RealTimeUpdateBookByDictConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewRealTimeUpdateBookByDictConsumer(svcCtx *svc.ServiceContext) *RealTimeUpdateBookByDictConsumer {
	return &RealTimeUpdateBookByDictConsumer{
		svcCtx: svcCtx,
	}
}

func (h *RealTimeUpdateBookByDictConsumer) Handle(ctx context.Context, message []byte) error {
	var msg []BusinessDictionaryNodeRelation
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *RealTimeUpdateBookByDictConsumer) Name() string {
	return "business_dictionary_change"
}

func (h *RealTimeUpdateBookByDictConsumer) handleApproved(ctx context.Context, msg []BusinessDictionaryNodeRelation) {
	var businessDictionaryNodeRelation []*docvault.DictNodeInfo
	for _, v := range msg {
		businessDictionaryNodeRelation = append(businessDictionaryNodeRelation, &docvault.DictNodeInfo{
			DictNodeId: v.NodeID,
			Codes:      v.Codes,
			Names:      v.Names,
		})
	}
	// 更新图书
	_, err := docvault.NewBookClient(h.svcCtx.DocvaultRpcConn).UpdateBookNumberAndTypeByDictNodeId(ctx, &docvault.DictNodeIdReq{
		DictNodes: businessDictionaryNodeRelation,
	})
	if err != nil {
		logc.Errorf(ctx, "更新图书编号和类型失败: %v", err)
	}
	// 更新外部库
	_, err = docvault.NewExternalDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateByDictNodeId(ctx, &docvault.DictNodeIdReq{
		DictNodes: businessDictionaryNodeRelation,
	})
	if err != nil {
		logc.Errorf(ctx, "根据字典节点id更新外部库失败: %v", err)
	}
	// 更新内部库
	_, err = docvault.NewInternalDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateNumberByDictNodeId(ctx, &docvault.DictNodeIdReq{
		DictNodes: businessDictionaryNodeRelation,
	})
	if err != nil {
		logc.Errorf(ctx, "根据字典节点id更新内部库失败: %v", err)
	}
}

type BusinessDictionaryNodeRelation struct {
	NodeID       string `json:"node_id"`
	DictionaryID string `json:"dictionary_id"`
	Codes        string `json:"codes"`
	Names        string `json:"names"`
}
