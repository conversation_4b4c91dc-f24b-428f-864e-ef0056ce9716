package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

// BookReceiveApprovalConsumer 书籍借用消费者
type BookReceiveApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewBookReceiveApprovalConsumer(svcCtx *svc.ServiceContext) *BookReceiveApprovalConsumer {
	return &BookReceiveApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *BookReceiveApprovalConsumer) Name() string {
	return "file_book_collect"
}

// Handle 处理Kafka消息
// 参数:
//   - ctx: 上下文
//   - message: Kafka消息内容
//
// 返回值:
//   - error: 处理错误信息
//
// 功能: 解析Kafka消息并调用相应的处理方法
func (h *BookReceiveApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

// handleApproved 处理书籍领用审批事件
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 功能: 根据不同的事件类型处理书籍领用审批状态更新
func (h *BookReceiveApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	// 处理表单信息
	var bookReceive BookReceive
	if err := json.Unmarshal([]byte(msg.FormContent), &bookReceive); err != nil {
		logc.Errorf(ctx, "处理表单信息失败: %v", err)
		return
	}

	data := bookReceive.Data

	// 根据事件类型进行不同的处理
	switch msg.EventType {
	case consts.WorkflowEventRejected:
		// 审批驳回，更新状态为已驳回
		h.handleRejected(ctx, msg, data)

	case consts.WorkflowEventCanceled:
		// 审批撤销，更新状态为已撤销
		h.handleCanceled(ctx, msg, data)

	case consts.WorkflowEventPassed:
		// 审批通过，更新状态为已通过，保存审批信息，更新库存
		h.handlePassed(ctx, msg, data)

	default:
		logc.Errorf(ctx, "未知的工作流事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理审批驳回事件
// 功能: 将书籍领用记录状态更新为已驳回
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//   - data: 书籍领用信息
func (h *BookReceiveApprovalConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage, data BookReceiveInfo) {
	_, err := docvault.NewBookClient(h.svcCtx.DocvaultRpcConn).UpdateReceiveInfo(ctx, &docvault.UpdateBookReceiveReq{
		WorkflowId: msg.WorkflowID,
		BookId:     data.BookID,
		Status:     4, // 已驳回
	})
	if err != nil {
		logc.Errorf(ctx, "更新书籍领用信息失败: %v", err)
	}
}

// handleCanceled 处理审批撤销事件
// 功能: 将书籍领用记录状态更新为已撤销
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//   - data: 书籍领用信息
func (h *BookReceiveApprovalConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage, data BookReceiveInfo) {
	_, err := docvault.NewBookClient(h.svcCtx.DocvaultRpcConn).UpdateReceiveInfo(ctx, &docvault.UpdateBookReceiveReq{
		WorkflowId: msg.WorkflowID,
		BookId:     data.BookID,
		Status:     5, // 已撤销
	})
	if err != nil {
		logc.Errorf(ctx, "更新书籍领用信息失败: %v", err)
	}
}

// handlePassed 处理审批通过事件
// 功能: 将书籍领用记录状态更新为已通过，并保存审批信息
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//   - data: 书籍领用信息
func (h *BookReceiveApprovalConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage, data BookReceiveInfo) {
	// 获取审批人信息
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return
	}
	_, err = docvault.NewBookClient(h.svcCtx.DocvaultRpcConn).UpdateReceiveInfo(ctx, &docvault.UpdateBookReceiveReq{
		WorkflowId:   msg.WorkflowID,
		BookId:       data.BookID,
		Status:       3, // 已通过
		ApprovalInfo: approvalInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "更新书籍领用信息失败: %v", err)
	}
}

type BookReceive struct {
	Data BookReceiveInfo `json:"data"`
}

type BookReceiveInfo struct {
	BookID string `json:"bookId"`
	Reason string `json:"reason"`
}
