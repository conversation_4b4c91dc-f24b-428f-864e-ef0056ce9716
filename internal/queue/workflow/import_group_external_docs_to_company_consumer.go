package workflow

import (
	"context"
	"encoding/json"
	"errors"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type ImportGroupExternalDocsToCompanyApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewImportGroupExternalDocsToCompanyApprovalConsumer(svcCtx *svc.ServiceContext) *ImportGroupExternalDocsToCompanyApprovalConsumer {
	return &ImportGroupExternalDocsToCompanyApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) Name() string {
	return "import_group_external_docs_to_company_approval"
}

func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	if msg.EventType == consts.WorkflowEventRejected || msg.EventType == consts.WorkflowEventCanceled {
		err := h.toRpc(ctx, msg, 3)
		if err != nil {
			logc.Errorf(ctx, "处理审批失败: %v", err)
		}
	}
	if msg.EventType == consts.WorkflowEventPassed {
		err := h.toRpc(ctx, msg, 2)
		if err != nil {
			logc.Errorf(ctx, "处理审批失败: %v", err)
		}
	}

}

// toRpc 调用rpc 	incorporateStatus 导入状态 1 审批中 | 2已审批 | 3-驳回/撤销
func (h *ImportGroupExternalDocsToCompanyApprovalConsumer) toRpc(ctx context.Context, msg WorkflowEventMessage, incorporateStatus int32) error {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)
	orgInfo, err := h.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, msg.OrganizationID)
	if err != nil {
		logc.Errorf(ctx, "获取组织信息失败: %v", err)
		return errors.New("获取组织信息失败")
	}

	var data Data
	if err = json.Unmarshal([]byte(msg.FormContent), &data); err != nil {
		logc.Errorf(ctx, "处理表单信息失败: %v", err)
		return errors.New("处理表单信息失败")
	}
	// 获取审批人
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return err
	}

	var importGroupDocsToCompanyInfo []*docvault.ImportGroupDocsToCompanyInfo
	for _, v := range data.Data.Data {
		importGroupDocsToCompanyInfo = append(importGroupDocsToCompanyInfo, &docvault.ImportGroupDocsToCompanyInfo{
			Id:              v.ID,
			OriginalNumber:  v.OriginalNumber,
			OriginalVersion: v.OriginalVersion,
		})
	}
	_, err = docvault.NewExternalDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ImportGroupDocsToCompany(ctx, &docvault.ImportGroupDocsToCompanyReq{
		OrgCode:           orgInfo.Code,
		Data:              importGroupDocsToCompanyInfo,
		ApprovalInfo:      approvalInfo,
		IncorporateStatus: incorporateStatus,
		WorkFlowId:        msg.WorkflowID,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
		return err
	}
	return nil
}

type Data struct {
	Data ImportGroupExternalDocsToCompany `json:"data"`
}

type ImportGroupExternalDocsToCompany struct {
	Data []ImportGroupExternalDocsToCompanyInfo `json:"data"`
}

type ImportGroupExternalDocsToCompanyInfo struct {
	ID              string `json:"id"`
	OriginalNumber  string `json:"originalNumber"`
	OriginalVersion string `json:"originalVersion"`
}
