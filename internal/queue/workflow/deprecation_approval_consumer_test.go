package workflow

import (
	"context"
	"encoding/json"
	"testing"

	"nebula/internal/consts"

	. "github.com/smartystreets/goconvey/convey"
)

// TestDeprecationApprovalConsumer_Name 测试消费者名称
func TestDeprecationApprovalConsumer_Name(t *testing.T) {
	Convey("测试作废审批消费者名称", t, func() {
		consumer := &DeprecationApprovalConsumer{}
		So(consumer.Name(), ShouldEqual, "FILE_REPEAL")
	})
}

// TestDeprecationApprovalConsumer_Handle 测试消息处理
func TestDeprecationApprovalConsumer_Handle(t *testing.T) {
	Convey("测试作废审批消息处理", t, func() {
		consumer := &DeprecationApprovalConsumer{}

		Convey("测试无效JSON消息", func() {
			invalidJSON := []byte("invalid json")
			err := consumer.Handle(context.Background(), invalidJSON)
			So(err, ShouldNotBeNil)
		})

		<PERSON>vey("测试有效JSON消息", func() {
			// 构造测试消息
			msg := WorkflowEventMessage{
				TenantID:       "test_tenant",
				OrganizationID: "test_org",
				WorkflowID:     "test_workflow",
				SponsorID:      "test_sponsor",
				FormContent: `{
					"businessId": "FILE_REPEAL",
					"version": "1.0.0",
					"deprecateId": "test_deprecate_001",
					"data": {
						"applicant": "前端集团01",
						"applyDate": 1755155626820,
						"plannedDeprecateDate": 1755100800000,
						"documentModuleType": 2,
						"documentCategoryId": "29722844460704",
						"deprecateReason": 9,
						"otherReason": "其他原因，作废原因为9时为其他原因",
						"deprecateList": [
							{
								"id": "JEUsLj0B-E",
								"documentId": "29729096297504",
								"documentName": "新时代中国特色社会主义思想学习纲要",
								"documentNo": "ZYJT/-7",
								"documentVersionNo": "B/0",
								"_X_ROW_KEY": "row_46"
							}
						],
						"deprecateReasonName": "其他",
						"documentModuleName": "内部文件",
						"documentCategoryName": "内部文件3"
					}
				}`,
				CompletedAt:  1755155626820,
				CreatedAt:    1755155626820,
				BusinessID:   "FILE_REPEAL_001",
				BusinessCode: "FILE_REPEAL",
				EventType:    consts.WorkflowEventPassed,
			}

			msgBytes, err := json.Marshal(msg)
			So(err, ShouldBeNil)

			// 由于没有真实的服务上下文，这里只测试JSON解析部分
			// 实际的gRPC调用需要在集成测试中验证
			var parsedMsg WorkflowEventMessage
			err = json.Unmarshal(msgBytes, &parsedMsg)
			So(err, ShouldBeNil)
			So(parsedMsg.EventType, ShouldEqual, consts.WorkflowEventPassed)
			So(parsedMsg.BusinessCode, ShouldEqual, "FILE_REPEAL")

			// 测试提取deprecateId
			deprecateID, err := parsedMsg.GetParamOfFormContent([]string{"deprecateId"})
			So(err, ShouldBeNil)
			So(deprecateID, ShouldEqual, "test_deprecate_001")
		})
	})
}

// TestDeprecationApprovalConsumer_ExtractFormContent 测试表单内容提取
func TestDeprecationApprovalConsumer_ExtractFormContent(t *testing.T) {
	Convey("测试作废审批表单内容提取", t, func() {
		// 构造完整的formContent
		formContent := `{
			"businessId": "FILE_REPEAL",
			"version": "1.0.0",
			"deprecateId": "test_deprecate_001",
			"data": {
				"applicant": "前端集团01",
				"applyDate": 1755155626820,
				"plannedDeprecateDate": 1755100800000,
				"documentModuleType": 2,
				"documentCategoryId": "29722844460704",
				"deprecateReason": 9,
				"otherReason": "其他原因，作废原因为9时为其他原因",
				"deprecateList": [
					{
						"id": "JEUsLj0B-E",
						"documentId": "29729096297504",
						"documentName": "新时代中国特色社会主义思想学习纲要",
						"documentNo": "ZYJT/-7",
						"documentVersionNo": "B/0",
						"_X_ROW_KEY": "row_46"
					},
					{
						"id": "wS13vwvD4B",
						"documentId": "29810283434336",
						"documentName": "",
						"documentNo": "ZYJT/-4",
						"documentVersionNo": "A/0",
						"_X_ROW_KEY": "row_47"
					}
				],
				"deprecateReasonName": "其他",
				"documentModuleName": "内部文件",
				"documentCategoryName": "内部文件3"
			}
		}`

		msg := WorkflowEventMessage{
			FormContent: formContent,
		}

		Convey("提取作废记录ID", func() {
			deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
			So(err, ShouldBeNil)
			So(deprecateID, ShouldEqual, "test_deprecate_001")
		})

		Convey("提取业务ID", func() {
			businessID, err := msg.GetParamOfFormContent([]string{"businessId"})
			So(err, ShouldBeNil)
			So(businessID, ShouldEqual, "FILE_REPEAL")
		})

		Convey("提取申请人", func() {
			applicant, err := msg.GetParamOfFormContent([]string{"data", "applicant"})
			So(err, ShouldBeNil)
			So(applicant, ShouldEqual, "前端集团01")
		})

		Convey("提取作废原因", func() {
			reason, err := msg.GetParamOfFormContent([]string{"data", "deprecateReason"})
			So(err, ShouldBeNil)
			So(reason, ShouldEqual, 9)
		})

		Convey("提取作废文档列表", func() {
			deprecateList, err := msg.GetParamOfFormContent([]string{"data", "deprecateList"})
			So(err, ShouldBeNil)
			So(deprecateList, ShouldNotBeNil)

			// 验证是数组类型
			listArray, ok := deprecateList.([]interface{})
			So(ok, ShouldBeTrue)
			So(len(listArray), ShouldEqual, 2)

			// 验证第一个文档
			firstDoc, ok := listArray[0].(map[string]interface{})
			So(ok, ShouldBeTrue)
			So(firstDoc["documentId"], ShouldEqual, "29729096297504")
			So(firstDoc["documentNo"], ShouldEqual, "ZYJT/-7")
			So(firstDoc["documentVersionNo"], ShouldEqual, "B/0")
		})

		Convey("提取不存在的字段", func() {
			_, err := msg.GetParamOfFormContent([]string{"nonexistent"})
			So(err, ShouldNotBeNil)
		})
	})
}

// TestDeprecationApprovalConsumer_EventTypes 测试不同事件类型的处理
func TestDeprecationApprovalConsumer_EventTypes(t *testing.T) {
	Convey("测试不同事件类型的处理逻辑", t, func() {

		testCases := []struct {
			eventType string
			desc      string
		}{
			{consts.WorkflowEventPassed, "审批通过"},
			{consts.WorkflowEventRejected, "审批驳回"},
			{consts.WorkflowEventCanceled, "审批撤销"},
			{consts.WorkflowEventStartFailed, "启动失败"},
			{consts.WorkflowEventStartSuccess, "启动成功"},
		}

		for _, tc := range testCases {
			Convey("测试"+tc.desc+"事件", func() {
				msg := WorkflowEventMessage{
					EventType: tc.eventType,
					FormContent: `{
						"deprecateId": "test_deprecate_001"
					}`,
				}

				// 由于没有真实的服务上下文，这里只验证事件类型识别
				// 实际的处理逻辑需要在集成测试中验证
				So(msg.EventType, ShouldEqual, tc.eventType)

				// 验证能够正确提取deprecateId
				deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
				So(err, ShouldBeNil)
				So(deprecateID, ShouldEqual, "test_deprecate_001")
			})
		}
	})
}
