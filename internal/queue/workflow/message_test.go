package workflow

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// TestWorkflowEventMessage_GetParamOfFormContent 测试GetParamOfFormContent方法
func TestWorkflowEventMessage_GetParamOfFormContent(t *testing.T) {
	Convey("测试GetParamOfFormContent方法", t, func() {
		// 准备测试数据 - 复杂嵌套JSON结构
		complexJSON := `{
			"businessId": "FILE_BORROW",
			"borrowRecordId": "29794932326624",
			"data": {
				"borrowReason": "工作需要",
				"otherReason": "",
				"borrowPeriod": ["2024-01-01", "2024-01-31"],
				"nested": {
					"level2": {
						"level3": "深层嵌套值"
					}
				},
				"numberValue": 12345,
				"boolValue": true
			}
		}`

		msg := &WorkflowEventMessage{
			FormContent: complexJSON,
		}

		<PERSON><PERSON>("正常情况", func() {
			<PERSON>vey("获取顶级字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "29794932326624")
			})

			Convey("获取顶级业务ID字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"businessId"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "FILE_BORROW")
			})

			Convey("获取嵌套字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "borrowReason"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "工作需要")
			})

			Convey("获取深层嵌套字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "nested", "level2", "level3"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "深层嵌套值")
			})

			Convey("获取空字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "otherReason"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "")
			})

			Convey("获取数字字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "numberValue"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, 12345)
			})

			Convey("获取布尔字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "boolValue"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, true)
			})

			Convey("获取数组字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "borrowPeriod"})
				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				// 验证是数组类型
				if arr, ok := result.([]any); ok {
					So(len(arr), ShouldEqual, 2)
					So(arr[0], ShouldEqual, "2024-01-01")
					So(arr[1], ShouldEqual, "2024-01-31")
				}
			})

			Convey("获取对象字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data"})
				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				// 验证是对象类型
				if obj, ok := result.(map[string]any); ok {
					So(obj["borrowReason"], ShouldEqual, "工作需要")
					So(obj["numberValue"], ShouldEqual, 12345)
				}
			})
		})

		Convey("异常情况", func() {
			Convey("空FormContent", func() {
				emptyMsg := &WorkflowEventMessage{FormContent: ""}
				result, err := emptyMsg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("无效JSON格式", func() {
				invalidMsg := &WorkflowEventMessage{FormContent: "{invalid json}"}
				result, err := invalidMsg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("不存在的字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"nonExistentField"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("不存在的嵌套字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "nonExistentField"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("空路径数组", func() {
				result, err := msg.GetParamOfFormContent([]string{})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})
		})
	})
}

// TestWorkflowEventMessage_GetParamOfFormContent_SimpleJSON 测试简单JSON结构
func TestWorkflowEventMessage_GetParamOfFormContent_SimpleJSON(t *testing.T) {
	Convey("测试简单JSON结构", t, func() {
		// 简单JSON结构
		simpleJSON := `{"borrowRecordId": "12345", "amount": 100}`
		msg := &WorkflowEventMessage{FormContent: simpleJSON}

		Convey("获取字符串值", func() {
			result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "12345")
		})

		Convey("获取数字值", func() {
			result, err := msg.GetParamOfFormContent([]string{"amount"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, 100)
		})
	})
}

// TestWorkflowEventMessage_GetParamOfFormContent_EdgeCases 测试边界情况
func TestWorkflowEventMessage_GetParamOfFormContent_EdgeCases(t *testing.T) {
	Convey("测试边界情况", t, func() {
		Convey("null值", func() {
			nullJSON := `{"borrowRecordId": null}`
			msg := &WorkflowEventMessage{FormContent: nullJSON}
			result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("零值", func() {
			zeroJSON := `{"count": 0, "flag": false, "text": ""}`
			msg := &WorkflowEventMessage{FormContent: zeroJSON}

			// 测试数字零值
			result, err := msg.GetParamOfFormContent([]string{"count"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, 0)

			// 测试布尔false值
			result, err = msg.GetParamOfFormContent([]string{"flag"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, false)

			// 测试空字符串
			result, err = msg.GetParamOfFormContent([]string{"text"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "")
		})

		Convey("特殊字符", func() {
			specialJSON := `{"special": "包含中文和特殊字符!@#$%^&*()"}`
			msg := &WorkflowEventMessage{FormContent: specialJSON}
			result, err := msg.GetParamOfFormContent([]string{"special"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "包含中文和特殊字符!@#$%^&*()")
		})
	})
}

// TestWorkflowEventMessage_GetParamOfFormContent_RecycleData 测试用户提供的recycleData JSON结构
func TestWorkflowEventMessage_GetParamOfFormContent_RecycleData(t *testing.T) {
	Convey("测试recycleData JSON结构提取", t, func() {
		// 用户提供的JSON结构
		recycleDataJSON := `{"businessId": "FILE_BORROW", "version": "1.0.0", "recycleData": [{"documentId": "xxxx", "documentVersionNo": "111"}]}`

		msg := &WorkflowEventMessage{
			FormContent: recycleDataJSON,
		}

		Convey("提取recycleData数组", func() {
			// 测试提取recycleData数组
			result, err := msg.GetParamOfFormContent([]string{"recycleData"})
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			// 验证返回的是数组类型
			recycleDataArray, ok := result.([]any)
			So(ok, ShouldBeTrue)
			So(len(recycleDataArray), ShouldEqual, 1)

			// 验证数组中第一个元素的结构
			firstItem, ok := recycleDataArray[0].(map[string]any)
			So(ok, ShouldBeTrue)
			So(firstItem["documentId"], ShouldEqual, "xxxx")
			So(firstItem["documentVersionNo"], ShouldEqual, "111")
		})

		Convey("提取其他顶级字段", func() {
			// 测试提取businessId
			result, err := msg.GetParamOfFormContent([]string{"businessId"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "FILE_BORROW")

			// 测试提取version
			result, err = msg.GetParamOfFormContent([]string{"version"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "1.0.0")
		})

		Convey("验证recycleData数组内容的完整性", func() {
			// 获取recycleData数组
			result, err := msg.GetParamOfFormContent([]string{"recycleData"})
			So(err, ShouldBeNil)

			// 将结果转换为数组并验证内容
			if recycleDataArray, ok := result.([]any); ok {
				So(len(recycleDataArray), ShouldEqual, 1)

				// 验证第一个元素包含期望的字段
				if firstItem, ok := recycleDataArray[0].(map[string]any); ok {
					So(firstItem, ShouldContainKey, "documentId")
					So(firstItem, ShouldContainKey, "documentVersionNo")
					So(firstItem["documentId"], ShouldEqual, "xxxx")
					So(firstItem["documentVersionNo"], ShouldEqual, "111")
				}
			}
		})
	})
}

// TestGetParamOfFormContentTyped 测试泛型函数
func TestGetParamOfFormContentTyped(t *testing.T) {
	Convey("测试GetParamOfFormContentTyped泛型函数", t, func() {
		// 准备测试数据
		formContent := `{
			"borrowRecordId": "record123",
			"businessId": "biz789",
			"version": "1.0",
			"recycleData": [
				{"documentId": "doc123", "documentVersionNo": "v1.0"},
				{"documentId": "doc456", "documentVersionNo": "v2.0"}
			],
			"count": 42,
			"isActive": true,
			"nested": {
				"level1": {
					"level2": "deep_value"
				}
			}
		}`

		msg := &WorkflowEventMessage{
			FormContent: formContent,
		}

		Convey("提取字符串类型", func() {
			// 测试提取字符串类型
			borrowRecordId, err := GetParamOfFormContentTyped[string](msg, []string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(borrowRecordId, ShouldEqual, "record123")

			// 测试提取嵌套字符串
			deepValue, err := GetParamOfFormContentTyped[string](msg, []string{"nested", "level1", "level2"})
			So(err, ShouldBeNil)
			So(deepValue, ShouldEqual, "deep_value")
		})

		Convey("提取数组类型", func() {
			// 测试提取数组类型
			recycleData, err := GetParamOfFormContentTyped[[]any](msg, []string{"recycleData"})
			So(err, ShouldBeNil)
			So(len(recycleData), ShouldEqual, 2)

			// 验证数组内容
			firstItem, ok := recycleData[0].(map[string]any)
			So(ok, ShouldBeTrue)
			So(firstItem["documentId"], ShouldEqual, "doc123")
		})

		Convey("提取数值类型", func() {
			// 测试提取数值类型（JSON中的数字会被解析为float64）
			count, err := GetParamOfFormContentTyped[float64](msg, []string{"count"})
			So(err, ShouldBeNil)
			So(count, ShouldEqual, 42.0)
		})

		Convey("提取布尔类型", func() {
			// 测试提取布尔类型
			isActive, err := GetParamOfFormContentTyped[bool](msg, []string{"isActive"})
			So(err, ShouldBeNil)
			So(isActive, ShouldBeTrue)
		})

		Convey("提取map类型", func() {
			// 测试提取map类型
			nested, err := GetParamOfFormContentTyped[map[string]any](msg, []string{"nested"})
			So(err, ShouldBeNil)
			So(nested, ShouldNotBeNil)
			So(nested["level1"], ShouldNotBeNil)
		})

		Convey("类型转换错误测试", func() {
			// 尝试将字符串转换为数值类型，应该失败
			_, err := GetParamOfFormContentTyped[int](msg, []string{"borrowRecordId"})
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "反序列化为目标类型")
		})

		Convey("路径不存在测试", func() {
			// 测试不存在的路径
			_, err := GetParamOfFormContentTyped[string](msg, []string{"nonExistentField"})
			So(err, ShouldNotBeNil)
		})

		Convey("空路径测试", func() {
			// 测试空路径
			_, err := GetParamOfFormContentTyped[string](msg, []string{})
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "jsonPath不能为空")
		})
	})
}

// TestRecycleDataParsing 测试回收数据解析功能
// 功能：验证GetParamOfFormContentTyped函数能否正确解析recycleData数组
func TestRecycleDataParsing(t *testing.T) {
	Convey("测试回收数据解析", t, func() {
		// 准备测试数据 - 模拟用户提供的JSON数据
		msg := &WorkflowEventMessage{
			FormContent: `{"data": {"reason": 5, "documents": [{"id": "GakAvpJINv8uTGxnHPa7a", "_X_ROW_KEY": "row_341", "documentId": "浙0291行初14号", "documentNo": "1001/-1", "borrowStatus": 1, "documentName": "浙0291行初14号", "documentValidity": 3, "documentVersionNo": "A/0", "documentCategoryId": "内部文件3", "documentModuleName": "内部文档", "documentModuleType": 2, "documentCategoryName": "内部文件3"}], "otherReason": "32323", "borrowPeriod": [1759161600000, 1759593600000], "userNickname": "前端集团02", "approvalApplyTime": "2025-08-07"}, "version": "1.0.0", "businessId": "FILE_BORROW_RECLAIM", "recycleData": [{"documentId": "29729096297504", "documentVersionNo": "A/0"}], "borrowRecordId": "29805927697760"}`,
		}

		Convey("应该能够成功解析borrowRecordId", func() {
			// 测试提取borrowRecordId
			borrowRecordID, err := GetParamOfFormContentTyped[string](msg, []string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(borrowRecordID, ShouldEqual, "29805927697760")
		})

		Convey("应该能够成功解析recycleData数组", func() {
			// 测试提取recycleData
			documentItems, err := GetParamOfFormContentTyped[[]documentItem](msg, []string{"recycleData"})
			So(err, ShouldBeNil)
			So(len(documentItems), ShouldEqual, 1)
			So(documentItems[0].DocumentId, ShouldEqual, "29729096297504")
			So(documentItems[0].VersionNo, ShouldEqual, "A/0")
		})

		Convey("应该能够处理空的recycleData数组", func() {
			// 测试空数组情况
			emptyMsg := &WorkflowEventMessage{
				FormContent: `{"recycleData": [], "borrowRecordId": "test123"}`,
			}
			documentItems, err := GetParamOfFormContentTyped[[]documentItem](emptyMsg, []string{"recycleData"})
			So(err, ShouldBeNil)
			So(len(documentItems), ShouldEqual, 0)
		})

		Convey("应该能够处理多个文档项的recycleData数组", func() {
			// 测试多个文档项
			multiMsg := &WorkflowEventMessage{
				FormContent: `{"recycleData": [{"documentId": "doc1", "documentVersionNo": "v1.0"}, {"documentId": "doc2", "documentVersionNo": "v2.0"}], "borrowRecordId": "test123"}`,
			}
			documentItems, err := GetParamOfFormContentTyped[[]documentItem](multiMsg, []string{"recycleData"})
			So(err, ShouldBeNil)
			So(len(documentItems), ShouldEqual, 2)
			So(documentItems[0].DocumentId, ShouldEqual, "doc1")
			So(documentItems[0].VersionNo, ShouldEqual, "v1.0")
			So(documentItems[1].DocumentId, ShouldEqual, "doc2")
			So(documentItems[1].VersionNo, ShouldEqual, "v2.0")
		})
	})
}

// TestGetParamOfFormContent_Standard 标准测试（不使用convey）
func TestGetParamOfFormContent_Standard(t *testing.T) {
	// 简单JSON结构
	simpleJSON := `{"borrowRecordId": "12345"}`
	msg := &WorkflowEventMessage{FormContent: simpleJSON}

	// 测试获取字符串值
	result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result != "12345" {
		t.Errorf("Expected '12345', got: %v", result)
	}

	// 测试错误情况
	emptyMsg := &WorkflowEventMessage{FormContent: ""}
	_, err = emptyMsg.GetParamOfFormContent([]string{"borrowRecordId"})
	if err == nil {
		t.Error("Expected error for empty FormContent, got nil")
	}
}
