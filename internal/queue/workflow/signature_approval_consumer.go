package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/domain/aggregate"
	"nebula/internal/domain/factory"

	"github.com/zeromicro/go-zero/core/logc"
)

// SignatureApproval handles signature approval messages.
type SignatureApprovalConsumer struct {
	factory factory.Factory
}

// NewSignatureApproval creates a new SignatureApproval.
func NewSignatureApprovalConsumer(factory factory.Factory) *SignatureApprovalConsumer {
	return &SignatureApprovalConsumer{
		factory: factory,
	}
}

// Handle processes the incoming Kafka message.
func (h *SignatureApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *SignatureApprovalConsumer) Name() string {
	return "signature_approval"
}

func (h *SignatureApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	if msg.EventType != consts.WorkflowEventPassed {
		return
	}
	err := h.factory.SignatureService().ApprovalCompleted(ctx, aggregate.ApprovalResultMsg{
		TenantID:       msg.TenantID,
		OrganizationID: msg.OrganizationID,
		WorkflowID:     msg.WorkflowID,
		SponsorID:      msg.SponsorID,
		FormContent:    msg.FormContent,
		CompletedAt:    msg.CompletedAt,
		CreatedAt:      msg.CreatedAt,
		BusinessID:     msg.BusinessID,
		BusinessCode:   msg.BusinessCode,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
	}
}
