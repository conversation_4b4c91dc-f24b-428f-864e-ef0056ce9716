package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

// DeprecationApprovalConsumer 作废审批消费者
type DeprecationApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewDeprecationApprovalConsumer(svcCtx *svc.ServiceContext) *DeprecationApprovalConsumer {
	return &DeprecationApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *DeprecationApprovalConsumer) Name() string {
	return "FILE_REPEAL"
}

func (h *DeprecationApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

// handleApproved 处理作废审批事件
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 功能: 根据不同的事件类型处理作废审批状态更新
func (h *DeprecationApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 实现步骤:
	// 1. 根据事件类型进行不同的处理
	// 2. 审批驳回时更新状态为已驳回
	// 3. 审批撤销时更新状态为待提交
	// 4. 审批通过时处理通过逻辑

	switch msg.EventType {
	case consts.WorkflowEventRejected:
		// 审批驳回，更新状态为已驳回
		h.handleRejected(ctx, msg)

	case consts.WorkflowEventCanceled, consts.WorkflowEventStartFailed:
		// 审批撤销或启动失败，更新状态为待提交
		h.handleCanceled(ctx, msg)

	case consts.WorkflowEventStartSuccess:
		// 审批启动成功，更新状态为审批中
		h.handleStartSuccess(ctx, msg)

	case consts.WorkflowEventPassed:
		// 审批通过，更新状态为已审批
		h.handlePassed(ctx, msg)

	default:
		// 未知事件类型，记录日志
		logc.Errorf(ctx, "未知的工作流事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理审批驳回事件
// 功能: 将作废记录状态更新为已驳回
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *DeprecationApprovalConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage) {
	// 调用docvault服务更新作废记录状态为已驳回(4)
	deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
	if err != nil {
		logc.Errorf(ctx, "提取作废记录ID失败: %v", err)
		return
	}
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyDeprecationApprovalStatus(ctx, &docvault.ModifyDeprecationApprovalStatusReq{
		DeprecationRecordId: deprecateID.(string),
		ApprovalStatus:      4, // 已驳回
	})
	if err != nil {
		logc.Errorf(ctx, "处理作废审批驳回失败: %v", err)
		return
	}
	logc.Infof(ctx, "作废记录 %s 审批驳回处理完成", deprecateID)
}

// handleCanceled 处理审批撤销事件
// 功能: 将作废记录状态更新为待审批
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *DeprecationApprovalConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage) {
	// 调用docvault服务更新作废记录状态为暂存/待提交(1)
	deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
	if err != nil {
		logc.Errorf(ctx, "提取作废记录ID失败: %v", err)
		return
	}
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyDeprecationApprovalStatus(ctx, &docvault.ModifyDeprecationApprovalStatusReq{
		DeprecationRecordId: deprecateID.(string),
		ApprovalStatus:      1, // 暂存/待提交
	})
	if err != nil {
		logc.Errorf(ctx, "处理作废审批撤销失败: %v", err)
		return
	}
	logc.Infof(ctx, "作废记录 %s 审批撤销处理完成", msg.BusinessID)
}

// handlePassed 处理审批通过事件
// 功能: 将作废记录状态更新为已审批，并保存审批信息
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *DeprecationApprovalConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage) {
	// 获取审核人和审批人
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return
	}

	// 调用docvault服务更新作废记录状态为已审批(3)并保存审批信息
	deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
	if err != nil {
		logc.Errorf(ctx, "提取作废记录ID失败: %v", err)
		return
	}
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyDeprecationApprovalStatus(ctx, &docvault.ModifyDeprecationApprovalStatusReq{
		DeprecationRecordId: deprecateID.(string),
		ApprovalStatus:      3, // 已审批
		ApprovalInfo:        approvalInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "处理作废审批通过失败: %v", err)
		return
	}
	logc.Infof(ctx, "作废记录 %s 审批通过处理完成", msg.BusinessID)
}

// handleStartSuccess 处理审批启动成功事件
// 功能: 将作废记录状态更新为审批中
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *DeprecationApprovalConsumer) handleStartSuccess(ctx context.Context, msg WorkflowEventMessage) {
	// 调用docvault服务更新作废记录状态为审批中(2)
	deprecateID, err := msg.GetParamOfFormContent([]string{"deprecateId"})
	if err != nil {
		logc.Errorf(ctx, "提取作废记录ID失败: %v", err)
		return
	}
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyDeprecationApprovalStatus(ctx, &docvault.ModifyDeprecationApprovalStatusReq{
		DeprecationRecordId: deprecateID.(string),
		ApprovalStatus:      2, // 审批中
		WorkflowId:          msg.WorkflowID,
	})
	if err != nil {
		logc.Errorf(ctx, "处理作废审批启动成功失败: %v", err)
		return
	}
	logc.Infof(ctx, "作废记录 %s 审批启动成功处理完成", deprecateID)
}
