package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

// BorrowRetrieveApprovalConsumer 借阅后的回收审批
type BorrowRetrieveApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewBorrowRetrieveApprovalConsumer(svcCtx *svc.ServiceContext) *BorrowRetrieveApprovalConsumer {
	return &BorrowRetrieveApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *BorrowRetrieveApprovalConsumer) Name() string {
	return "file_borrow_reclaim"
}

func (h *BorrowRetrieveApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleRetrieve(ctx, msg)
	return nil
}

// handleRetrieve 处理文件回收审批事件
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 功能: 根据不同的事件类型处理文件回收审批状态更新
func (h *BorrowRetrieveApprovalConsumer) handleRetrieve(ctx context.Context, msg WorkflowEventMessage) {
	// 实现步骤:
	// 1. 根据事件类型进行不同的处理
	// 2. 审批驳回时更新状态为已驳回
	// 3. 审批撤销时更新状态为待提交
	// 4. 审批通过时处理通过逻辑
	// 5. 工作流启动成功时更新状态为审批中
	// 6. 工作流启动失败时更新状态为借阅中

	switch msg.EventType {
	case consts.WorkflowEventRejected:
		// 审批驳回，更新状态为借阅中
		h.handleRejected(ctx, msg)

	case consts.WorkflowEventCanceled, consts.WorkflowEventStartFailed:
		// 审批撤销或启动失败，更新状态为借阅中
		h.handleCanceled(ctx, msg)

	case consts.WorkflowEventStartSuccess:
		// 审批启动成功，更新状态为回收中
		h.handleStartSuccess(ctx, msg)

	case consts.WorkflowEventPassed:
		// 审批通过，更新状态为已回收
		h.handlePassed(ctx, msg)

	default:
		// 未知事件类型，记录日志
		logc.Errorf(ctx, "未知的工作流事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理回收审批驳回事件
// 功能: 将借阅记录回收状态更新为借阅中
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowRetrieveApprovalConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage) {
	// 提取借阅记录数据
	borrowRecordID, documentWithVersions, err := h.extractBorrowRecordData(ctx, msg)
	if err != nil {
		return
	}

	// 调用docvault服务更新借阅记录回收状态为已驳回(4)
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowRecordId: borrowRecordID, // 现在是类型安全的string，无需类型断言
		BorrowStatus:   1,              // 借阅中
		Documents:      documentWithVersions,
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批驳回失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批驳回处理完成", borrowRecordID)
}

// handleCanceled 处理回收审批撤销事件
// 功能: 将借阅记录回收状态更新为借阅中
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowRetrieveApprovalConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 提取借阅记录数据
	borrowRecordID, documentWithVersions, err := h.extractBorrowRecordData(ctx, msg)
	if err != nil {
		return
	}

	// 2. 调用docvault服务更新借阅记录回收状态为借阅中(1)
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowRecordId: borrowRecordID,
		BorrowStatus:   1, // 借阅中
		Documents:      documentWithVersions,
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批撤销失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批撤销处理完成", borrowRecordID)
}

// handlePassed 处理回收审批通过事件
// 功能: 将借阅记录回收状态更新为已回收，并保存审批信息
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowRetrieveApprovalConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 获取审批信息
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		logc.Errorf(ctx, "获取回收审批信息失败: %v", err)
		return
	}

	// 2. 提取借阅记录数据
	borrowRecordID, documentWithVersions, err := h.extractBorrowRecordData(ctx, msg)
	if err != nil {
		return
	}

	// 3. 调用docvault服务更新借阅记录回收状态为已回收(3)
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowRecordId: borrowRecordID,
		BorrowStatus:   3, // 已回收
		Documents:      documentWithVersions,
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批通过失败: %v", err)
		return
	}

	// 4. 保存回收审批信息
	recycleApprovalInfo := &docvault.RecycleApprovalInfo{
		DistributeId: borrowRecordID,
		ApprovalInfo: approvalInfo,
	}
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).SaveRecycleApprovalInfo(ctx, recycleApprovalInfo)
	if err != nil {
		logc.Errorf(ctx, "保存回收审批信息失败: %v", err)
		return
	}

	logc.Infof(ctx, "文件回收记录 %s 审批通过处理完成", borrowRecordID)
}

// handleStartSuccess 处理回收审批启动成功事件
// 功能: 将借阅记录回收状态更新为回收中
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowRetrieveApprovalConsumer) handleStartSuccess(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 提取借阅记录数据
	borrowRecordID, documentWithVersions, err := h.extractBorrowRecordData(ctx, msg)
	if err != nil {
		return
	}

	// 2. 调用docvault服务更新借阅记录回收状态为回收中(2)
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowRecordId: borrowRecordID,
		BorrowStatus:   2, // 回收中
		Documents:      documentWithVersions,
		RecoverId:      msg.SponsorID,
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批启动成功失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批启动成功处理完成", borrowRecordID)
}

// documentItem 文档项结构体
// 用于表示回收数据中的单个文档信息
type documentItem struct {
	DocumentId string `json:"documentId"`        // 文档ID
	VersionNo  string `json:"documentVersionNo"` // 文档版本号
}

// extractBorrowRecordData 提取借阅记录数据
// 功能: 从工作流消息中提取borrowRecordId和recycleData，并转换为docvault所需的格式
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 返回值:
//   - borrowRecordID: 借阅记录ID
//   - documentWithVersions: 文档版本信息列表
//   - error: 错误信息
func (h *BorrowRetrieveApprovalConsumer) extractBorrowRecordData(ctx context.Context, msg WorkflowEventMessage) (string, []*docvault.DocumentWithVersion, error) {
	// 实现步骤:
	// 1. 提取borrowRecordId参数
	// 2. 提取recycleData参数
	// 3. 将documentItem转换为docvault.DocumentWithVersion格式

	// 提取borrowRecordId参数
	borrowRecordID, err := GetParamOfFormContentTyped[string](&msg, []string{"borrowRecordId"})
	if err != nil {
		logc.Errorf(ctx, "提取借阅记录ID失败: %v", err)
		return "", nil, err
	}

	// 提取recycleData参数
	documentItems, err := GetParamOfFormContentTyped[[]documentItem](&msg, []string{"recycleData"})
	if err != nil {
		logc.Errorf(ctx, "提取回收数据失败: %v", err)
		return "", nil, err
	}

	// 转换为docvault.DocumentWithVersion格式
	var documentWithVersions []*docvault.DocumentWithVersion
	for _, v := range documentItems {
		documentWithVersions = append(documentWithVersions, &docvault.DocumentWithVersion{
			DocumentId: v.DocumentId,
			VersionNo:  v.VersionNo,
		})
	}

	return borrowRecordID, documentWithVersions, nil
}
