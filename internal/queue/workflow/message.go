package workflow

import (
	"fmt"

	"github.com/bytedance/sonic"
)

type WorkflowEventMessage struct {
	TenantID       string `json:"tenant_id"`       // 租户ID
	OrganizationID string `json:"organization_id"` // 组织架构ID
	WorkflowID     string `json:"workflow_id"`     // 工作流ID
	SponsorID      string `json:"sponsor_id"`      // 发起人ID
	FormContent    string `json:"form_content"`    // 审批表单信息（JSON字符串）
	CompletedAt    int64  `json:"completed_at"`    // 完成时间（毫秒级时间戳）
	CreatedAt      int64  `json:"created_at"`      // 发起时间（毫秒级时间戳）
	BusinessID     string `json:"business_id"`     // 流程序列号
	BusinessCode   string `json:"business_code"`   // 业务代码
	EventType      string `json:"event_type"`      // 事件类型
}

// GetParamOfFormContent 从FormContent JSON字符串中根据jsonPath提取指定字段的值
// 功能：使用sonic库的GetFromString方法解析JSON路径，支持多级嵌套路径查询
// 参数：
//   - jsonPath: JSON路径数组，如 ["borrowRecordId"] 或 ["data", "borrowReason"]
//
// 返回值：
//   - any: 提取到的值，可能是字符串、数字、布尔值、对象或数组
//   - error: 解析错误，当JSON格式无效或路径不存在时返回
//
// 示例：
//   - 获取顶级字段：{"borrowRecordId":"1"},GetParamOfFormContent(["borrowRecordId"])
//   - 获取嵌套字段：{"data":{"borrowReason":"1"}},GetParamOfFormContent(["data", "borrowReason"])
//
// GetParamOfFormContent 从FormContent中提取指定路径的参数值
// 功能：使用sonic库解析JSON字符串，根据提供的路径提取对应的值
// 参数：
//   - jsonPath: JSON路径数组，用于定位要提取的字段
//
// 返回值：
//   - any: 提取到的值
//   - error: 解析过程中的错误
func (w *WorkflowEventMessage) GetParamOfFormContent(jsonPath []string) (any, error) {
	// 1. 检查路径是否为空
	if len(jsonPath) == 0 {
		return nil, fmt.Errorf("jsonPath不能为空")
	}

	// 2. 将字符串切片转换为interface{}切片
	paths := make([]any, len(jsonPath))
	for i, path := range jsonPath {
		paths[i] = path
	}

	// 3. 使用sonic库解析JSON路径，获取对应的AST节点
	node, err := sonic.GetFromString(w.FormContent, paths...)
	if err != nil {
		return nil, err
	}

	// 4. 将AST节点转换为Go原生类型并返回
	return node.Interface()
}

// GetParamOfFormContentTyped 从WorkflowEventMessage的FormContent中提取指定路径的参数值并转换为指定类型
// 功能：使用sonic库解析JSON字符串，根据提供的路径提取对应的值，并转换为泛型指定的类型
// 参数：
//   - msg: WorkflowEventMessage实例
//   - jsonPath: JSON路径数组，用于定位要提取的字段
//
// 返回值：
//   - T: 转换后的指定类型值
//   - error: 解析或类型转换过程中的错误
//
// 使用示例：
//   - 获取字符串类型：borrowRecordId, err := GetParamOfFormContentTyped[string](msg, []string{"borrowRecordId"})
//   - 获取切片类型：recycleData, err := GetParamOfFormContentTyped[[]documentItem](msg, []string{"recycleData"})
func GetParamOfFormContentTyped[T any](msg *WorkflowEventMessage, jsonPath []string) (T, error) {
	// 1. 声明零值用于错误返回
	var zero T

	// 2. 检查路径是否为空
	if len(jsonPath) == 0 {
		return zero, fmt.Errorf("jsonPath不能为空")
	}

	// 3. 将字符串切片转换为interface{}切片
	paths := make([]any, len(jsonPath))
	for i, path := range jsonPath {
		paths[i] = path
	}

	// 4. 使用sonic库解析JSON路径，获取对应的AST节点
	node, err := sonic.GetFromString(msg.FormContent, paths...)
	if err != nil {
		return zero, err
	}

	// 5. 获取节点的原始JSON字符串数据
	jsonStr, err := node.Raw()
	if err != nil {
		return zero, fmt.Errorf("获取JSON原始数据失败: %v", err)
	}

	// 6. 使用sonic.Unmarshal进行类型安全的反序列化
	var result T
	err = sonic.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return zero, fmt.Errorf("反序列化为目标类型 %T 失败: %v", zero, err)
	}

	return result, nil
}
